{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin, fetchStudentClassTuitionsByMonthAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage, setSearch, resetFilters } from \"src/features/filter/filterSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionStatistics$to, _tuitionStatistics$to2, _tuitionStatistics$to3, _tuitionStatistics$to4, _tuitionStatistics$mo4, _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    currentPage,\n    totalPages,\n    totalItems,\n    limit\n  } = useSelector(state => state.filter);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho bộ lọc\n  const [filterMonth, setFilterMonth] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"\");\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\n  const [filterClass, setFilterClass] = useState(\"\");\n  const [filterClassId, setFilterClassId] = useState(\"\");\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addExpectedAmount, setAddExpectedAmount] = useState(\"\");\n  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState(\"\");\n  const [addPaidAmount, setAddPaidAmount] = useState(\"\");\n  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addStatus, setAddStatus] = useState(\"\");\n  const [addNote, setAddNote] = useState(\"\");\n  const [addCalculateExpected, setAddCalculateExpected] = useState(false);\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editExpectedAmount, setEditExpectedAmount] = useState(\"\");\n  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState(\"\");\n  const [editPaidAmount, setEditPaidAmount] = useState(\"\");\n  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState(\"\");\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editStatus, setEditStatus] = useState(\"\");\n  const [editNote, setEditNote] = useState(\"\");\n  const [calculateExpected, setCalculateExpected] = useState(false);\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n  useEffect(() => {\n    if (!didInit) {\n      dispatch(resetFilters());\n      setDidInit(true);\n    }\n  }, [dispatch, didInit]);\n\n  // Hiệu ứng để vẽ biểu đồ khi có dữ liệu thống kê\n  useEffect(() => {\n    if (viewMode === 'statistics' && tuitionStatistics && monthlyChartRef.current && classChartRef.current) {\n      var _tuitionStatistics$mo, _tuitionStatistics$mo2, _tuitionStatistics$mo3, _tuitionStatistics$cl, _tuitionStatistics$cl2, _tuitionStatistics$cl3;\n      // Hủy biểu đồ cũ nếu có\n      if (monthlyChartInstance.current) {\n        monthlyChartInstance.current.destroy();\n      }\n      if (classChartInstance.current) {\n        classChartInstance.current.destroy();\n      }\n\n      // Chuẩn bị dữ liệu cho biểu đồ theo tháng\n      const monthlyData = {\n        labels: ((_tuitionStatistics$mo = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo === void 0 ? void 0 : _tuitionStatistics$mo.map(stat => stat.monthFormatted)) || [],\n        datasets: [{\n          label: 'Số tiền cần thu',\n          data: ((_tuitionStatistics$mo2 = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo2 === void 0 ? void 0 : _tuitionStatistics$mo2.map(stat => stat.totalExpectedAmount)) || [],\n          backgroundColor: 'rgba(54, 162, 235, 0.5)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }, {\n          label: 'Số tiền đã thu',\n          data: ((_tuitionStatistics$mo3 = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo3 === void 0 ? void 0 : _tuitionStatistics$mo3.map(stat => stat.totalPaidAmount)) || [],\n          backgroundColor: 'rgba(75, 192, 192, 0.5)',\n          borderColor: 'rgba(75, 192, 192, 1)',\n          borderWidth: 1\n        }]\n      };\n\n      // Chuẩn bị dữ liệu cho biểu đồ theo lớp\n      const classData = {\n        labels: ((_tuitionStatistics$cl = tuitionStatistics.classStatistics) === null || _tuitionStatistics$cl === void 0 ? void 0 : _tuitionStatistics$cl.map(stat => \"L\\u1EDBp \".concat(stat.userClass))) || [],\n        datasets: [{\n          label: 'Số tiền cần thu',\n          data: ((_tuitionStatistics$cl2 = tuitionStatistics.classStatistics) === null || _tuitionStatistics$cl2 === void 0 ? void 0 : _tuitionStatistics$cl2.map(stat => stat.totalExpectedAmount)) || [],\n          backgroundColor: 'rgba(153, 102, 255, 0.5)',\n          borderColor: 'rgba(153, 102, 255, 1)',\n          borderWidth: 1\n        }, {\n          label: 'Số tiền đã thu',\n          data: ((_tuitionStatistics$cl3 = tuitionStatistics.classStatistics) === null || _tuitionStatistics$cl3 === void 0 ? void 0 : _tuitionStatistics$cl3.map(stat => stat.totalPaidAmount)) || [],\n          backgroundColor: 'rgba(255, 159, 64, 0.5)',\n          borderColor: 'rgba(255, 159, 64, 1)',\n          borderWidth: 1\n        }]\n      };\n\n      // Vẽ biểu đồ theo tháng\n      const monthlyCtx = monthlyChartRef.current.getContext('2d');\n      monthlyChartInstance.current = new Chart(monthlyCtx, {\n        type: 'bar',\n        data: monthlyData,\n        options: {\n          responsive: true,\n          scales: {\n            y: {\n              beginAtZero: true,\n              ticks: {\n                callback: function (value) {\n                  return formatCurrency(value);\n                }\n              }\n            }\n          },\n          plugins: {\n            tooltip: {\n              callbacks: {\n                label: function (context) {\n                  return \"\".concat(context.dataset.label, \": \").concat(formatCurrency(context.raw));\n                }\n              }\n            }\n          }\n        }\n      });\n\n      // Vẽ biểu đồ theo lớp\n      const classCtx = classChartRef.current.getContext('2d');\n      classChartInstance.current = new Chart(classCtx, {\n        type: 'bar',\n        data: classData,\n        options: {\n          responsive: true,\n          scales: {\n            y: {\n              beginAtZero: true,\n              ticks: {\n                callback: function (value) {\n                  return formatCurrency(value);\n                }\n              }\n            }\n          },\n          plugins: {\n            tooltip: {\n              callbacks: {\n                label: function (context) {\n                  return \"\".concat(context.dataset.label, \": \").concat(formatCurrency(context.raw));\n                }\n              }\n            }\n          }\n        }\n      });\n    }\n  }, [viewMode, tuitionStatistics]);\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      limit,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate expected amount (required if not calculating automatically)\n    if (!addCalculateExpected && !addExpectedAmount) {\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\n      errors.expectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate paid amount (must be a positive number if provided)\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\n      errors.paidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // Validate status (required)\n    if (!addStatus) {\n      errors.status = \"Vui lòng chọn trạng thái\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        status: addStatus,\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Only include expectedAmount if not calculating automatically\n      if (!addCalculateExpected && addExpectedAmount) {\n        paymentData.expectedAmount = Number(addExpectedAmount);\n      }\n\n      // Include paidAmount if provided\n      if (addPaidAmount) {\n        paymentData.paidAmount = Number(addPaidAmount);\n      }\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: currentPage,\n        limit,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddExpectedAmount(\"\");\n      setAddExpectedAmountFormatted(\"\");\n      setAddPaidAmount(\"\");\n      setAddPaidAmountFormatted(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddStatus(\"\");\n      setAddNote(\"\");\n      setAddCalculateExpected(false);\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Hàm xử lý khi thay đổi chế độ xem\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n\n    // Nếu chuyển sang chế độ thống kê, tải dữ liệu thống kê\n    if (mode === 'statistics') {\n      // Khởi tạo giá trị mặc định cho startMonth và endMonth nếu chưa có\n      const currentDate = new Date();\n      const currentYear = currentDate.getFullYear();\n      const currentMonth = currentDate.getMonth() + 1;\n\n      // Nếu chưa có startMonth, đặt là tháng hiện tại của năm trước\n      if (!startMonth) {\n        const formattedMonth = currentMonth < 10 ? \"0\".concat(currentMonth) : \"\".concat(currentMonth);\n        setStartMonth(\"\".concat(currentYear - 1, \"-\").concat(formattedMonth));\n      }\n\n      // Nếu chưa có endMonth, đặt là tháng hiện tại\n      if (!endMonth) {\n        const formattedMonth = currentMonth < 10 ? \"0\".concat(currentMonth) : \"\".concat(currentMonth);\n        setEndMonth(\"\".concat(currentYear, \"-\").concat(formattedMonth));\n      }\n\n      // Tải dữ liệu thống kê\n      dispatch(fetchTuitionStatistics({\n        startMonth: startMonth || \"\".concat(currentYear - 1, \"-\").concat(currentMonth < 10 ? \"0\".concat(currentMonth) : currentMonth),\n        endMonth: endMonth || \"\".concat(currentYear, \"-\").concat(currentMonth < 10 ? \"0\".concat(currentMonth) : currentMonth),\n        userClass: filterClass\n      }));\n    }\n  };\n  useEffect(() => {\n    if (!didInit) return;\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: currentPage,\n      // Reset về trang 1 khi tìm kiếm\n      limit,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, currentPage, limit, didInit]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditExpectedAmount(payment.expectedAmount || \"\");\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\n      setEditPaidAmount(payment.paidAmount || \"\");\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditStatus(payment.status || \"\");\n      setEditNote(payment.note || \"\");\n      setCalculateExpected(false);\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n\n  // Hàm xuất chi tiết thanh toán thành ảnh\n  const handleExportImage = async () => {\n    if (!detailsContainerRef.current) return;\n    setExportLoading(true);\n    try {\n      // Lưu trữ style hiện tại\n      const originalStyle = detailsContainerRef.current.getAttribute('style') || '';\n\n      // Thêm style tạm thời cho việc xuất ảnh\n      detailsContainerRef.current.setAttribute('style', \"\".concat(originalStyle, \";\\n         background-color: white;\\n         border-radius: 8px;\\n         box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\"));\n      const element = detailsContainerRef.current;\n      const canvas = await html2canvas(element, {\n        scale: 2,\n        // Tăng độ phân giải\n        useCORS: true,\n        // Cho phép tải hình ảnh từ các domain khác\n        logging: false,\n        backgroundColor: '#ffffff',\n        margin: {\n          top: 20,\n          right: 20,\n          bottom: 20,\n          left: 20\n        }\n      });\n\n      // Khôi phục style ban đầu\n      detailsContainerRef.current.setAttribute('style', originalStyle);\n\n      // Chuyển canvas thành URL\n      const imageUrl = canvas.toDataURL('image/png');\n\n      // Tạo link tải xuống\n      const link = document.createElement('a');\n      const fileName = tuitionPayment !== null && tuitionPayment !== void 0 && tuitionPayment.user ? \"hoc-phi-\".concat(tuitionPayment.user.lastName, \"-\").concat(tuitionPayment.user.firstName, \"-\").concat(tuitionPayment.monthFormatted, \".png\") : \"hoc-phi-\".concat(new Date().toISOString(), \".png\");\n      link.download = fileName;\n      link.href = imageUrl;\n      link.click();\n    } catch (error) {\n      console.error(\"Error exporting image:\", error);\n    } finally {\n      setExportLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddExpectedAmount(\"\");\n    setAddExpectedAmountFormatted(\"\");\n    setAddPaidAmount(\"\");\n    setAddPaidAmountFormatted(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddStatus(\"\");\n    setAddNote(\"\");\n    setAddCalculateExpected(false);\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const handleCreateByClass = () => {\n    setRightPanelType(\"batchByClass\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditExpectedAmount(\"\");\n    setEditExpectedAmountFormatted(\"\");\n    setEditPaidAmount(\"\");\n    setEditPaidAmountFormatted(\"\");\n    setEditPaymentDate(\"\");\n    setEditStatus(\"\");\n    setEditNote(\"\");\n    setCalculateExpected(false);\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate paid amount (must be a positive number)\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate expected amount (must be a positive number if provided)\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate status (required)\n    if (!editStatus) {\n      errors.editStatus = \"Trạng thái không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\n\n    // Update the actual values with parsed values\n    if (editPaidAmountFormatted) {\n      setEditPaidAmount(parsedPaidAmount);\n    }\n    if (editExpectedAmountFormatted && !calculateExpected) {\n      setEditExpectedAmount(parsedExpectedAmount);\n    }\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\n        status: editStatus,\n        note: editNote,\n        calculateExpected: calculateExpected\n      };\n\n      // Only include expectedAmount if it's provided and not calculating automatically\n      if (parsedExpectedAmount && !calculateExpected) {\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\n      }\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: currentPage,\n        limit,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        status: filterStatus,\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: currentPage,\n        limit,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n  const getStatusBadge = status => {\n    switch (status) {\n      case \"PAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n          children: \"\\u0110\\xE3 thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this);\n      case \"UNPAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\",\n          children: \"Ch\\u01B0a thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 11\n        }, this);\n      case \"OVERDUE\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\",\n          children: \"Qu\\xE1 h\\u1EA1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this);\n      case \"PARTIAL\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\",\n          children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\",\n          children: status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 861,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 860,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 867,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 866,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 873,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 872,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 878,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 885,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterStatus,\n              onChange: e => setFilterStatus(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PAID\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"UNPAID\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PARTIAL\",\n                children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex border border-gray-300 rounded-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 flex items-center \".concat(viewMode === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-100'),\n              onClick: () => handleViewModeChange('table'),\n              children: [/*#__PURE__*/_jsxDEV(List, {\n                size: 16,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 17\n              }, this), \"Danh s\\xE1ch\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 flex items-center \".concat(viewMode === 'statistics' ? 'bg-blue-500 text-white' : 'bg-gray-100'),\n              onClick: () => handleViewModeChange('statistics'),\n              children: [/*#__PURE__*/_jsxDEV(BarChart2, {\n                size: 16,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this), \"Th\\u1ED1ng k\\xEA\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconUsers,\n            text: 'Tạo học phí theo lớp',\n            onClick: handleCreateByClass\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1017,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 890,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 889,\n      columnNumber: 7\n    }, this), viewMode === 'table' ? tuitionPayments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1028,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1027,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xF3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user, _payment$user2, _payment$user3, _payment$user4;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (currentPage - 1) * limit + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.lastName) + \" \" + ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.expectedAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.paidAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1074,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleView(payment.id, payment.userId, payment.month),\n                    className: \"text-blue-500 hover:text-blue-700\",\n                    title: \"Xem chi ti\\u1EBFt\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1087,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1082,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(payment.id),\n                    className: \"text-yellow-500 hover:text-yellow-700\",\n                    title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1094,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1089,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(payment.id),\n                    className: \"text-red-500 hover:text-red-700\",\n                    title: \"X\\xF3a\",\n                    children: /*#__PURE__*/_jsxDEV(Trash, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1101,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 21\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1032,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1031,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"B\\u1ED9 l\\u1ECDc th\\u1ED1ng k\\xEA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"T\\u1EEB th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"month\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: startMonth,\n              onChange: e => setStartMonth(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1116,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0110\\u1EBFn th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"month\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: endMonth,\n              onChange: e => setEndMonth(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Kh\\u1ED1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\u1EA5t c\\u1EA3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-64\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"L\\u1EDBp h\\u1ECDc\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                dispatch(fetchTuitionStatistics({\n                  startMonth,\n                  endMonth,\n                  userClass: filterClass,\n                  classId: filterClassId\n                }));\n              },\n              className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 16,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 19\n              }, this), \"Xem th\\u1ED1ng k\\xEA\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1113,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1180,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1179,\n        columnNumber: 13\n      }, this) : tuitionStatistics ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Th\\u1ED1ng k\\xEA t\\u1ED5ng quan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 p-4 rounded-lg border border-blue-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-500 mb-1\",\n                children: \"T\\u1ED5ng s\\u1ED1 ti\\u1EC1n c\\u1EA7n thu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: formatCurrency(((_tuitionStatistics$to = tuitionStatistics.totalStatistics) === null || _tuitionStatistics$to === void 0 ? void 0 : _tuitionStatistics$to.totalExpectedAmount) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 p-4 rounded-lg border border-green-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-green-500 mb-1\",\n                children: \"T\\u1ED5ng s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 thu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1193,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: formatCurrency(((_tuitionStatistics$to2 = tuitionStatistics.totalStatistics) === null || _tuitionStatistics$to2 === void 0 ? void 0 : _tuitionStatistics$to2.totalPaidAmount) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 p-4 rounded-lg border border-red-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-red-500 mb-1\",\n                children: \"T\\u1ED5ng s\\u1ED1 ti\\u1EC1n c\\xF2n l\\u1EA1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: formatCurrency(((_tuitionStatistics$to3 = tuitionStatistics.totalStatistics) === null || _tuitionStatistics$to3 === void 0 ? void 0 : _tuitionStatistics$to3.remainingAmount) || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 p-4 rounded-lg border border-purple-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-purple-500 mb-1\",\n                children: \"T\\u1EF7 l\\u1EC7 thu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold\",\n                children: [((_tuitionStatistics$to4 = tuitionStatistics.totalStatistics) === null || _tuitionStatistics$to4 === void 0 ? void 0 : _tuitionStatistics$to4.collectionRate) || 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1202,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1185,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-4 rounded-lg border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Doanh thu theo th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1210,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-80\",\n              children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                ref: monthlyChartRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1211,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1209,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-4 rounded-lg border border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Doanh thu theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-80\",\n              children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n                ref: classChartRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1215,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1208,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Th\\u1ED1ng k\\xEA theo th\\xE1ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"Th\\xE1ng\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n thu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 thu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"S\\u1ED1 ti\\u1EC1n c\\xF2n l\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"T\\u1EF7 l\\u1EC7 thu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1234,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1229,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1228,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (_tuitionStatistics$mo4 = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo4 === void 0 ? void 0 : _tuitionStatistics$mo4.map((stat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: stat.monthFormatted\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1240,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: formatCurrency(stat.totalExpectedAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1241,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: formatCurrency(stat.totalPaidAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1242,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: formatCurrency(stat.remainingAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1243,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: [stat.collectionRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1244,\n                    columnNumber: 27\n                  }, this)]\n                }, stat.month, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1239,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1237,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1227,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1226,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1224,\n          columnNumber: 15\n        }, this), tuitionStatistics.classStatistics && tuitionStatistics.classStatistics.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Th\\u1ED1ng k\\xEA theo l\\u1EDBp\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1254,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"L\\u1EDBp\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1259,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n thu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 thu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1261,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"S\\u1ED1 ti\\u1EC1n c\\xF2n l\\u1EA1i\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1262,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"py-3 px-4 text-left\",\n                    children: \"T\\u1EF7 l\\u1EC7 thu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1263,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1258,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: tuitionStatistics.classStatistics.map((stat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: [\"L\\u1EDBp \", stat.userClass]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1269,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: formatCurrency(stat.totalExpectedAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: formatCurrency(stat.totalPaidAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1271,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: formatCurrency(stat.remainingAmount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1272,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"py-3 px-4\",\n                    children: [stat.collectionRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1273,\n                    columnNumber: 29\n                  }, this)]\n                }, stat.userClass, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1268,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1266,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1256,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1255,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1183,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 p-6 rounded-lg border border-blue-100 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-500\",\n          children: \"Vui l\\xF2ng ch\\u1ECDn kho\\u1EA3ng th\\u1EDDi gian v\\xE0 nh\\u1EA5n \\\"Xem th\\u1ED1ng k\\xEA\\\" \\u0111\\u1EC3 xem d\\u1EEF li\\u1EC7u th\\u1ED1ng k\\xEA.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1284,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1283,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1112,\n      columnNumber: 9\n    }, this), viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: currentPage,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: totalItems,\n        limit: limit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1291,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1290,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1306,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1317,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1313,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1305,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1324,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1329,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1327,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1351,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1352,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1350,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"addCalculateExpected\",\n                    checked: addCalculateExpected,\n                    onChange: e => setAddCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1368,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"addCalculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1375,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1365,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(addCalculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: addExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddExpectedAmount(value);\n                    setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: addCalculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 21\n              }, this), formErrors.expectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1396,\n                  columnNumber: 25\n                }, this), \" \", formErrors.expectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1395,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1364,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1401,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.paidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: addPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddPaidAmount(value);\n                    setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1402,\n                columnNumber: 21\n              }, this), formErrors.paidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1417,\n                  columnNumber: 25\n                }, this), \" \", formErrors.paidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1416,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1400,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1422,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1423,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1431,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1431,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1439,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1430,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1445,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.status ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addStatus,\n                onChange: e => setAddStatus(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1452,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1453,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1454,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1446,\n                columnNumber: 21\n              }, this), formErrors.status && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 25\n                }, this), \" \", formErrors.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1444,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1464,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1462,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1474,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1473,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1478,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1326,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1323,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1490,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1494,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1494,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1495,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1504,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1503,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1493,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500 text-xs font-normal\",\n                  children: \"(kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1510,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1509,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n ho\\u1EB7c \\u0111\\u1EC3 tr\\u1ED1ng \\u0111\\u1EC3 t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh\",\n                value: batchAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                    setBatchAmount(value ? parseInt(value, 10) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1512,\n                columnNumber: 21\n              }, this), formErrors.batchAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1527,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1526,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"N\\u1EBFu \\u0111\\u1EC3 tr\\u1ED1ng, h\\u1EC7 th\\u1ED1ng s\\u1EBD t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1530,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1508,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1535,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1535,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1542,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1543,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1544,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1545,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1536,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1549,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1548,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1534,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1554,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1555,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1564,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1563,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1553,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1569,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1570,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1568,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1578,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1492,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1489,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByClass\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t l\\u1EDBp h\\u1ECDc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1591,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"L\\u1EDBp h\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1595,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp h\\u1ECDc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1597,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1\",\n                  children: \"L\\u1EDBp 10A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1598,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2\",\n                  children: \"L\\u1EDBp 11A2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1599,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3\",\n                  children: \"L\\u1EDBp 12A3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1600,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1596,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1594,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1604,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1605,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1603,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1611,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1612,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1610,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"H\\u1EA1n thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1619,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1620,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1618,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1626,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1627,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1625,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n              children: \"T\\u1EA1o h\\u1ECDc ph\\xED theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1633,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1593,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1590,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1645,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"calculateExpected\",\n                    checked: calculateExpected,\n                    onChange: e => setCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1652,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"calculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh l\\u1EA1i d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1659,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1651,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1649,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(calculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: editExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: calculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1664,\n                columnNumber: 21\n              }, this), formErrors.editExpectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1679,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editExpectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1678,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1648,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1684,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: editPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1685,\n                columnNumber: 21\n              }, this), formErrors.editPaidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1699,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editPaidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1698,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1683,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1704,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1705,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1703,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1713,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1713,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editStatus ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editStatus,\n                onChange: e => setEditStatus(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1720,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1721,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1722,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1723,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1714,\n                columnNumber: 21\n              }, this), formErrors.editStatus && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1727,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editStatus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1712,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1732,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1733,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1731,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1742,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1746,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1647,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1644,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1761,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1760,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1769,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1773,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1774,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1775,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1772,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1768,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1767,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1783,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1785,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1785,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1786,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1786,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1787,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1787,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1788,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1788,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1784,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1782,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1794,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1796,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1796,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1797,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1797,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1798,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1798,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1799,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1799,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1801,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1802,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1800,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1813,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1813,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1814,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1814,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1815,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1815,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1795,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1793,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1822,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1827,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1828,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1826,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1832,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1832,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1833,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1833,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1834,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1834,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1825,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1823,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1839,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1840,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1838,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1821,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1849,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1850,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1848,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1853,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1854,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1855,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1856,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1852,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1847,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1846,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1765,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1864,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleExportImage,\n                className: \"flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 flex items-center justify-center\",\n                children: exportLoading ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      className: \"opacity-25\",\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1877,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      className: \"opacity-75\",\n                      fill: \"currentColor\",\n                      d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1878,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1876,\n                    columnNumber: 29\n                  }, this), \"\\u0110ang xu\\u1EA5t...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1875,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Xu\\u1EA5t \\u1EA3nh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1883,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1870,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1863,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1764,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1889,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1758,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1321,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1304,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1899,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 884,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"VciDjJTw1FtiW8ACeGPuOANS9r4=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "fetchStudentClassTuitionsByMonthAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "setSearch", "resetFilters", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "AdminLayout", "FunctionBarAdmin", "Chart", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionStatistics$to", "_tuitionStatistics$to2", "_tuitionStatistics$to3", "_tuitionStatistics$to4", "_tuitionStatistics$mo4", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "currentPage", "totalPages", "totalItems", "limit", "filter", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "didInit", "setDidInit", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "filterMonth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterStatus", "setFilterStatus", "filterOverdue", "setFilterOverdue", "filterClass", "setFilterClass", "filterClassId", "setFilterClassId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addExpectedAmount", "setAddExpectedAmount", "addExpectedAmountFormatted", "setAddExpectedAmountFormatted", "addPaidAmount", "setAddPaidAmount", "addPaidAmountFormatted", "setAddPaidAmountFormatted", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addStatus", "setAddStatus", "addNote", "setAddNote", "addCalculateExpected", "setAddCalculateExpected", "editId", "setEditId", "editExpectedAmount", "setEditExpectedAmount", "editExpectedAmountFormatted", "setEditExpectedAmountFormatted", "editPaidAmount", "setEditPaidAmount", "editPaidAmountFormatted", "setEditPaidAmountFormatted", "editPaymentDate", "setEditPaymentDate", "editStatus", "setEditStatus", "editNote", "setEditNote", "calculateExpected", "setCalculateExpected", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "current", "_tuitionStatistics$mo", "_tuitionStatistics$mo2", "_tuitionStatistics$mo3", "_tuitionStatistics$cl", "_tuitionStatistics$cl2", "_tuitionStatistics$cl3", "destroy", "monthlyData", "labels", "monthlyStatistics", "map", "stat", "monthFormatted", "datasets", "label", "data", "totalExpectedAmount", "backgroundColor", "borderColor", "borderWidth", "totalPaidAmount", "classData", "classStatistics", "concat", "userClass", "monthlyCtx", "getContext", "type", "options", "responsive", "scales", "y", "beginAtZero", "ticks", "callback", "value", "plugins", "tooltip", "callbacks", "context", "dataset", "raw", "classCtx", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "page", "sortOrder", "status", "month", "overdue", "classId", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "expectedAmount", "isNaN", "Number", "paidAmount", "dueDate", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "error", "console", "submit", "handleViewModeChange", "mode", "currentDate", "Date", "currentYear", "getFullYear", "currentMonth", "getMonth", "formattedMonth", "handleEdit", "response", "unwrap", "payment", "toISOString", "split", "handleView", "handleExportImage", "originalStyle", "getAttribute", "setAttribute", "element", "canvas", "scale", "useCORS", "logging", "margin", "top", "right", "bottom", "left", "imageUrl", "toDataURL", "link", "document", "createElement", "fileName", "user", "download", "href", "click", "handleAdd", "today", "formattedDate", "year", "handleBatchAdd", "handleCreateBatchTuition", "handleCreateByClass", "validateEditForm", "handleUpdateTuitionPayment", "parsedPaidAmount", "parsedExpectedAmount", "validateBatchTuitionForm", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "handleDelete", "confirmDelete", "cancelDelete", "getStatusBadge", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "placeholder", "onChange", "target", "Array", "from", "_", "i", "monthStr", "selectedClassId", "onSelect", "onClear", "strokeWidth", "index", "_payment$user", "_payment$user2", "_payment$user3", "_payment$user4", "class", "highSchool", "toLocaleDateString", "isOverdue", "title", "totalStatistics", "remainingAmount", "collectionRate", "ref", "onPageChange", "onSubmit", "role", "checked", "htmlFor", "replace", "parseInt", "disabled", "rows", "required", "phone", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "cx", "cy", "r", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n  fetchStudentClassTuitionsByMonthAdmin\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage, setSearch, resetFilters } from \"src/features/filter/filterSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { currentPage, totalPages, totalItems, limit } = useSelector(\r\n    (state) => state.filter\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho bộ lọc\r\n  const [filterMonth, setFilterMonth] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"\");\r\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  const [filterClass, setFilterClass] = useState(\"\");\r\n  const [filterClassId, setFilterClassId] = useState(\"\");\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addExpectedAmount, setAddExpectedAmount] = useState(\"\");\r\n  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState(\"\");\r\n  const [addPaidAmount, setAddPaidAmount] = useState(\"\");\r\n  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addStatus, setAddStatus] = useState(\"\");\r\n  const [addNote, setAddNote] = useState(\"\");\r\n  const [addCalculateExpected, setAddCalculateExpected] = useState(false);\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editExpectedAmount, setEditExpectedAmount] = useState(\"\");\r\n  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState(\"\");\r\n  const [editPaidAmount, setEditPaidAmount] = useState(\"\");\r\n  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState(\"\");\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editStatus, setEditStatus] = useState(\"\");\r\n  const [editNote, setEditNote] = useState(\"\");\r\n  const [calculateExpected, setCalculateExpected] = useState(false);\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (!didInit) {\r\n      dispatch(resetFilters());\r\n      setDidInit(true);\r\n    }\r\n  }, [dispatch, didInit]);\r\n\r\n  // Hiệu ứng để vẽ biểu đồ khi có dữ liệu thống kê\r\n  useEffect(() => {\r\n    if (viewMode === 'statistics' && tuitionStatistics && monthlyChartRef.current && classChartRef.current) {\r\n      // Hủy biểu đồ cũ nếu có\r\n      if (monthlyChartInstance.current) {\r\n        monthlyChartInstance.current.destroy();\r\n      }\r\n      if (classChartInstance.current) {\r\n        classChartInstance.current.destroy();\r\n      }\r\n\r\n      // Chuẩn bị dữ liệu cho biểu đồ theo tháng\r\n      const monthlyData = {\r\n        labels: tuitionStatistics.monthlyStatistics?.map(stat => stat.monthFormatted) || [],\r\n        datasets: [\r\n          {\r\n            label: 'Số tiền cần thu',\r\n            data: tuitionStatistics.monthlyStatistics?.map(stat => stat.totalExpectedAmount) || [],\r\n            backgroundColor: 'rgba(54, 162, 235, 0.5)',\r\n            borderColor: 'rgba(54, 162, 235, 1)',\r\n            borderWidth: 1\r\n          },\r\n          {\r\n            label: 'Số tiền đã thu',\r\n            data: tuitionStatistics.monthlyStatistics?.map(stat => stat.totalPaidAmount) || [],\r\n            backgroundColor: 'rgba(75, 192, 192, 0.5)',\r\n            borderColor: 'rgba(75, 192, 192, 1)',\r\n            borderWidth: 1\r\n          }\r\n        ]\r\n      };\r\n\r\n      // Chuẩn bị dữ liệu cho biểu đồ theo lớp\r\n      const classData = {\r\n        labels: tuitionStatistics.classStatistics?.map(stat => `Lớp ${stat.userClass}`) || [],\r\n        datasets: [\r\n          {\r\n            label: 'Số tiền cần thu',\r\n            data: tuitionStatistics.classStatistics?.map(stat => stat.totalExpectedAmount) || [],\r\n            backgroundColor: 'rgba(153, 102, 255, 0.5)',\r\n            borderColor: 'rgba(153, 102, 255, 1)',\r\n            borderWidth: 1\r\n          },\r\n          {\r\n            label: 'Số tiền đã thu',\r\n            data: tuitionStatistics.classStatistics?.map(stat => stat.totalPaidAmount) || [],\r\n            backgroundColor: 'rgba(255, 159, 64, 0.5)',\r\n            borderColor: 'rgba(255, 159, 64, 1)',\r\n            borderWidth: 1\r\n          }\r\n        ]\r\n      };\r\n\r\n      // Vẽ biểu đồ theo tháng\r\n      const monthlyCtx = monthlyChartRef.current.getContext('2d');\r\n      monthlyChartInstance.current = new Chart(monthlyCtx, {\r\n        type: 'bar',\r\n        data: monthlyData,\r\n        options: {\r\n          responsive: true,\r\n          scales: {\r\n            y: {\r\n              beginAtZero: true,\r\n              ticks: {\r\n                callback: function (value) {\r\n                  return formatCurrency(value);\r\n                }\r\n              }\r\n            }\r\n          },\r\n          plugins: {\r\n            tooltip: {\r\n              callbacks: {\r\n                label: function (context) {\r\n                  return `${context.dataset.label}: ${formatCurrency(context.raw)}`;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      // Vẽ biểu đồ theo lớp\r\n      const classCtx = classChartRef.current.getContext('2d');\r\n      classChartInstance.current = new Chart(classCtx, {\r\n        type: 'bar',\r\n        data: classData,\r\n        options: {\r\n          responsive: true,\r\n          scales: {\r\n            y: {\r\n              beginAtZero: true,\r\n              ticks: {\r\n                callback: function (value) {\r\n                  return formatCurrency(value);\r\n                }\r\n              }\r\n            }\r\n          },\r\n          plugins: {\r\n            tooltip: {\r\n              callbacks: {\r\n                label: function (context) {\r\n                  return `${context.dataset.label}: ${formatCurrency(context.raw)}`;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }, [viewMode, tuitionStatistics]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        limit,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate expected amount (required if not calculating automatically)\r\n    if (!addCalculateExpected && !addExpectedAmount) {\r\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\r\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\r\n      errors.expectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate paid amount (must be a positive number if provided)\r\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\r\n      errors.paidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!addStatus) {\r\n      errors.status = \"Vui lòng chọn trạng thái\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        status: addStatus,\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Only include expectedAmount if not calculating automatically\r\n      if (!addCalculateExpected && addExpectedAmount) {\r\n        paymentData.expectedAmount = Number(addExpectedAmount);\r\n      }\r\n\r\n      // Include paidAmount if provided\r\n      if (addPaidAmount) {\r\n        paymentData.paidAmount = Number(addPaidAmount);\r\n      }\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: currentPage,\r\n        limit,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddExpectedAmount(\"\");\r\n      setAddExpectedAmountFormatted(\"\");\r\n      setAddPaidAmount(\"\");\r\n      setAddPaidAmountFormatted(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddStatus(\"\");\r\n      setAddNote(\"\");\r\n      setAddCalculateExpected(false);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý khi thay đổi chế độ xem\r\n  const handleViewModeChange = (mode) => {\r\n    setViewMode(mode);\r\n\r\n    // Nếu chuyển sang chế độ thống kê, tải dữ liệu thống kê\r\n    if (mode === 'statistics') {\r\n      // Khởi tạo giá trị mặc định cho startMonth và endMonth nếu chưa có\r\n      const currentDate = new Date();\r\n      const currentYear = currentDate.getFullYear();\r\n      const currentMonth = currentDate.getMonth() + 1;\r\n\r\n      // Nếu chưa có startMonth, đặt là tháng hiện tại của năm trước\r\n      if (!startMonth) {\r\n        const formattedMonth = currentMonth < 10 ? `0${currentMonth}` : `${currentMonth}`;\r\n        setStartMonth(`${currentYear - 1}-${formattedMonth}`);\r\n      }\r\n\r\n      // Nếu chưa có endMonth, đặt là tháng hiện tại\r\n      if (!endMonth) {\r\n        const formattedMonth = currentMonth < 10 ? `0${currentMonth}` : `${currentMonth}`;\r\n        setEndMonth(`${currentYear}-${formattedMonth}`);\r\n      }\r\n\r\n      // Tải dữ liệu thống kê\r\n      dispatch(fetchTuitionStatistics({\r\n        startMonth: startMonth || `${currentYear - 1}-${currentMonth < 10 ? `0${currentMonth}` : currentMonth}`,\r\n        endMonth: endMonth || `${currentYear}-${currentMonth < 10 ? `0${currentMonth}` : currentMonth}`,\r\n        userClass: filterClass\r\n      }));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!didInit) return;\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: currentPage, // Reset về trang 1 khi tìm kiếm\r\n        limit,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, currentPage, limit, didInit]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditExpectedAmount(payment.expectedAmount || \"\");\r\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\r\n      setEditPaidAmount(payment.paidAmount || \"\");\r\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditStatus(payment.status || \"\");\r\n      setEditNote(payment.note || \"\");\r\n      setCalculateExpected(false);\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xuất chi tiết thanh toán thành ảnh\r\n  const handleExportImage = async () => {\r\n    if (!detailsContainerRef.current) return;\r\n\r\n    setExportLoading(true);\r\n    try {\r\n      // Lưu trữ style hiện tại\r\n      const originalStyle = detailsContainerRef.current.getAttribute('style') || '';\r\n\r\n      // Thêm style tạm thời cho việc xuất ảnh\r\n      detailsContainerRef.current.setAttribute(\r\n        'style',\r\n        `${originalStyle};\r\n         background-color: white;\r\n         border-radius: 8px;\r\n         box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);`\r\n      );\r\n\r\n      const element = detailsContainerRef.current;\r\n      const canvas = await html2canvas(element, {\r\n        scale: 2, // Tăng độ phân giải\r\n        useCORS: true, // Cho phép tải hình ảnh từ các domain khác\r\n        logging: false,\r\n        backgroundColor: '#ffffff',\r\n        margin: {\r\n          top: 20,\r\n          right: 20,\r\n          bottom: 20,\r\n          left: 20\r\n        }\r\n      });\r\n\r\n      // Khôi phục style ban đầu\r\n      detailsContainerRef.current.setAttribute('style', originalStyle);\r\n\r\n      // Chuyển canvas thành URL\r\n      const imageUrl = canvas.toDataURL('image/png');\r\n\r\n      // Tạo link tải xuống\r\n      const link = document.createElement('a');\r\n      const fileName = tuitionPayment?.user\r\n        ? `hoc-phi-${tuitionPayment.user.lastName}-${tuitionPayment.user.firstName}-${tuitionPayment.monthFormatted}.png`\r\n        : `hoc-phi-${new Date().toISOString()}.png`;\r\n\r\n      link.download = fileName;\r\n      link.href = imageUrl;\r\n      link.click();\r\n    } catch (error) {\r\n      console.error(\"Error exporting image:\", error);\r\n    } finally {\r\n      setExportLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddExpectedAmount(\"\");\r\n    setAddExpectedAmountFormatted(\"\");\r\n    setAddPaidAmount(\"\");\r\n    setAddPaidAmountFormatted(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddStatus(\"\");\r\n    setAddNote(\"\");\r\n    setAddCalculateExpected(false);\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateByClass = () => {\r\n    setRightPanelType(\"batchByClass\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditExpectedAmount(\"\");\r\n    setEditExpectedAmountFormatted(\"\");\r\n    setEditPaidAmount(\"\");\r\n    setEditPaidAmountFormatted(\"\");\r\n    setEditPaymentDate(\"\");\r\n    setEditStatus(\"\");\r\n    setEditNote(\"\");\r\n    setCalculateExpected(false);\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate paid amount (must be a positive number)\r\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\r\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate expected amount (must be a positive number if provided)\r\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\r\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!editStatus) {\r\n      errors.editStatus = \"Trạng thái không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\r\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\r\n\r\n    // Update the actual values with parsed values\r\n    if (editPaidAmountFormatted) {\r\n      setEditPaidAmount(parsedPaidAmount);\r\n    }\r\n\r\n    if (editExpectedAmountFormatted && !calculateExpected) {\r\n      setEditExpectedAmount(parsedExpectedAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\r\n        status: editStatus,\r\n        note: editNote,\r\n        calculateExpected: calculateExpected\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided and not calculating automatically\r\n      if (parsedExpectedAmount && !calculateExpected) {\r\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\r\n      }\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: currentPage,\r\n        limit,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: currentPage,\r\n        limit,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  const getStatusBadge = (status) => {\r\n    switch (status) {\r\n      case \"PAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\">\r\n            Đã thanh toán\r\n          </span>\r\n        );\r\n      case \"UNPAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\">\r\n            Chưa thanh toán\r\n          </span>\r\n        );\r\n      case \"OVERDUE\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\">\r\n            Quá hạn\r\n          </span>\r\n        );\r\n      case \"PARTIAL\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\">\r\n            Thanh toán một phần\r\n          </span>\r\n        );\r\n      default:\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\">\r\n            {status}\r\n          </span>\r\n        );\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterStatus}\r\n                onChange={(e) => setFilterStatus(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"PAID\">Đã thanh toán</option>\r\n                <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n            <div className=\"flex border border-gray-300 rounded-md overflow-hidden\">\r\n              <button\r\n                className={`px-4 py-2 flex items-center ${viewMode === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}\r\n                onClick={() => handleViewModeChange('table')}\r\n              >\r\n                <List size={16} className=\"mr-2\" />\r\n                Danh sách\r\n              </button>\r\n              <button\r\n                className={`px-4 py-2 flex items-center ${viewMode === 'statistics' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}\r\n                onClick={() => handleViewModeChange('statistics')}\r\n              >\r\n                <BarChart2 size={16} className=\"mr-2\" />\r\n                Thống kê\r\n              </button>\r\n            </div>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n            <ButtonFunctionBarAdmin icon={iconUsers} text={'Tạo học phí theo lớp'} onClick={handleCreateByClass} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {viewMode === 'table' ? (\r\n        tuitionPayments.length === 0 ? (\r\n          <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n            <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n              <thead className=\"bg-gray-100\">\r\n                <tr>\r\n                  <th className=\"py-3 px-4 text-left\">STT</th>\r\n                  <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                  <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                  <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                  <th className=\"py-3 px-4 text-left\">Số tiền cần</th>\r\n                  <th className=\"py-3 px-4 text-left\">Số tiền đóng</th>\r\n                  <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                  <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                  <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                  <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                  <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {tuitionPayments.map((payment, index) => (\r\n                  <tr\r\n                    key={payment.id}\r\n                    className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                  >\r\n                    <td className=\"py-3 px-4\">\r\n                      {(currentPage - 1) * limit + index + 1}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                    <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {formatCurrency(payment.expectedAmount)}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {formatCurrency(payment.paidAmount)}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {payment.paymentDate\r\n                        ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                        : \"Chưa thanh toán\"}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      <div className=\"flex space-x-2\">\r\n                        <button\r\n                          onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                          className=\"text-blue-500 hover:text-blue-700\"\r\n                          title=\"Xem chi tiết\"\r\n                        >\r\n                          <Eye size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleEdit(payment.id)}\r\n                          className=\"text-yellow-500 hover:text-yellow-700\"\r\n                          title=\"Chỉnh sửa\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(payment.id)}\r\n                          className=\"text-red-500 hover:text-red-700\"\r\n                          title=\"Xóa\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )\r\n      ) : (\r\n        <div className=\"bg-white shadow-md rounded-lg p-6\">\r\n          <div className=\"mb-6\">\r\n            <h2 className=\"text-xl font-semibold mb-4\">Bộ lọc thống kê</h2>\r\n            <div className=\"flex flex-wrap gap-4\">\r\n              <div className=\"w-64\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Từ tháng</label>\r\n                <input\r\n                  type=\"month\"\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                  value={startMonth}\r\n                  onChange={(e) => setStartMonth(e.target.value)}\r\n                />\r\n              </div>\r\n              <div className=\"w-64\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Đến tháng</label>\r\n                <input\r\n                  type=\"month\"\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                  value={endMonth}\r\n                  onChange={(e) => setEndMonth(e.target.value)}\r\n                />\r\n              </div>\r\n              <div className=\"w-64\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Khối</label>\r\n                <select\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                  value={filterClass}\r\n                  onChange={(e) => setFilterClass(e.target.value)}\r\n                >\r\n                  <option value=\"\">Tất cả</option>\r\n                  <option value=\"10\">Khối 10</option>\r\n                  <option value=\"11\">Khối 11</option>\r\n                  <option value=\"12\">Khối 12</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"w-64\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp học</label>\r\n                <ClassSearchInput\r\n                  value={classSearchTerm}\r\n                  selectedClassId={filterClassId}\r\n                  onChange={setClassSearchTerm}\r\n                  onSelect={handleSelectClass}\r\n                  onClear={handleClearClassSelection}\r\n                  placeholder=\"Tìm kiếm lớp học...\"\r\n                />\r\n              </div>\r\n              <div className=\"flex items-end\">\r\n                <button\r\n                  onClick={() => {\r\n                    dispatch(fetchTuitionStatistics({\r\n                      startMonth,\r\n                      endMonth,\r\n                      userClass: filterClass,\r\n                      classId: filterClassId\r\n                    }));\r\n                  }}\r\n                  className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n                >\r\n                  <Search size={16} className=\"mr-2\" />\r\n                  Xem thống kê\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {loading ? (\r\n            <div className=\"flex justify-center items-center h-64\">\r\n              <LoadingSpinner />\r\n            </div>\r\n          ) : tuitionStatistics ? (\r\n            <div>\r\n              {/* Thống kê tổng quan */}\r\n              <div className=\"mb-8\">\r\n                <h3 className=\"text-lg font-semibold mb-4\">Thống kê tổng quan</h3>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n                  <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-100\">\r\n                    <p className=\"text-sm text-blue-500 mb-1\">Tổng số tiền cần thu</p>\r\n                    <p className=\"text-2xl font-bold\">{formatCurrency(tuitionStatistics.totalStatistics?.totalExpectedAmount || 0)}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 p-4 rounded-lg border border-green-100\">\r\n                    <p className=\"text-sm text-green-500 mb-1\">Tổng số tiền đã thu</p>\r\n                    <p className=\"text-2xl font-bold\">{formatCurrency(tuitionStatistics.totalStatistics?.totalPaidAmount || 0)}</p>\r\n                  </div>\r\n                  <div className=\"bg-red-50 p-4 rounded-lg border border-red-100\">\r\n                    <p className=\"text-sm text-red-500 mb-1\">Tổng số tiền còn lại</p>\r\n                    <p className=\"text-2xl font-bold\">{formatCurrency(tuitionStatistics.totalStatistics?.remainingAmount || 0)}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 p-4 rounded-lg border border-purple-100\">\r\n                    <p className=\"text-sm text-purple-500 mb-1\">Tỷ lệ thu</p>\r\n                    <p className=\"text-2xl font-bold\">{tuitionStatistics.totalStatistics?.collectionRate || 0}%</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Biểu đồ */}\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\r\n                <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\r\n                  <h3 className=\"text-lg font-semibold mb-4\">Doanh thu theo tháng</h3>\r\n                  <div className=\"h-80\">\r\n                    <canvas ref={monthlyChartRef}></canvas>\r\n                  </div>\r\n                </div>\r\n                <div className=\"bg-white p-4 rounded-lg border border-gray-200\">\r\n                  <h3 className=\"text-lg font-semibold mb-4\">Doanh thu theo lớp</h3>\r\n                  <div className=\"h-80\">\r\n                    <canvas ref={classChartRef}></canvas>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Bảng thống kê chi tiết */}\r\n              <div className=\"mb-8\">\r\n                <h3 className=\"text-lg font-semibold mb-4\">Thống kê theo tháng</h3>\r\n                <div className=\"overflow-x-auto\">\r\n                  <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n                    <thead className=\"bg-gray-100\">\r\n                      <tr>\r\n                        <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                        <th className=\"py-3 px-4 text-left\">Số tiền cần thu</th>\r\n                        <th className=\"py-3 px-4 text-left\">Số tiền đã thu</th>\r\n                        <th className=\"py-3 px-4 text-left\">Số tiền còn lại</th>\r\n                        <th className=\"py-3 px-4 text-left\">Tỷ lệ thu</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      {tuitionStatistics.monthlyStatistics?.map((stat, index) => (\r\n                        <tr key={stat.month} className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}>\r\n                          <td className=\"py-3 px-4\">{stat.monthFormatted}</td>\r\n                          <td className=\"py-3 px-4\">{formatCurrency(stat.totalExpectedAmount)}</td>\r\n                          <td className=\"py-3 px-4\">{formatCurrency(stat.totalPaidAmount)}</td>\r\n                          <td className=\"py-3 px-4\">{formatCurrency(stat.remainingAmount)}</td>\r\n                          <td className=\"py-3 px-4\">{stat.collectionRate}%</td>\r\n                        </tr>\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n\r\n              {tuitionStatistics.classStatistics && tuitionStatistics.classStatistics.length > 0 && (\r\n                <div>\r\n                  <h3 className=\"text-lg font-semibold mb-4\">Thống kê theo lớp</h3>\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n                      <thead className=\"bg-gray-100\">\r\n                        <tr>\r\n                          <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                          <th className=\"py-3 px-4 text-left\">Số tiền cần thu</th>\r\n                          <th className=\"py-3 px-4 text-left\">Số tiền đã thu</th>\r\n                          <th className=\"py-3 px-4 text-left\">Số tiền còn lại</th>\r\n                          <th className=\"py-3 px-4 text-left\">Tỷ lệ thu</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {tuitionStatistics.classStatistics.map((stat, index) => (\r\n                          <tr key={stat.userClass} className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}>\r\n                            <td className=\"py-3 px-4\">Lớp {stat.userClass}</td>\r\n                            <td className=\"py-3 px-4\">{formatCurrency(stat.totalExpectedAmount)}</td>\r\n                            <td className=\"py-3 px-4\">{formatCurrency(stat.totalPaidAmount)}</td>\r\n                            <td className=\"py-3 px-4\">{formatCurrency(stat.remainingAmount)}</td>\r\n                            <td className=\"py-3 px-4\">{stat.collectionRate}%</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"bg-blue-50 p-6 rounded-lg border border-blue-100 text-center\">\r\n              <p className=\"text-blue-500\">Vui lòng chọn khoảng thời gian và nhấn \"Xem thống kê\" để xem dữ liệu thống kê.</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={currentPage}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={totalItems}\r\n            limit={limit}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"addCalculateExpected\"\r\n                          checked={addCalculateExpected}\r\n                          onChange={(e) => setAddCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"addCalculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${addCalculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={addExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddExpectedAmount(value);\r\n                          setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={addCalculateExpected}\r\n                    />\r\n                    {formErrors.expectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.expectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.paidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={addPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddPaidAmount(value);\r\n                          setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.paidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.paidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.status ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addStatus}\r\n                      onChange={(e) => setAddStatus(e.target.value)}\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.status && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.status}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng <span className=\"text-gray-500 text-xs font-normal\">(không bắt buộc)</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền hoặc để trống để tự động tính\"\r\n                      value={batchAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                          setBatchAmount(value ? parseInt(value, 10) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.batchAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchAmount}\r\n                      </p>\r\n                    )}\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      Nếu để trống, hệ thống sẽ tự động tính dựa trên các lớp học sinh đã tham gia\r\n                    </p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"batchByClass\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí cho tất cả học sinh trong một lớp học.</p>\r\n                {/* Form content for batch tuition by class */}\r\n                <form className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp học</label>\r\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\r\n                      <option value=\"\">Chọn lớp học</option>\r\n                      <option value=\"1\">Lớp 10A1</option>\r\n                      <option value=\"2\">Lớp 11A2</option>\r\n                      <option value=\"3\">Lớp 12A3</option>\r\n                    </select>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng</label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền cần đóng</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      placeholder=\"Nhập số tiền\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                  >\r\n                    Tạo học phí theo lớp\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"calculateExpected\"\r\n                          checked={calculateExpected}\r\n                          onChange={(e) => setCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"calculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính lại dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${calculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={editExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={calculateExpected}\r\n                    />\r\n                    {formErrors.editExpectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editExpectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={editPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.editPaidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editPaidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.editStatus ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editStatus}\r\n                      onChange={(e) => setEditStatus(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.editStatus && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editStatus}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                      <button\r\n                        onClick={handleExportImage}\r\n                        className=\"flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 flex items-center justify-center\"\r\n                      >\r\n                        {exportLoading ? (\r\n                          <span className=\"flex items-center\">\r\n                            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                            </svg>\r\n                            Đang xuất...\r\n                          </span>\r\n                        ) : (\r\n                          <span>Xuất ảnh</span>\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,qCAAqC,QAChC,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,EAAEC,SAAS,EAAEC,YAAY,QAAQ,iCAAiC;AACzF,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACzH,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAGvD,WAAW,CAAC,CAAC;EAC9B,MAAMwD,QAAQ,GAAGtD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuD,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAG5D,WAAW,CAAE6D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,WAAW;IAAEC,UAAU;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGlE,WAAW,CAC/D6D,KAAK,IAAKA,KAAK,CAACM,MACnB,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+E,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiF,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACmF,WAAW,EAAEC,cAAc,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuF,aAAa,EAAEC,gBAAgB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2F,aAAa,EAAEC,gBAAgB,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC+F,cAAc,EAAEC,iBAAiB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiG,cAAc,EAAEC,iBAAiB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAACmG,QAAQ,EAAEC,WAAW,CAAC,GAAGpG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqG,UAAU,EAAEC,aAAa,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuG,QAAQ,EAAEC,WAAW,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMyG,eAAe,GAAGxG,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMyG,aAAa,GAAGzG,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM0G,oBAAoB,GAAG1G,MAAM,CAAC,IAAI,CAAC;EACzC,MAAM2G,kBAAkB,GAAG3G,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAAC4G,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+G,WAAW,EAAEC,cAAc,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACmH,YAAY,EAAEC,eAAe,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqH,UAAU,EAAEC,aAAa,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuH,SAAS,EAAEC,YAAY,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyH,UAAU,EAAEC,aAAa,CAAC,GAAG1H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC2H,YAAY,EAAEC,eAAe,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC6H,SAAS,EAAEC,YAAY,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiI,QAAQ,EAAEC,WAAW,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACqI,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAChF,MAAM,CAACuI,aAAa,EAAEC,gBAAgB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyI,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC2I,cAAc,EAAEC,iBAAiB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6I,UAAU,EAAEC,aAAa,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+I,SAAS,EAAEC,YAAY,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiJ,OAAO,EAAEC,UAAU,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACqJ,MAAM,EAAEC,SAAS,CAAC,GAAGtJ,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACuJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACyJ,2BAA2B,EAAEC,8BAA8B,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAAC2J,cAAc,EAAEC,iBAAiB,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6J,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAAC+J,eAAe,EAAEC,kBAAkB,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiK,UAAU,EAAEC,aAAa,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmK,QAAQ,EAAEC,WAAW,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACuK,WAAW,EAAEC,cAAc,CAAC,GAAGxK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1K,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2K,WAAW,EAAEC,cAAc,CAAC,GAAG5K,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6K,aAAa,EAAEC,gBAAgB,CAAC,GAAG9K,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM+K,mBAAmB,GAAG9K,MAAM,CAAC,IAAI,CAAC;EAExCF,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8E,OAAO,EAAE;MACZpB,QAAQ,CAACjC,YAAY,CAAC,CAAC,CAAC;MACxBsD,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC,EAAE,CAACrB,QAAQ,EAAEoB,OAAO,CAAC,CAAC;;EAEvB;EACA9E,SAAS,CAAC,MAAM;IACd,IAAIoG,QAAQ,KAAK,YAAY,IAAIvC,iBAAiB,IAAI6C,eAAe,CAACuE,OAAO,IAAItE,aAAa,CAACsE,OAAO,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACtG;MACA,IAAI3E,oBAAoB,CAACqE,OAAO,EAAE;QAChCrE,oBAAoB,CAACqE,OAAO,CAACO,OAAO,CAAC,CAAC;MACxC;MACA,IAAI3E,kBAAkB,CAACoE,OAAO,EAAE;QAC9BpE,kBAAkB,CAACoE,OAAO,CAACO,OAAO,CAAC,CAAC;MACtC;;MAEA;MACA,MAAMC,WAAW,GAAG;QAClBC,MAAM,EAAE,EAAAR,qBAAA,GAAArH,iBAAiB,CAAC8H,iBAAiB,cAAAT,qBAAA,uBAAnCA,qBAAA,CAAqCU,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,cAAc,CAAC,KAAI,EAAE;QACnFC,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,EAAAd,sBAAA,GAAAtH,iBAAiB,CAAC8H,iBAAiB,cAAAR,sBAAA,uBAAnCA,sBAAA,CAAqCS,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,mBAAmB,CAAC,KAAI,EAAE;UACtFC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC,EACD;UACEL,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,EAAAb,sBAAA,GAAAvH,iBAAiB,CAAC8H,iBAAiB,cAAAP,sBAAA,uBAAnCA,sBAAA,CAAqCQ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACS,eAAe,CAAC,KAAI,EAAE;UAClFH,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;;MAED;MACA,MAAME,SAAS,GAAG;QAChBb,MAAM,EAAE,EAAAL,qBAAA,GAAAxH,iBAAiB,CAAC2I,eAAe,cAAAnB,qBAAA,uBAAjCA,qBAAA,CAAmCO,GAAG,CAACC,IAAI,gBAAAY,MAAA,CAAWZ,IAAI,CAACa,SAAS,CAAE,CAAC,KAAI,EAAE;QACrFX,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,EAAAX,sBAAA,GAAAzH,iBAAiB,CAAC2I,eAAe,cAAAlB,sBAAA,uBAAjCA,sBAAA,CAAmCM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,mBAAmB,CAAC,KAAI,EAAE;UACpFC,eAAe,EAAE,0BAA0B;UAC3CC,WAAW,EAAE,wBAAwB;UACrCC,WAAW,EAAE;QACf,CAAC,EACD;UACEL,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,EAAAV,sBAAA,GAAA1H,iBAAiB,CAAC2I,eAAe,cAAAjB,sBAAA,uBAAjCA,sBAAA,CAAmCK,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACS,eAAe,CAAC,KAAI,EAAE;UAChFH,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;;MAED;MACA,MAAMM,UAAU,GAAGjG,eAAe,CAACuE,OAAO,CAAC2B,UAAU,CAAC,IAAI,CAAC;MAC3DhG,oBAAoB,CAACqE,OAAO,GAAG,IAAItI,KAAK,CAACgK,UAAU,EAAE;QACnDE,IAAI,EAAE,KAAK;QACXZ,IAAI,EAAER,WAAW;QACjBqB,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE,IAAI;cACjBC,KAAK,EAAE;gBACLC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAE;kBACzB,OAAOnM,cAAc,CAACmM,KAAK,CAAC;gBAC9B;cACF;YACF;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE;cACPC,SAAS,EAAE;gBACTxB,KAAK,EAAE,SAAAA,CAAUyB,OAAO,EAAE;kBACxB,UAAAhB,MAAA,CAAUgB,OAAO,CAACC,OAAO,CAAC1B,KAAK,QAAAS,MAAA,CAAKvL,cAAc,CAACuM,OAAO,CAACE,GAAG,CAAC;gBACjE;cACF;YACF;UACF;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,QAAQ,GAAGjH,aAAa,CAACsE,OAAO,CAAC2B,UAAU,CAAC,IAAI,CAAC;MACvD/F,kBAAkB,CAACoE,OAAO,GAAG,IAAItI,KAAK,CAACiL,QAAQ,EAAE;QAC/Cf,IAAI,EAAE,KAAK;QACXZ,IAAI,EAAEM,SAAS;QACfO,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE,IAAI;cACjBC,KAAK,EAAE;gBACLC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAE;kBACzB,OAAOnM,cAAc,CAACmM,KAAK,CAAC;gBAC9B;cACF;YACF;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE;cACPC,SAAS,EAAE;gBACTxB,KAAK,EAAE,SAAAA,CAAUyB,OAAO,EAAE;kBACxB,UAAAhB,MAAA,CAAUgB,OAAO,CAACC,OAAO,CAAC1B,KAAK,QAAAS,MAAA,CAAKvL,cAAc,CAACuM,OAAO,CAACE,GAAG,CAAC;gBACjE;cACF;YACF;UACF;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACvH,QAAQ,EAAEvC,iBAAiB,CAAC,CAAC;EAEjC7D,SAAS,CAAC,MAAM;IACd0D,QAAQ,CAACpC,WAAW,CAAC,EAAE,CAAC,CAAC;IACzBoC,QAAQ,CAACrC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAACqC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMmK,iBAAiB,GAAIC,SAAS,IAAK;IACvCjI,gBAAgB,CAACiI,SAAS,CAACC,EAAE,CAAC;IAC9BhI,kBAAkB,CAAC+H,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCpI,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMmI,gBAAgB,GAAIC,QAAQ,IAAK;IACrClI,iBAAiB,CAACkI,QAAQ,CAACJ,EAAE,CAAC;IAC9B5H,iBAAiB,CAACgI,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCrI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAID;EACA,MAAMoI,YAAY,GAAGA,CAAA,KAAM;IACzB7K,QAAQ,CACNhD,oBAAoB,CAAC;MACnB8N,MAAM,EAAEhK,UAAU;MAClBiK,IAAI,EAAE,CAAC;MAAE;MACTnK,KAAK;MACLoK,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAErJ,YAAY;MACpBsJ,KAAK,EAAExJ,WAAW;MAClByJ,OAAO,EAAErJ,aAAa;MACtBkH,SAAS,EAAEhH,WAAW;MACtBoJ,OAAO,EAAElJ;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAMmJ,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACpH,SAAS,EAAE;MACdoH,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAACjH,QAAQ,EAAE;MACbgH,MAAM,CAACN,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAACQ,IAAI,CAAClH,QAAQ,CAAC,EAAE;MAC1CgH,MAAM,CAACN,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAACxF,oBAAoB,IAAI,CAAChB,iBAAiB,EAAE;MAC/C8G,MAAM,CAACG,cAAc,GAAG,uDAAuD;IACjF,CAAC,MAAM,IAAIjH,iBAAiB,KAAKkH,KAAK,CAAClH,iBAAiB,CAAC,IAAImH,MAAM,CAACnH,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3F8G,MAAM,CAACG,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAI7G,aAAa,KAAK8G,KAAK,CAAC9G,aAAa,CAAC,IAAI+G,MAAM,CAAC/G,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MACxE0G,MAAM,CAACM,UAAU,GAAG,0BAA0B;IAChD;;IAEA;IACA,IAAI,CAAC1G,UAAU,EAAE;MACfoG,MAAM,CAACO,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAI,CAACzG,SAAS,EAAE;MACdkG,MAAM,CAACP,MAAM,GAAG,0BAA0B;IAC5C;;IAEA;IACA,IAAIe,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClCjI,aAAa,CAACuH,MAAM,CAAC;MACrB;IACF;IAEArH,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMgI,WAAW,GAAG;QAClBV,MAAM,EAAErH,SAAS;QACjB8G,KAAK,EAAE1G,QAAQ;QACfyG,MAAM,EAAE3F,SAAS;QACjB8G,IAAI,EAAE5G,OAAO;QACbuG,OAAO,EAAE3G;MACX,CAAC;;MAED;MACA,IAAI,CAACM,oBAAoB,IAAIhB,iBAAiB,EAAE;QAC9CyH,WAAW,CAACR,cAAc,GAAGE,MAAM,CAACnH,iBAAiB,CAAC;MACxD;;MAEA;MACA,IAAII,aAAa,EAAE;QACjBqH,WAAW,CAACL,UAAU,GAAGD,MAAM,CAAC/G,aAAa,CAAC;MAChD;;MAEA;MACA,IAAII,cAAc,EAAE;QAClBiH,WAAW,CAACE,WAAW,GAAGnH,cAAc;MAC1C;;MAEA;MACA,MAAMlF,QAAQ,CAAC9C,oBAAoB,CAACiP,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjBtM,QAAQ,CAAChD,oBAAoB,CAAC;QAC5B+N,IAAI,EAAEtK,WAAW;QACjBG,KAAK;QACLkK,MAAM,EAAEhK,UAAU;QAClBkK,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA3G,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,6BAA6B,CAAC,EAAE,CAAC;MACjCE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,EAAE,CAAC;MAChBE,UAAU,CAAC,EAAE,CAAC;MACdE,uBAAuB,CAAC,KAAK,CAAC;IAEhC,CAAC,CAAC,OAAO4G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDtI,aAAa,CAAC;QAAEwI,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRtI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMuI,oBAAoB,GAAIC,IAAI,IAAK;IACrChK,WAAW,CAACgK,IAAI,CAAC;;IAEjB;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;MAC9B,MAAMC,WAAW,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;MAC7C,MAAMC,YAAY,GAAGJ,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;;MAE/C;MACA,IAAI,CAACrK,UAAU,EAAE;QACf,MAAMsK,cAAc,GAAGF,YAAY,GAAG,EAAE,OAAAjE,MAAA,CAAOiE,YAAY,OAAAjE,MAAA,CAAQiE,YAAY,CAAE;QACjFnK,aAAa,IAAAkG,MAAA,CAAI+D,WAAW,GAAG,CAAC,OAAA/D,MAAA,CAAImE,cAAc,CAAE,CAAC;MACvD;;MAEA;MACA,IAAI,CAACpK,QAAQ,EAAE;QACb,MAAMoK,cAAc,GAAGF,YAAY,GAAG,EAAE,OAAAjE,MAAA,CAAOiE,YAAY,OAAAjE,MAAA,CAAQiE,YAAY,CAAE;QACjFjK,WAAW,IAAAgG,MAAA,CAAI+D,WAAW,OAAA/D,MAAA,CAAImE,cAAc,CAAE,CAAC;MACjD;;MAEA;MACAlN,QAAQ,CAAC5C,sBAAsB,CAAC;QAC9BwF,UAAU,EAAEA,UAAU,OAAAmG,MAAA,CAAO+D,WAAW,GAAG,CAAC,OAAA/D,MAAA,CAAIiE,YAAY,GAAG,EAAE,OAAAjE,MAAA,CAAOiE,YAAY,IAAKA,YAAY,CAAE;QACvGlK,QAAQ,EAAEA,QAAQ,OAAAiG,MAAA,CAAO+D,WAAW,OAAA/D,MAAA,CAAIiE,YAAY,GAAG,EAAE,OAAAjE,MAAA,CAAOiE,YAAY,IAAKA,YAAY,CAAE;QAC/FhE,SAAS,EAAEhH;MACb,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED1F,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8E,OAAO,EAAE;IACdpB,QAAQ,CACNhD,oBAAoB,CAAC;MACnB8N,MAAM,EAAEhK,UAAU;MAClBiK,IAAI,EAAEtK,WAAW;MAAE;MACnBG,KAAK;MACLoK,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAErJ,YAAY;MACpBsJ,KAAK,EAAExJ,WAAW;MAClByJ,OAAO,EAAErJ,aAAa;MACtBkH,SAAS,EAAEhH;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAAChC,QAAQ,EAAES,WAAW,EAAEG,KAAK,EAAEQ,OAAO,CAAC,CAAC;EAE3C,MAAM+L,UAAU,GAAG,MAAO9C,EAAE,IAAK;IAC/BlG,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMiJ,QAAQ,GAAG,MAAMpN,QAAQ,CAAC1C,4BAA4B,CAAC+M,EAAE,CAAC,CAAC,CAACgD,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAAC7E,IAAI;;MAE7B;MACA1C,SAAS,CAACwE,EAAE,CAAC;MACbtE,qBAAqB,CAACuH,OAAO,CAAC3B,cAAc,IAAI,EAAE,CAAC;MACnD1F,8BAA8B,CAACqH,OAAO,CAAC3B,cAAc,GAAGlO,oBAAoB,CAAC6P,OAAO,CAAC3B,cAAc,CAAC,GAAG,EAAE,CAAC;MAC1GxF,iBAAiB,CAACmH,OAAO,CAACxB,UAAU,IAAI,EAAE,CAAC;MAC3CzF,0BAA0B,CAACiH,OAAO,CAACxB,UAAU,GAAGrO,oBAAoB,CAAC6P,OAAO,CAACxB,UAAU,CAAC,GAAG,EAAE,CAAC;MAC9FvF,kBAAkB,CAAC+G,OAAO,CAACjB,WAAW,GAAG,IAAIQ,IAAI,CAACS,OAAO,CAACjB,WAAW,CAAC,CAACkB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxG/G,aAAa,CAAC6G,OAAO,CAACrC,MAAM,IAAI,EAAE,CAAC;MACnCtE,WAAW,CAAC2G,OAAO,CAAClB,IAAI,IAAI,EAAE,CAAC;MAC/BvF,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACApF,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOgL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRpI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMsJ,UAAU,GAAG,MAAAA,CAAOpD,EAAE,EAAEoB,MAAM,EAAEP,KAAK,KAAK;IAC9C/D,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAMnH,QAAQ,CAAC1C,4BAA4B,CAAC+M,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACA5I,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOgL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRpF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMuG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACpG,mBAAmB,CAACC,OAAO,EAAE;IAElCF,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF;MACA,MAAMsG,aAAa,GAAGrG,mBAAmB,CAACC,OAAO,CAACqG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;;MAE7E;MACAtG,mBAAmB,CAACC,OAAO,CAACsG,YAAY,CACtC,OAAO,KAAA9E,MAAA,CACJ4E,aAAa,2HAIlB,CAAC;MAED,MAAMG,OAAO,GAAGxG,mBAAmB,CAACC,OAAO;MAC3C,MAAMwG,MAAM,GAAG,MAAMnR,WAAW,CAACkR,OAAO,EAAE;QACxCE,KAAK,EAAE,CAAC;QAAE;QACVC,OAAO,EAAE,IAAI;QAAE;QACfC,OAAO,EAAE,KAAK;QACdzF,eAAe,EAAE,SAAS;QAC1B0F,MAAM,EAAE;UACNC,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE;QACR;MACF,CAAC,CAAC;;MAEF;MACAjH,mBAAmB,CAACC,OAAO,CAACsG,YAAY,CAAC,OAAO,EAAEF,aAAa,CAAC;;MAEhE;MACA,MAAMa,QAAQ,GAAGT,MAAM,CAACU,SAAS,CAAC,WAAW,CAAC;;MAE9C;MACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxC,MAAMC,QAAQ,GAAGxO,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEyO,IAAI,cAAA/F,MAAA,CACtB1I,cAAc,CAACyO,IAAI,CAACnE,QAAQ,OAAA5B,MAAA,CAAI1I,cAAc,CAACyO,IAAI,CAACpE,SAAS,OAAA3B,MAAA,CAAI1I,cAAc,CAAC+H,cAAc,uBAAAW,MAAA,CAC9F,IAAI8D,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,SAAM;MAE7CmB,IAAI,CAACK,QAAQ,GAAGF,QAAQ;MACxBH,IAAI,CAACM,IAAI,GAAGR,QAAQ;MACpBE,IAAI,CAACO,KAAK,CAAC,CAAC;IACd,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRlF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM6H,SAAS,GAAGA,CAAA,KAAM;IACtB;IACA7K,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,6BAA6B,CAAC,EAAE,CAAC;IACjCE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,yBAAyB,CAAC,EAAE,CAAC;IAC7BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdE,uBAAuB,CAAC,KAAK,CAAC;IAC9B1B,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAMkL,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC;IACxB,MAAMuC,aAAa,GAAGD,KAAK,CAAC5B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvDnI,aAAa,CAAC+J,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACpC,WAAW,CAAC,CAAC;IAChC,MAAM7B,KAAK,GAAGiE,KAAK,CAAClC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAnE,MAAA,CAAMsG,IAAI,OAAAtG,MAAA,CAAImC,KAAK,GAAG,EAAE,OAAAnC,MAAA,CAAOmC,KAAK,IAAKA,KAAK,CAAE;IACpEzG,WAAW,CAACyI,cAAc,CAAC;;IAE3B;IACAzL,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+N,cAAc,GAAGA,CAAA,KAAM;IAC3B7N,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMgO,wBAAwB,GAAGA,CAAA,KAAM;IACrC9N,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMiO,mBAAmB,GAAGA,CAAA,KAAM;IAChC/N,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+K,eAAe,GAAGA,CAAA,KAAM;IAC5B/K,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACA4B,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChB8B,SAAS,CAAC,IAAI,CAAC;IACfE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,8BAA8B,CAAC,EAAE,CAAC;IAClCE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,0BAA0B,CAAC,EAAE,CAAC;IAC9BE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,aAAa,CAAC,EAAE,CAAC;IACjBE,WAAW,CAAC,EAAE,CAAC;IACfE,oBAAoB,CAAC,KAAK,CAAC;IAC3B;IACAE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BhD,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtBgD,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMjE,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAItF,cAAc,KAAK0F,KAAK,CAAC1F,cAAc,CAAC,IAAI2F,MAAM,CAAC3F,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3EsF,MAAM,CAACtF,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAIJ,kBAAkB,KAAK8F,KAAK,CAAC9F,kBAAkB,CAAC,IAAI+F,MAAM,CAAC/F,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;MACvF0F,MAAM,CAAC1F,kBAAkB,GAAG,0BAA0B;IACxD;;IAEA;IACA,IAAI,CAACU,UAAU,EAAE;MACfgF,MAAM,CAAChF,UAAU,GAAG,gCAAgC;IACtD;IAEA,OAAOgF,MAAM;EACf,CAAC;;EAED;EACA,MAAMkE,0BAA0B,GAAG,MAAOpE,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMoE,gBAAgB,GAAGvJ,uBAAuB,GAAG1I,kBAAkB,CAAC0I,uBAAuB,CAAC,GAAGF,cAAc;IAC/G,MAAM0J,oBAAoB,GAAG5J,2BAA2B,GAAGtI,kBAAkB,CAACsI,2BAA2B,CAAC,GAAGF,kBAAkB;;IAE/H;IACA,IAAIM,uBAAuB,EAAE;MAC3BD,iBAAiB,CAACwJ,gBAAgB,CAAC;IACrC;IAEA,IAAI3J,2BAA2B,IAAI,CAACY,iBAAiB,EAAE;MACrDb,qBAAqB,CAAC6J,oBAAoB,CAAC;IAC7C;;IAEA;IACA,MAAMpE,MAAM,GAAGiE,gBAAgB,CAAC,CAAC;IACjC,IAAIzD,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClCjI,aAAa,CAACuH,MAAM,CAAC;MACrB;IACF;IAEArH,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMgI,WAAW,GAAG;QAClBL,UAAU,EAAE6D,gBAAgB,GAAG9D,MAAM,CAAC8D,gBAAgB,CAAC,GAAG,CAAC;QAC3D1E,MAAM,EAAEzE,UAAU;QAClB4F,IAAI,EAAE1F,QAAQ;QACdE,iBAAiB,EAAEA;MACrB,CAAC;;MAED;MACA,IAAIgJ,oBAAoB,IAAI,CAAChJ,iBAAiB,EAAE;QAC9CuF,WAAW,CAACR,cAAc,GAAGE,MAAM,CAAC+D,oBAAoB,CAAC;MAC3D;;MAEA;MACA,IAAItJ,eAAe,EAAE;QACnB6F,WAAW,CAACE,WAAW,GAAG/F,eAAe;MAC3C;;MAEA;MACA,MAAMtG,QAAQ,CAAC3C,oBAAoB,CAAC;QAClCgN,EAAE,EAAEzE,MAAM;QACVuG;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjBtM,QAAQ,CAAChD,oBAAoB,CAAC;QAC5B+N,IAAI,EAAEtK,WAAW;QACjBG,KAAK;QACLkK,MAAM,EAAEhK,UAAU;QAClBkK,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAErJ,YAAY;QACpBsJ,KAAK,EAAExJ,WAAW;QAClByJ,OAAO,EAAErJ,aAAa;QACtBkH,SAAS,EAAEhH;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOuK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDtI,aAAa,CAAC;QAAEwI,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRtI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM0L,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMrE,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACpI,UAAU,EAAE;MACfoI,MAAM,CAACpI,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAACsI,IAAI,CAACtI,UAAU,CAAC,EAAE;MAC5CoI,MAAM,CAACpI,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAKsI,KAAK,CAACtI,WAAW,CAAC,IAAIuI,MAAM,CAACvI,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEkI,MAAM,CAAClI,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACf4H,MAAM,CAAC5H,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjB8H,MAAM,CAAC9H,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAO8H,MAAM;EACf,CAAC;;EAED;EACA,MAAMsE,wBAAwB,GAAG,MAAOxE,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMwE,iBAAiB,GAAGvM,oBAAoB,GAAG9F,kBAAkB,CAAC8F,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACwM,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAMvE,MAAM,GAAGqE,wBAAwB,CAAC,CAAC;IACzC,IAAI7D,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClCjI,aAAa,CAACuH,MAAM,CAAC;MACrB;IACF;IAEArH,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM6L,SAAS,GAAG;QAChB9E,KAAK,EAAE9H,UAAU;QACjB2I,OAAO,EAAErI,YAAY;QACrBE,UAAU;QACVwI,IAAI,EAAEtI;MACR,CAAC;;MAED;MACA,IAAIiM,iBAAiB,EAAE;QACrBC,SAAS,CAACrE,cAAc,GAAGE,MAAM,CAACkE,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAM/P,QAAQ,CAAC7C,0BAA0B,CAAC6S,SAAS,CAAC,CAAC;;MAErD;MACA1D,eAAe,CAAC,CAAC;MACjBtM,QAAQ,CAAChD,oBAAoB,CAAC;QAC5B+N,IAAI,EAAEtK,WAAW;QACjBG,KAAK;QACLkK,MAAM,EAAEhK,UAAU;QAClBkK,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DtI,aAAa,CAAC;QAAEwI,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACRtI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM8L,YAAY,GAAI5F,EAAE,IAAK;IAC3BlJ,kBAAkB,CAACkJ,EAAE,CAAC;IACtBpJ,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMiP,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChClQ,QAAQ,CAAC/C,oBAAoB,CAACiE,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMkP,YAAY,GAAGA,CAAA,KAAM;IACzBlP,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMiP,cAAc,GAAInF,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,oBACE9L,OAAA;UAAMkR,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE7E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,QAAQ;QACX,oBACEtR,OAAA;UAAMkR,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEzE;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACEtR,OAAA;UAAMkR,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAAC;QAE/E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACEtR,OAAA;UAAMkR,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAE3E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX;QACE,oBACEtR,OAAA;UAAMkR,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACvErF;QAAM;UAAA4D,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEb;EACF,CAAC;EAED,IAAIrQ,OAAO,EAAE;IACX,oBAAOjB,OAAA,CAACnB,cAAc;MAAA6Q,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACExR,OAAA;MACE2R,OAAO,EAAEA,OAAQ;MACjBT,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJM,IAAI,eACLzR,OAAA;QAAAmR,QAAA,EAAOO;MAAI;QAAAhC,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMM,OAAO,gBACX5R,OAAA;IAAK,wBAAgB;IAACkR,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnR,OAAA,CAACd,IAAI;MAAC2S,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMQ,SAAS,gBACb9R,OAAA;IAAK,wBAAgB;IAACkR,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnR,OAAA,CAACZ,QAAQ;MAACyS,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMS,YAAY,gBAChB/R,OAAA;IAAK,wBAAgB;IAACkR,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnR,OAAA,CAACX,QAAQ;MAACwS,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMU,SAAS,gBACbhS,OAAA;IAAK,wBAAgB;IAACkR,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnR,OAAA,CAACV,KAAK;MAACuS,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,oBACEtR,OAAA,CAACJ,WAAW;IAAAuR,QAAA,gBACVnR,OAAA;MAAKkR,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAzB,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENtR,OAAA;MAAKkR,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFnR,OAAA;QAAKkR,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBnR,OAAA;UAAKkR,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnR,OAAA;YAAKkR,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCnR,OAAA;cACEiS,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXnB,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnEnR,OAAA;gBACEsS,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAA/C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtR,OAAA;cACEgK,IAAI,EAAC,MAAM;cACX0I,WAAW,EAAC,kEAAwC;cACpDlI,KAAK,EAAE7I,UAAW;cAClBgR,QAAQ,EAAGxG,CAAC,IAAKvK,aAAa,CAACuK,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;cAC/C0G,SAAS,EAAC;YAAsI;cAAAxB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtR,OAAA;YAAKkR,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnR,OAAA;cACEkR,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAEjI,WAAY;cACnBoQ,QAAQ,EAAGxG,CAAC,IAAK3J,cAAc,CAAC2J,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;cAAA2G,QAAA,gBAEhDnR,OAAA;gBAAQwK,KAAK,EAAC,EAAE;gBAAA2G,QAAA,EAAC;cAAK;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9BuB,KAAK,CAACC,IAAI,CAAC;gBAAE/F,MAAM,EAAE;cAAG,CAAC,EAAE,CAACgG,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAMjH,KAAK,GAAGiH,CAAC,GAAG,CAAC;gBACnB,MAAM9C,IAAI,GAAG,IAAIxC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;gBACrC,MAAMqF,QAAQ,GAAGlH,KAAK,GAAG,EAAE,OAAAnC,MAAA,CAAOmC,KAAK,OAAAnC,MAAA,CAAQmC,KAAK,CAAE;gBACtD,oBACE/L,OAAA;kBAAoBwK,KAAK,KAAAZ,MAAA,CAAKsG,IAAI,OAAAtG,MAAA,CAAIqJ,QAAQ,CAAG;kBAAA9B,QAAA,cAAAvH,MAAA,CACrCmC,KAAK,OAAAnC,MAAA,CAAIsG,IAAI;gBAAA,GADZnE,KAAK;kBAAA2D,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtR,OAAA;YAAKkR,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnR,OAAA;cACEkR,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAE/H,YAAa;cACpBkQ,QAAQ,EAAGxG,CAAC,IAAKzJ,eAAe,CAACyJ,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;cAAA2G,QAAA,gBAEjDnR,OAAA;gBAAQwK,KAAK,EAAC,EAAE;gBAAA2G,QAAA,EAAC;cAAU;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCtR,OAAA;gBAAQwK,KAAK,EAAC,MAAM;gBAAA2G,QAAA,EAAC;cAAa;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CtR,OAAA;gBAAQwK,KAAK,EAAC,QAAQ;gBAAA2G,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CtR,OAAA;gBAAQwK,KAAK,EAAC,SAAS;gBAAA2G,QAAA,EAAC;cAAmB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtR,OAAA;YAAKkR,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnR,OAAA;cACEkR,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAE7H,aAAc;cACrBgQ,QAAQ,EAAGxG,CAAC,IAAKvJ,gBAAgB,CAACuJ,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;cAAA2G,QAAA,gBAElDnR,OAAA;gBAAQwK,KAAK,EAAC,EAAE;gBAAA2G,QAAA,EAAC;cAAc;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCtR,OAAA;gBAAQwK,KAAK,EAAC,MAAM;gBAAA2G,QAAA,EAAC;cAAU;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCtR,OAAA;gBAAQwK,KAAK,EAAC,OAAO;gBAAA2G,QAAA,EAAC;cAAY;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtR,OAAA;YAAKkR,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnR,OAAA;cACEkR,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAE3H,WAAY;cACnB8P,QAAQ,EAAGxG,CAAC,IAAKrJ,cAAc,CAACqJ,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;cAAA2G,QAAA,gBAEhDnR,OAAA;gBAAQwK,KAAK,EAAC,EAAE;gBAAA2G,QAAA,EAAC;cAAI;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BtR,OAAA;gBAAQwK,KAAK,EAAC,IAAI;gBAAA2G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCtR,OAAA;gBAAQwK,KAAK,EAAC,IAAI;gBAAA2G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCtR,OAAA;gBAAQwK,KAAK,EAAC,IAAI;gBAAA2G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNtR,OAAA;YAAKkR,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnR,OAAA,CAACrC,gBAAgB;cACf6M,KAAK,EAAEvH,eAAgB;cACvBiQ,eAAe,EAAEnQ,aAAc;cAC/B4P,QAAQ,EAAEzP,kBAAmB;cAC7BiQ,QAAQ,EAAEnI,iBAAkB;cAC5BoI,OAAO,EAAEhI,yBAA0B;cACnCsH,WAAW,EAAC;YAAqB;cAAAhD,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtR,OAAA;YACE2R,OAAO,EAAEjG,YAAa;YACtBwF,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3FnR,OAAA;cAAKkR,SAAS,EAAC,cAAc;cAACmB,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAd,QAAA,eACpHnR,OAAA;gBAAMwS,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACY,WAAW,EAAC,GAAG;gBAACf,CAAC,EAAC;cAA6C;gBAAA5C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtR,OAAA;YAAKkR,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACrEnR,OAAA;cACEkR,SAAS,iCAAAtH,MAAA,CAAiCrG,QAAQ,KAAK,OAAO,GAAG,wBAAwB,GAAG,aAAa,CAAG;cAC5GoO,OAAO,EAAEA,CAAA,KAAMpE,oBAAoB,CAAC,OAAO,CAAE;cAAA4D,QAAA,gBAE7CnR,OAAA,CAACN,IAAI;gBAACmS,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtR,OAAA;cACEkR,SAAS,iCAAAtH,MAAA,CAAiCrG,QAAQ,KAAK,YAAY,GAAG,wBAAwB,GAAG,aAAa,CAAG;cACjHoO,OAAO,EAAEA,CAAA,KAAMpE,oBAAoB,CAAC,YAAY,CAAE;cAAA4D,QAAA,gBAElDnR,OAAA,CAACP,SAAS;gBAACoS,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAE1C;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAENtR,OAAA;UAAKkR,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnR,OAAA,CAACuR,sBAAsB;YAACE,IAAI,EAAEG,OAAQ;YAACF,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAE5B;UAAU;YAAAL,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtFtR,OAAA,CAACuR,sBAAsB;YAACE,IAAI,EAAEM,YAAa;YAACL,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAEvB;UAAyB;YAAAV,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChHtR,OAAA,CAACuR,sBAAsB;YAACE,IAAI,EAAEO,SAAU;YAACN,IAAI,EAAE,sBAAuB;YAACC,OAAO,EAAEtB;UAAoB;YAAAX,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL/N,QAAQ,KAAK,OAAO,GACnBxC,eAAe,CAACgM,MAAM,KAAK,CAAC,gBAC1B/M,OAAA;MAAKkR,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DnR,OAAA;QAAGkR,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAzB,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,gBAENtR,OAAA;MAAKkR,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BnR,OAAA;QAAOkR,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzEnR,OAAA;UAAOkR,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5BnR,OAAA;YAAAmR,QAAA,gBACEnR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAY;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDtR,OAAA;cAAIkR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRtR,OAAA;UAAAmR,QAAA,EACGpQ,eAAe,CAACgI,GAAG,CAAC,CAACoF,OAAO,EAAEmF,KAAK;YAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClC1T,OAAA;cAEEkR,SAAS,EAAEoC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAAnC,QAAA,gBAEvDnR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAAC7P,WAAW,GAAG,CAAC,IAAIG,KAAK,GAAG6R,KAAK,GAAG;cAAC;gBAAA5D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAAoC,aAAA,GAAApF,OAAO,CAACwB,IAAI,cAAA4D,aAAA,uBAAZA,aAAA,CAAc/H,QAAQ,IAAG,GAAG,KAAAgI,cAAA,GAAGrF,OAAO,CAACwB,IAAI,cAAA6D,cAAA,uBAAZA,cAAA,CAAcjI,SAAS,KAAI;cAAK;gBAAAmE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAsC,cAAA,GAAAtF,OAAO,CAACwB,IAAI,cAAA8D,cAAA,uBAAZA,cAAA,CAAcE,KAAK,KAAI;cAAK;gBAAAjE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAuC,cAAA,GAAAvF,OAAO,CAACwB,IAAI,cAAA+D,cAAA,uBAAZA,cAAA,CAAcE,UAAU,KAAI;cAAK;gBAAAlE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB9S,cAAc,CAAC8P,OAAO,CAAC3B,cAAc;cAAC;gBAAAkD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB9S,cAAc,CAAC8P,OAAO,CAACxB,UAAU;cAAC;gBAAA+C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhD,OAAO,CAAClF;cAAc;gBAAAyG,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhD,OAAO,CAACjB,WAAW,GAChB,IAAIQ,IAAI,CAACS,OAAO,CAACjB,WAAW,CAAC,CAAC2G,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAAnE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAIzD,IAAI,CAACS,OAAO,CAACvB,OAAO,CAAC,CAACiH,kBAAkB,CAAC,OAAO;cAAC;gBAAAnE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBF,cAAc,CAAC9C,OAAO,CAACrC,MAAM,KAAK,MAAM,GAAG,MAAM,GAAGqC,OAAO,CAAC2F,SAAS,GAAG,SAAS,GAAG3F,OAAO,CAACrC,MAAM,KAAK,SAAS,GAAG,SAAS,GAAGqC,OAAO,CAACrC,MAAM;cAAC;gBAAA4D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I,CAAC,eACLtR,OAAA;gBAAIkR,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBnR,OAAA;kBAAKkR,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BnR,OAAA;oBACE2R,OAAO,EAAEA,CAAA,KAAMrD,UAAU,CAACH,OAAO,CAACjD,EAAE,EAAEiD,OAAO,CAAC7B,MAAM,EAAE6B,OAAO,CAACpC,KAAK,CAAE;oBACrEmF,SAAS,EAAC,mCAAmC;oBAC7C6C,KAAK,EAAC,mBAAc;oBAAA5C,QAAA,eAEpBnR,OAAA,CAACb,GAAG;sBAAC0S,IAAI,EAAE;oBAAG;sBAAAnC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACTtR,OAAA;oBACE2R,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAACG,OAAO,CAACjD,EAAE,CAAE;oBACtCgG,SAAS,EAAC,uCAAuC;oBACjD6C,KAAK,EAAC,qBAAW;oBAAA5C,QAAA,eAEjBnR,OAAA,CAAChB,IAAI;sBAAC6S,IAAI,EAAE;oBAAG;sBAAAnC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACTtR,OAAA;oBACE2R,OAAO,EAAEA,CAAA,KAAMb,YAAY,CAAC3C,OAAO,CAACjD,EAAE,CAAE;oBACxCgG,SAAS,EAAC,iCAAiC;oBAC3C6C,KAAK,EAAC,QAAK;oBAAA5C,QAAA,eAEXnR,OAAA,CAACf,KAAK;sBAAC4S,IAAI,EAAE;oBAAG;sBAAAnC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArDAnD,OAAO,CAACjD,EAAE;cAAAwE,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsDb,CAAC;UAAA,CACN;QAAC;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,gBAEDtR,OAAA;MAAKkR,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnR,OAAA;QAAKkR,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnR,OAAA;UAAIkR,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAe;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/DtR,OAAA;UAAKkR,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCnR,OAAA;YAAKkR,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnR,OAAA;cAAOkR,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAQ;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChFtR,OAAA;cACEgK,IAAI,EAAC,OAAO;cACZkH,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAE/G,UAAW;cAClBkP,QAAQ,EAAGxG,CAAC,IAAKzI,aAAa,CAACyI,CAAC,CAACyG,MAAM,CAACpI,KAAK;YAAE;cAAAkF,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtR,OAAA;YAAKkR,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnR,OAAA;cAAOkR,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAS;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjFtR,OAAA;cACEgK,IAAI,EAAC,OAAO;cACZkH,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAE7G,QAAS;cAChBgP,QAAQ,EAAGxG,CAAC,IAAKvI,WAAW,CAACuI,CAAC,CAACyG,MAAM,CAACpI,KAAK;YAAE;cAAAkF,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtR,OAAA;YAAKkR,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnR,OAAA;cAAOkR,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAI;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5EtR,OAAA;cACEkR,SAAS,EAAC,oDAAoD;cAC9D1G,KAAK,EAAE3H,WAAY;cACnB8P,QAAQ,EAAGxG,CAAC,IAAKrJ,cAAc,CAACqJ,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;cAAA2G,QAAA,gBAEhDnR,OAAA;gBAAQwK,KAAK,EAAC,EAAE;gBAAA2G,QAAA,EAAC;cAAM;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCtR,OAAA;gBAAQwK,KAAK,EAAC,IAAI;gBAAA2G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCtR,OAAA;gBAAQwK,KAAK,EAAC,IAAI;gBAAA2G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCtR,OAAA;gBAAQwK,KAAK,EAAC,IAAI;gBAAA2G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtR,OAAA;YAAKkR,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnR,OAAA;cAAOkR,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAO;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/EtR,OAAA,CAACrC,gBAAgB;cACf6M,KAAK,EAAEvH,eAAgB;cACvBiQ,eAAe,EAAEnQ,aAAc;cAC/B4P,QAAQ,EAAEzP,kBAAmB;cAC7BiQ,QAAQ,EAAEnI,iBAAkB;cAC5BoI,OAAO,EAAEhI,yBAA0B;cACnCsH,WAAW,EAAC;YAAqB;cAAAhD,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtR,OAAA;YAAKkR,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BnR,OAAA;cACE2R,OAAO,EAAEA,CAAA,KAAM;gBACb9Q,QAAQ,CAAC5C,sBAAsB,CAAC;kBAC9BwF,UAAU;kBACVE,QAAQ;kBACRkG,SAAS,EAAEhH,WAAW;kBACtBoJ,OAAO,EAAElJ;gBACX,CAAC,CAAC,CAAC;cACL,CAAE;cACFmO,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAE3FnR,OAAA,CAACL,MAAM;gBAACkS,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEvC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELrQ,OAAO,gBACNjB,OAAA;QAAKkR,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDnR,OAAA,CAACnB,cAAc;UAAA6Q,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,GACJtQ,iBAAiB,gBACnBhB,OAAA;QAAAmR,QAAA,gBAEEnR,OAAA;UAAKkR,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBnR,OAAA;YAAIkR,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAkB;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEtR,OAAA;YAAKkR,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEnR,OAAA;cAAKkR,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/DnR,OAAA;gBAAGkR,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAoB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClEtR,OAAA;gBAAGkR,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE9S,cAAc,CAAC,EAAA8B,qBAAA,GAAAa,iBAAiB,CAACgT,eAAe,cAAA7T,qBAAA,uBAAjCA,qBAAA,CAAmCkJ,mBAAmB,KAAI,CAAC;cAAC;gBAAAqG,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC,eACNtR,OAAA;cAAKkR,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBACjEnR,OAAA;gBAAGkR,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAmB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClEtR,OAAA;gBAAGkR,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE9S,cAAc,CAAC,EAAA+B,sBAAA,GAAAY,iBAAiB,CAACgT,eAAe,cAAA5T,sBAAA,uBAAjCA,sBAAA,CAAmCqJ,eAAe,KAAI,CAAC;cAAC;gBAAAiG,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eACNtR,OAAA;cAAKkR,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DnR,OAAA;gBAAGkR,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAoB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEtR,OAAA;gBAAGkR,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAE9S,cAAc,CAAC,EAAAgC,sBAAA,GAAAW,iBAAiB,CAACgT,eAAe,cAAA3T,sBAAA,uBAAjCA,sBAAA,CAAmC4T,eAAe,KAAI,CAAC;cAAC;gBAAAvE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eACNtR,OAAA;cAAKkR,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEnR,OAAA;gBAAGkR,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAS;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzDtR,OAAA;gBAAGkR,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAE,EAAA7Q,sBAAA,GAAAU,iBAAiB,CAACgT,eAAe,cAAA1T,sBAAA,uBAAjCA,sBAAA,CAAmC4T,cAAc,KAAI,CAAC,EAAC,GAAC;cAAA;gBAAAxE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtR,OAAA;UAAKkR,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDnR,OAAA;YAAKkR,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DnR,OAAA;cAAIkR,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAoB;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEtR,OAAA;cAAKkR,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBnR,OAAA;gBAAQmU,GAAG,EAAEtQ;cAAgB;gBAAA6L,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtR,OAAA;YAAKkR,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DnR,OAAA;cAAIkR,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAkB;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEtR,OAAA;cAAKkR,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBnR,OAAA;gBAAQmU,GAAG,EAAErQ;cAAc;gBAAA4L,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtR,OAAA;UAAKkR,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBnR,OAAA;YAAIkR,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAmB;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEtR,OAAA;YAAKkR,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BnR,OAAA;cAAOkR,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACzEnR,OAAA;gBAAOkR,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC5BnR,OAAA;kBAAAmR,QAAA,gBACEnR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9CtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxDtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAc;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvDtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxDtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRtR,OAAA;gBAAAmR,QAAA,GAAA5Q,sBAAA,GACGS,iBAAiB,CAAC8H,iBAAiB,cAAAvI,sBAAA,uBAAnCA,sBAAA,CAAqCwI,GAAG,CAAC,CAACC,IAAI,EAAEsK,KAAK,kBACpDtT,OAAA;kBAAqBkR,SAAS,EAAEoC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;kBAAAnC,QAAA,gBAC1EnR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEnI,IAAI,CAACC;kBAAc;oBAAAyG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE9S,cAAc,CAAC2K,IAAI,CAACK,mBAAmB;kBAAC;oBAAAqG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzEtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE9S,cAAc,CAAC2K,IAAI,CAACS,eAAe;kBAAC;oBAAAiG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE9S,cAAc,CAAC2K,IAAI,CAACiL,eAAe;kBAAC;oBAAAvE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,GAAEnI,IAAI,CAACkL,cAAc,EAAC,GAAC;kBAAA;oBAAAxE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAL9CtI,IAAI,CAAC+C,KAAK;kBAAA2D,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMf,CACL;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELtQ,iBAAiB,CAAC2I,eAAe,IAAI3I,iBAAiB,CAAC2I,eAAe,CAACoD,MAAM,GAAG,CAAC,iBAChF/M,OAAA;UAAAmR,QAAA,gBACEnR,OAAA;YAAIkR,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAiB;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEtR,OAAA;YAAKkR,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BnR,OAAA;cAAOkR,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACzEnR,OAAA;gBAAOkR,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC5BnR,OAAA;kBAAAmR,QAAA,gBACEnR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAG;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5CtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxDtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAc;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvDtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxDtR,OAAA;oBAAIkR,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRtR,OAAA;gBAAAmR,QAAA,EACGnQ,iBAAiB,CAAC2I,eAAe,CAACZ,GAAG,CAAC,CAACC,IAAI,EAAEsK,KAAK,kBACjDtT,OAAA;kBAAyBkR,SAAS,EAAEoC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;kBAAAnC,QAAA,gBAC9EnR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,GAAC,WAAI,EAACnI,IAAI,CAACa,SAAS;kBAAA;oBAAA6F,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnDtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE9S,cAAc,CAAC2K,IAAI,CAACK,mBAAmB;kBAAC;oBAAAqG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzEtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE9S,cAAc,CAAC2K,IAAI,CAACS,eAAe;kBAAC;oBAAAiG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE9S,cAAc,CAAC2K,IAAI,CAACiL,eAAe;kBAAC;oBAAAvE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrEtR,OAAA;oBAAIkR,SAAS,EAAC,WAAW;oBAAAC,QAAA,GAAEnI,IAAI,CAACkL,cAAc,EAAC,GAAC;kBAAA;oBAAAxE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAL9CtI,IAAI,CAACa,SAAS;kBAAA6F,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMnB,CACL;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENtR,OAAA;QAAKkR,SAAS,EAAC,8DAA8D;QAAAC,QAAA,eAC3EnR,OAAA;UAAGkR,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5G,CACN;IAAA;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EACA/N,QAAQ,KAAK,OAAO,iBACnBvD,OAAA;MAAKkR,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBnR,OAAA,CAAClB,UAAU;QACTwC,WAAW,EAAEA,WAAY;QACzB8S,YAAY,EAAGxI,IAAI,IAAK/K,QAAQ,CAACnC,cAAc,CAACkN,IAAI,CAAC,CAAE;QACvDpK,UAAU,EAAEA,UAAW;QACvBC,KAAK,EAAEA;MAAM;QAAAiO,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAIAnP,cAAc,iBACbnC,OAAA;MAAKkR,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFnR,OAAA;QAAKkR,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EnR,OAAA;UAAIkR,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClC9O,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,cAAc,IAAI,sBAAsB,EAC3DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAAqN,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACLtR,OAAA;UACE2R,OAAO,EAAExE,eAAgB;UACzB+D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CnR,OAAA,CAACT,CAAC;YAACsS,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtR,OAAA;QAAAmR,QAAA,GACG9O,cAAc,KAAK,KAAK,iBACvBrC,OAAA;UAAKkR,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnR,OAAA;YAAGkR,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvEtR,OAAA;YAAMkR,SAAS,EAAC,WAAW;YAACmD,QAAQ,EAAEnI,sBAAuB;YAAAiF,QAAA,gBAC3DnR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxHtR,OAAA,CAACpC,eAAe;gBACd4M,KAAK,EAAErF,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1B0N,QAAQ,EAAEvN,oBAAqB;gBAC/B+N,QAAQ,EAAGxD,IAAI,IAAK;kBAClBzK,YAAY,CAACyK,IAAI,CAACzE,EAAE,CAAC;kBACrB9F,oBAAoB,IAAAwE,MAAA,CAAI+F,IAAI,CAACnE,QAAQ,OAAA5B,MAAA,CAAI+F,IAAI,CAACpE,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACF6H,OAAO,EAAEA,CAAA,KAAM;kBACblO,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACFsN,WAAW,EAAC,mCAAsB;gBAClC4B,IAAI,EAAC;cAAS;gBAAA5E,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDzM,UAAU,CAACyH,MAAM,iBAChBtM,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACyH,MAAM;cAAA;gBAAAoD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrHtR,OAAA;gBACEgK,IAAI,EAAC,OAAO;gBACZkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACkH,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3GvB,KAAK,EAAEnF,QAAS;gBAChBsN,QAAQ,EAAGxG,CAAC,IAAK7G,WAAW,CAAC6G,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACDzM,UAAU,CAACkH,KAAK,iBACf/L,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACkH,KAAK;cAAA;gBAAA2D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAAnR,OAAA;kBAAKkR,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCnR,OAAA;oBACEgK,IAAI,EAAC,UAAU;oBACfkB,EAAE,EAAC,sBAAsB;oBACzBqJ,OAAO,EAAEhO,oBAAqB;oBAC9BoM,QAAQ,EAAGxG,CAAC,IAAK3F,uBAAuB,CAAC2F,CAAC,CAACyG,MAAM,CAAC2B,OAAO,CAAE;oBAC3DrD,SAAS,EAAC;kBAAM;oBAAAxB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACFtR,OAAA;oBAAOwU,OAAO,EAAC,sBAAsB;oBAACtD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAExE;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAAC2H,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,kBAAA5C,MAAA,CAAerD,oBAAoB,GAAG,aAAa,GAAG,EAAE,CAAG;gBACjKmM,WAAW,EAAC,mDAAuB;gBACnClI,KAAK,EAAE/E,0BAA2B;gBAClCkN,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM3B,KAAK,GAAG2B,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAACiK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAACjC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjChF,oBAAoB,CAACgF,KAAK,CAAC;oBAC3B9E,6BAA6B,CAAC8E,KAAK,GAAGlM,oBAAoB,CAACoW,QAAQ,CAAClK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACvF;gBACF,CAAE;gBACFmK,QAAQ,EAAEpO;cAAqB;gBAAAmJ,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACDzM,UAAU,CAAC2H,cAAc,iBACxBxM,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAAC2H,cAAc;cAAA;gBAAAkD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAAC8H,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH+F,WAAW,EAAC,qDAAsB;gBAClClI,KAAK,EAAE3E,sBAAuB;gBAC9B8M,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM3B,KAAK,GAAG2B,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAACiK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAACjC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC5E,gBAAgB,CAAC4E,KAAK,CAAC;oBACvB1E,yBAAyB,CAAC0E,KAAK,GAAGlM,oBAAoB,CAACoW,QAAQ,CAAClK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACnF;gBACF;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDzM,UAAU,CAAC8H,UAAU,iBACpB3M,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAAC8H,UAAU;cAAA;gBAAA+C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,EAAC,oDAAoD;gBAC9D1G,KAAK,EAAEzE,cAAe;gBACtB4M,QAAQ,EAAGxG,CAAC,IAAKnG,iBAAiB,CAACmG,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAAC+H,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7GpC,KAAK,EAAEvE,UAAW;gBAClB0M,QAAQ,EAAGxG,CAAC,IAAKjG,aAAa,CAACiG,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDzM,UAAU,CAAC+H,OAAO,iBACjB5M,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAAC+H,OAAO;cAAA;gBAAA8C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1HtR,OAAA;gBACEkR,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACiH,MAAM,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC5GtB,KAAK,EAAErE,SAAU;gBACjBwM,QAAQ,EAAGxG,CAAC,IAAK/F,YAAY,CAAC+F,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;gBAAA2G,QAAA,gBAE9CnR,OAAA;kBAAQwK,KAAK,EAAC,EAAE;kBAAA2G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCtR,OAAA;kBAAQwK,KAAK,EAAC,MAAM;kBAAA2G,QAAA,EAAC;gBAAa;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CtR,OAAA;kBAAQwK,KAAK,EAAC,QAAQ;kBAAA2G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CtR,OAAA;kBAAQwK,KAAK,EAAC,SAAS;kBAAA2G,QAAA,EAAC;gBAAmB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRzM,UAAU,CAACiH,MAAM,iBAChB9L,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACiH,MAAM;cAAA;gBAAA4D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EtR,OAAA;gBACEkR,SAAS,EAAC,oDAAoD;gBAC9D0D,IAAI,EAAC,GAAG;gBACRlC,WAAW,EAAC,uCAAuB;gBACnClI,KAAK,EAAEnE,OAAQ;gBACfsM,QAAQ,EAAGxG,CAAC,IAAK7F,UAAU,CAAC6F,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLzM,UAAU,CAACyI,MAAM,iBAChBtN,OAAA;cAAKkR,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjGnR,OAAA,CAACR,WAAW;gBAACqS,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAA2B;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DtR,OAAA;gBAAAmR,QAAA,EAAItM,UAAU,CAACyI;cAAM;gBAAAoC,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACDtR,OAAA;cACEgK,IAAI,EAAC,QAAQ;cACbkH,SAAS,EAAC,uHAAuH;cACjIyD,QAAQ,EAAE5P,YAAa;cAAAoM,QAAA,EAEtBpM,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAA2K,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACAjP,cAAc,KAAK,cAAc,iBAChCrC,OAAA;UAAKkR,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnR,OAAA;YAAGkR,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElFtR,OAAA;YAAMkR,SAAS,EAAC,WAAW;YAACmD,QAAQ,EAAGlI,CAAC,IAAKwE,wBAAwB,CAACxE,CAAC,CAAE;YAAAgF,QAAA,gBACvEnR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrHtR,OAAA;gBACEgK,IAAI,EAAC,OAAO;gBACZkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHuG,KAAK,EAAEvG,UAAW;gBAClB0O,QAAQ,EAAGxG,CAAC,IAAKjI,aAAa,CAACiI,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;gBAC/CqK,QAAQ;cAAA;gBAAAnF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDzM,UAAU,CAACZ,UAAU,iBACpBjE,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACZ,UAAU;cAAA;gBAAAyL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,0CAC7C,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACRtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACV,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjHuO,WAAW,EAAC,2GAA4C;gBACxDlI,KAAK,EAAEnG,oBAAqB;gBAC5BsO,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM3B,KAAK,GAAG2B,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAACiK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAACjC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjClG,uBAAuB,CAACkG,KAAK,GAAGlM,oBAAoB,CAACoW,QAAQ,CAAClK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC/EpG,cAAc,CAACoG,KAAK,GAAGkK,QAAQ,CAAClK,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;kBAClD;gBACF;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDzM,UAAU,CAACV,WAAW,iBACrBnE,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACV,WAAW;cAAA;gBAAAuL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ,eACDtR,OAAA;gBAAGkR,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnHtR,OAAA;gBACEkR,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH+F,KAAK,EAAE/F,UAAW;gBAClBkO,QAAQ,EAAGxG,CAAC,IAAKzH,aAAa,CAACyH,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;gBAC/CqK,QAAQ;gBAAA1D,QAAA,gBAERnR,OAAA;kBAAQwK,KAAK,EAAC,EAAE;kBAAA2G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCtR,OAAA;kBAAQwK,KAAK,EAAC,IAAI;kBAAA2G,QAAA,EAAC;gBAAM;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCtR,OAAA;kBAAQwK,KAAK,EAAC,IAAI;kBAAA2G,QAAA,EAAC;gBAAM;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCtR,OAAA;kBAAQwK,KAAK,EAAC,IAAI;kBAAA2G,QAAA,EAAC;gBAAM;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACRzM,UAAU,CAACJ,UAAU,iBACpBzE,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACJ,UAAU;cAAA;gBAAAiL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClHiG,KAAK,EAAEjG,YAAa;gBACpBoO,QAAQ,EAAGxG,CAAC,IAAK3H,eAAe,CAAC2H,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;gBACjDqK,QAAQ;cAAA;gBAAAnF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDzM,UAAU,CAACN,YAAY,iBACtBvE,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACN,YAAY;cAAA;gBAAAmL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EtR,OAAA;gBACEkR,SAAS,EAAC,oDAAoD;gBAC9D0D,IAAI,EAAC,GAAG;gBACRlC,WAAW,EAAC,uCAAuB;gBACnClI,KAAK,EAAE7F,SAAU;gBACjBgO,QAAQ,EAAGxG,CAAC,IAAKvH,YAAY,CAACuH,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNtR,OAAA;cACEgK,IAAI,EAAC,QAAQ;cACbkH,SAAS,EAAC,uHAAuH;cACjIyD,QAAQ,EAAE5P,YAAa;cAAAoM,QAAA,EAEtBpM,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAA2K,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAjP,cAAc,KAAK,cAAc,iBAChCrC,OAAA;UAAKkR,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnR,OAAA;YAAGkR,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkD;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE1EtR,OAAA;YAAMkR,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzBnR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EtR,OAAA;gBAAQkR,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBACpEnR,OAAA;kBAAQwK,KAAK,EAAC,EAAE;kBAAA2G,QAAA,EAAC;gBAAY;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtR,OAAA;kBAAQwK,KAAK,EAAC,GAAG;kBAAA2G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCtR,OAAA;kBAAQwK,KAAK,EAAC,GAAG;kBAAA2G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCtR,OAAA;kBAAQwK,KAAK,EAAC,GAAG;kBAAA2G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7EtR,OAAA;gBACEgK,IAAI,EAAC,OAAO;gBACZkH,SAAS,EAAC;cAAoD;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxFtR,OAAA;gBACEgK,IAAI,EAAC,QAAQ;gBACbkH,SAAS,EAAC,oDAAoD;gBAC9DwB,WAAW,EAAC;cAAc;gBAAAhD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAc;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtFtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,EAAC;cAAoD;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EtR,OAAA;gBACEkR,SAAS,EAAC,oDAAoD;gBAC9D0D,IAAI,EAAC,GAAG;gBACRlC,WAAW,EAAC;cAAuB;gBAAAhD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNtR,OAAA;cACEgK,IAAI,EAAC,QAAQ;cACbkH,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAjP,cAAc,KAAK,MAAM,iBACxBrC,OAAA;UAAKkR,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnR,OAAA;YAAGkR,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/DtR,OAAA;YAAMkR,SAAS,EAAC,WAAW;YAACmD,QAAQ,EAAGlI,CAAC,IAAKoE,0BAA0B,CAACpE,CAAC,CAAE;YAAAgF,QAAA,gBACzEnR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAAnR,OAAA;kBAAKkR,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCnR,OAAA;oBACEgK,IAAI,EAAC,UAAU;oBACfkB,EAAE,EAAC,mBAAmB;oBACtBqJ,OAAO,EAAE9M,iBAAkB;oBAC3BkL,QAAQ,EAAGxG,CAAC,IAAKzE,oBAAoB,CAACyE,CAAC,CAACyG,MAAM,CAAC2B,OAAO,CAAE;oBACxDrD,SAAS,EAAC;kBAAM;oBAAAxB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACFtR,OAAA;oBAAOwU,OAAO,EAAC,mBAAmB;oBAACtD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErE;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAAC8B,kBAAkB,GAAG,gBAAgB,GAAG,iBAAiB,kBAAAiD,MAAA,CAAenC,iBAAiB,GAAG,aAAa,GAAG,EAAE,CAAG;gBAClKiL,WAAW,EAAC,mDAAuB;gBACnClI,KAAK,EAAE3D,2BAA4B;gBACnC8L,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM3B,KAAK,GAAG2B,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAACiK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAACjC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC1D,8BAA8B,CAAC0D,KAAK,GAAGlM,oBAAoB,CAACoW,QAAQ,CAAClK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACxF;gBACF,CAAE;gBACFmK,QAAQ,EAAElN;cAAkB;gBAAAiI,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EACDzM,UAAU,CAAC8B,kBAAkB,iBAC5B3G,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAAC8B,kBAAkB;cAAA;gBAAA+I,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACkC,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACpH2L,WAAW,EAAC,qDAAsB;gBAClClI,KAAK,EAAEvD,uBAAwB;gBAC/B0L,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM3B,KAAK,GAAG2B,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAACiK,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAACjC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjCtD,0BAA0B,CAACsD,KAAK,GAAGlM,oBAAoB,CAACoW,QAAQ,CAAClK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACpF;gBACF;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDzM,UAAU,CAACkC,cAAc,iBACxB/G,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACkC,cAAc;cAAA;gBAAA2I,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFtR,OAAA;gBACEgK,IAAI,EAAC,MAAM;gBACXkH,SAAS,EAAC,oDAAoD;gBAC9D1G,KAAK,EAAErD,eAAgB;gBACvBwL,QAAQ,EAAGxG,CAAC,IAAK/E,kBAAkB,CAAC+E,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAAnR,OAAA;kBAAMkR,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1HtR,OAAA;gBACEkR,SAAS,6BAAAtH,MAAA,CAA6B/E,UAAU,CAACwC,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHmD,KAAK,EAAEnD,UAAW;gBAClBsL,QAAQ,EAAGxG,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACyG,MAAM,CAACpI,KAAK,CAAE;gBAC/CqK,QAAQ;gBAAA1D,QAAA,gBAERnR,OAAA;kBAAQwK,KAAK,EAAC,EAAE;kBAAA2G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCtR,OAAA;kBAAQwK,KAAK,EAAC,MAAM;kBAAA2G,QAAA,EAAC;gBAAa;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CtR,OAAA;kBAAQwK,KAAK,EAAC,QAAQ;kBAAA2G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CtR,OAAA;kBAAQwK,KAAK,EAAC,SAAS;kBAAA2G,QAAA,EAAC;gBAAmB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRzM,UAAU,CAACwC,UAAU,iBACpBrH,OAAA;gBAAGkR,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnR,OAAA,CAACR,WAAW;kBAACqS,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACwC,UAAU;cAAA;gBAAAqI,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNtR,OAAA;cAAAmR,QAAA,gBACEnR,OAAA;gBAAOkR,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EtR,OAAA;gBACEkR,SAAS,EAAC,oDAAoD;gBAC9D0D,IAAI,EAAC,GAAG;gBACRlC,WAAW,EAAC,uCAAuB;gBACnClI,KAAK,EAAEjD,QAAS;gBAChBoL,QAAQ,EAAGxG,CAAC,IAAK3E,WAAW,CAAC2E,CAAC,CAACyG,MAAM,CAACpI,KAAK;cAAE;gBAAAkF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLzM,UAAU,CAACyI,MAAM,iBAChBtN,OAAA;cAAGkR,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDnR,OAAA,CAACR,WAAW;gBAACqS,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACzM,UAAU,CAACyI,MAAM;YAAA;cAAAoC,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACDtR,OAAA;cACEgK,IAAI,EAAC,QAAQ;cACbkH,SAAS,EAAC,uHAAuH;cACjIyD,QAAQ,EAAE5P,YAAa;cAAAoM,QAAA,EAEtBpM,YAAY,GAAG,eAAe,GAAG;YAAc;cAAA2K,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAjP,cAAc,KAAK,MAAM,iBACxBrC,OAAA;UAAAmR,QAAA,EACGpJ,WAAW,gBACV/H,OAAA;YAAKkR,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDnR,OAAA,CAACnB,cAAc;cAAA6Q,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJpQ,cAAc,gBAChBlB,OAAA;YAAKkR,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnR,OAAA;cAAKmU,GAAG,EAAEhM,mBAAoB;cAAC+I,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1EnR,OAAA;gBAAKkR,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnEnR,OAAA;kBAAKkR,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnR,OAAA;oBAAKkR,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtR,OAAA;oBAAAmR,QAAA,gBACEnR,OAAA;sBAAIkR,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClEtR,OAAA;sBAAGkR,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEtR,OAAA;sBAAGkR,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAIzD,IAAI,CAAC,CAAC,CAACmG,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAnE,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGNtR,OAAA;gBAAKkR,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnR,OAAA;kBAAIkR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEtR,OAAA;kBAAKkR,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAA9Q,oBAAA,GAACU,cAAc,CAACyO,IAAI,cAAAnP,oBAAA,uBAAnBA,oBAAA,CAAqBgL,QAAQ,EAAC,GAAC,GAAA/K,qBAAA,GAACS,cAAc,CAACyO,IAAI,cAAAlP,qBAAA,uBAAnBA,qBAAA,CAAqB8K,SAAS;kBAAA;oBAAAmE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpHtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAA5Q,qBAAA,GAAAQ,cAAc,CAACyO,IAAI,cAAAjP,qBAAA,uBAAnBA,qBAAA,CAAqBoU,KAAK,KAAI,UAAU;kBAAA;oBAAApF,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrGtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAA3Q,qBAAA,GAAAO,cAAc,CAACyO,IAAI,cAAAhP,qBAAA,uBAAnBA,qBAAA,CAAqBgT,KAAK,KAAI,UAAU;kBAAA;oBAAAjE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3FtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAA1Q,qBAAA,GAAAM,cAAc,CAACyO,IAAI,cAAA/O,qBAAA,uBAAnBA,qBAAA,CAAqBgT,UAAU,KAAI,UAAU;kBAAA;oBAAAlE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtR,OAAA;gBAAAmR,QAAA,gBACEnR,OAAA;kBAAIkR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEtR,OAAA;kBAAKkR,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvEnR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpQ,cAAc,CAAC+H,cAAc;kBAAA;oBAAAyG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClFtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjT,cAAc,CAAC6C,cAAc,CAACsL,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAkD,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClHtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjT,cAAc,CAAC6C,cAAc,CAACyL,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAA+C,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7GtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjT,cAAc,CAAC,CAAC6C,cAAc,CAACsL,cAAc,IAAI,CAAC,KAAKtL,cAAc,CAACyL,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAA+C,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9ItR,OAAA;oBAAAmR,QAAA,gBACEnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrDtR,OAAA;sBAAMkR,SAAS,oCAAAtH,MAAA,CAAoC1I,cAAc,CAAC4K,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjH5K,cAAc,CAAC4K,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5D5K,cAAc,CAAC4K,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAAqF,QAAA,EACFjQ,cAAc,CAAC4K,MAAM,KAAK,MAAM,GAAG,eAAe,GACjD5K,cAAc,CAAC4K,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpD5K,cAAc,CAAC4K,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAA4D,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpQ,cAAc,CAACgM,WAAW,GAAG,IAAIQ,IAAI,CAACxM,cAAc,CAACgM,WAAW,CAAC,CAAC2G,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAAnE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzKtR,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpQ,cAAc,CAAC0L,OAAO,GAAG,IAAIc,IAAI,CAACxM,cAAc,CAAC0L,OAAO,CAAC,CAACiH,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAAnE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJpQ,cAAc,CAAC+L,IAAI,iBAAIjN,OAAA;oBAAAmR,QAAA,gBAAGnR,OAAA;sBAAMkR,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpQ,cAAc,CAAC+L,IAAI;kBAAA;oBAAAyC,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLnQ,yBAAyB,IAAIA,yBAAyB,CAAC4T,aAAa,IAAI5T,yBAAyB,CAAC4T,aAAa,CAAChI,MAAM,GAAG,CAAC,iBACzH/M,OAAA;gBAAAmR,QAAA,gBACEnR,OAAA;kBAAIkR,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEtR,OAAA;kBAAKkR,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBhQ,yBAAyB,CAAC4T,aAAa,CAAChM,GAAG,CAAE1H,OAAO,iBACnDrB,OAAA;oBAAsBkR,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9EnR,OAAA;sBAAKkR,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDnR,OAAA;wBAAIkR,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAE9P,OAAO,CAAC6P;sBAAS;wBAAAxB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpDtR,OAAA;wBAAMkR,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAAC9P,OAAO,CAAC2T,UAAU;sBAAA;wBAAAtF,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAA5B,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNtR,OAAA;sBAAAmR,QAAA,gBAAGnR,OAAA;wBAAMkR,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAzB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACjT,cAAc,CAACgD,OAAO,CAAC4T,MAAM,CAAC;oBAAA;sBAAAvF,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/FtR,OAAA;sBAAAmR,QAAA,gBAAGnR,OAAA;wBAAMkR,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAzB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAI5D,IAAI,CAACrM,OAAO,CAAC6T,QAAQ,CAAC,CAACrB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnE,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrHjQ,OAAO,CAAC4L,IAAI,iBAAIjN,OAAA;sBAAAmR,QAAA,gBAAGnR,OAAA;wBAAMkR,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAzB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACjQ,OAAO,CAAC4L,IAAI;oBAAA;sBAAAyC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtFjQ,OAAO,CAAC6J,EAAE;oBAAAwE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtR,OAAA;kBAAKkR,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CnR,OAAA;oBAAGkR,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAAC9S,cAAc,CAAC8C,yBAAyB,CAACgU,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAAzF,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjHtR,OAAA;oBAAGkR,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAAC9S,cAAc,CAAC6C,cAAc,CAACsL,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAkD,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGDtR,OAAA;gBAAKkR,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCnR,OAAA;kBAAKkR,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDnR,OAAA;oBAAAmR,QAAA,gBACEnR,OAAA;sBAAGkR,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClEtR,OAAA;sBAAGkR,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACNtR,OAAA;oBAAKkR,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BnR,OAAA;sBAAGkR,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxDtR,OAAA;sBAAKkR,SAAS,EAAC;oBAAM;sBAAAxB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5BtR,OAAA;sBAAGkR,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxDtR,OAAA;sBAAGkR,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGPtR,OAAA;cAAKkR,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCnR,OAAA;gBACE2R,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC9M,cAAc,CAACgK,EAAE,CAAE;gBAC7CgG,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtR,OAAA;gBACE2R,OAAO,EAAEpD,iBAAkB;gBAC3B2C,SAAS,EAAC,yGAAyG;gBAAAC,QAAA,EAElHlJ,aAAa,gBACZjI,OAAA;kBAAMkR,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBACjCnR,OAAA;oBAAKkR,SAAS,EAAC,4CAA4C;oBAACe,KAAK,EAAC,4BAA4B;oBAACI,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAAAjB,QAAA,gBAC5HnR,OAAA;sBAAQkR,SAAS,EAAC,YAAY;sBAACkE,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAAC/C,MAAM,EAAC,cAAc;sBAACc,WAAW,EAAC;oBAAG;sBAAA3D,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACrGtR,OAAA;sBAAMkR,SAAS,EAAC,YAAY;sBAACmB,IAAI,EAAC,cAAc;sBAACC,CAAC,EAAC;oBAAiH;sBAAA5C,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzK,CAAC,0BAER;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEPtR,OAAA;kBAAAmR,QAAA,EAAM;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACrB;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENtR,OAAA;YAAKkR,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDtR,OAAA,CAACjB,YAAY;MACXwW,MAAM,EAAE1T,gBAAiB;MACzB2T,SAAS,EAAEzE,aAAc;MACzBW,IAAI,EAAC,qGAAmD;MACxD+D,OAAO,EAAEzE;IAAa;MAAAtB,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAACpR,EAAA,CAr1DID,kBAAkB;EAAA,QACL3C,WAAW,EACXE,WAAW,EACuED,WAAW,EAEvDA,WAAW;AAAA;AAAAmY,EAAA,GAL9DzV,kBAAkB;AAu1DxB,eAAeA,kBAAkB;AAAC,IAAAyV,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}