{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\layouts\\\\UserLayout.jsx\",\n  _s = $RefreshSig$();\nimport Header from \"../components/header/Header\";\nimport { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserLayout = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [headerHeight, setHeaderHeight] = useState(0);\n  const dispatch = useDispatch();\n  const {\n    isTuitionNotification,\n    tuitionPaymentNotPaid\n  } = useSelector(state => state.tuition);\n\n  // useEffect(() => {\n  //     dispatch(checkTuitionPaymentNotPaid());\n  // }, [dispatch]);\n\n  useEffect(() => {\n    // Get the header height after it's rendered\n    const header = document.querySelector('header');\n    if (header) {\n      setHeaderHeight(header.offsetHeight);\n\n      // Update header height on window resize\n      const handleResize = () => {\n        setHeaderHeight(header.offsetHeight);\n      };\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-row w-full bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-full justify-center sm:mt-0 mt-4\",\n      style: {\n        paddingTop: \"\".concat(headerHeight, \"px\")\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_s(UserLayout, \"4gmb7s9wEZ+CRHYdoC5HU50EhKo=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = UserLayout;\nexport default UserLayout;\nvar _c;\n$RefreshReg$(_c, \"UserLayout\");", "map": {"version": 3, "names": ["Header", "useEffect", "useState", "useDispatch", "useSelector", "jsxDEV", "_jsxDEV", "UserLayout", "_ref", "_s", "children", "headerHeight", "setHeaderHeight", "dispatch", "isTuitionNotification", "tuitionPaymentNotPaid", "state", "tuition", "header", "document", "querySelector", "offsetHeight", "handleResize", "window", "addEventListener", "removeEventListener", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "paddingTop", "concat", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/layouts/UserLayout.jsx"], "sourcesContent": ["import Header from \"../components/header/Header\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nconst UserLayout = ({ children }) => {\r\n    const [headerHeight, setHeaderHeight] = useState(0);\r\n    const dispatch = useDispatch();\r\n    const { isTuitionNotification, tuitionPaymentNotPaid } = useSelector((state) => state.tuition);\r\n\r\n\r\n    // useEffect(() => {\r\n    //     dispatch(checkTuitionPaymentNotPaid());\r\n    // }, [dispatch]);\r\n\r\n    useEffect(() => {\r\n        // Get the header height after it's rendered\r\n        const header = document.querySelector('header');\r\n        if (header) {\r\n            setHeaderHeight(header.offsetHeight);\r\n\r\n            // Update header height on window resize\r\n            const handleResize = () => {\r\n                setHeaderHeight(header.offsetHeight);\r\n            };\r\n\r\n            window.addEventListener('resize', handleResize);\r\n            return () => window.removeEventListener('resize', handleResize);\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"flex flex-row w-full bg-gray-50\">\r\n            <Header />\r\n            <div className=\"flex flex-col w-full justify-center sm:mt-0 mt-4\" style={{ paddingTop: `${headerHeight}px` }}>\r\n                {children}\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\n\r\nexport default UserLayout;"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,6BAA6B;AAChD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,UAAU,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC5B,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,qBAAqB;IAAEC;EAAsB,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;;EAG9F;EACA;EACA;;EAEAhB,SAAS,CAAC,MAAM;IACZ;IACA,MAAMiB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIF,MAAM,EAAE;MACRN,eAAe,CAACM,MAAM,CAACG,YAAY,CAAC;;MAEpC;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACvBV,eAAe,CAACM,MAAM,CAACG,YAAY,CAAC;MACxC,CAAC;MAEDE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MAC/C,OAAO,MAAMC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACnE;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIhB,OAAA;IAAKoB,SAAS,EAAC,iCAAiC;IAAAhB,QAAA,gBAC5CJ,OAAA,CAACN,MAAM;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVxB,OAAA;MAAKoB,SAAS,EAAC,kDAAkD;MAACK,KAAK,EAAE;QAAEC,UAAU,KAAAC,MAAA,CAAKtB,YAAY;MAAK,CAAE;MAAAD,QAAA,EACxGA;IAAQ;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAArB,EAAA,CAlCKF,UAAU;EAAA,QAEKJ,WAAW,EAC6BC,WAAW;AAAA;AAAA8B,EAAA,GAHlE3B,UAAU;AAqChB,eAAeA,UAAU;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}