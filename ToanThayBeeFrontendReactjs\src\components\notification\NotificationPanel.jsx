import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bell, X, Check, ExternalLink, ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { socket } from '../../services/socket';
import { useSelector, useDispatch } from 'react-redux';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import {
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  addNotification,
  updateUnreadCount,
  resetNotifications
} from '../../features/notification/notificationSlice';
import { CreditCard } from 'lucide-react';

const NotificationPanel = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const user = useSelector((state) => state.auth.user);
  const { notifications, unreadCount, loading, hasMore } = useSelector((state) => state.notifications);
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const ITEMS_PER_PAGE = 10;

  // Mark a notification as read
  const handleMarkAsRead = (id) => {
    if (!user) return;
    
    // Also update via Redux/API
    dispatch(markAsRead([id]));
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user || unreadCount === 0) return;
    // Also update via Redux/API
    dispatch(markAllAsRead());
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    handleMarkAsRead(notification.id);
    if (notification.link) {
      navigate(notification.link);
      onClose();
    }
  };

  // Load more notifications
  const loadMore = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setPage(prevPage => prevPage + 1);
  };

  // Fetch initial notifications when panel is opened
  useEffect(() => {
    if (isOpen && user) {
      // Reset page and notifications when panel is opened
      setPage(1);
      dispatch(resetNotifications());

      // Fetch first page of notifications
      dispatch(fetchNotifications({
        limit: ITEMS_PER_PAGE,
        offset: 0
      }));
    }
  }, [isOpen, user, dispatch]);

  // Load more notifications when page changes
  useEffect(() => {
    if (page > 1 && isOpen && user) {
      setIsLoadingMore(true);

      dispatch(fetchNotifications({
        limit: ITEMS_PER_PAGE,
        offset: (page - 1) * ITEMS_PER_PAGE
      }))
        .finally(() => {
          setIsLoadingMore(false);
        });
    }
  }, [page, isOpen, user, dispatch, ITEMS_PER_PAGE]);

  // Get icon based on notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'exam':
        return (
          <div className="p-1.5 md:p-2 bg-blue-100 rounded-full">
            <svg className="w-4 h-4 md:w-5 md:h-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
              <path d="M9 14l2 2 4-4"></path>
            </svg>
          </div>
        );
      case 'reminder':
        return (
          <div className="p-1.5 md:p-2 bg-yellow-100 rounded-full">
            <svg className="w-4 h-4 md:w-5 md:h-5 text-yellow-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
          </div>
        );
      case 'result':
        return (
          <div className="p-1.5 md:p-2 bg-green-100 rounded-full">
            <svg className="w-4 h-4 md:w-5 md:h-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
        );
      case 'tuition':
        return (
          <div className="p-1.5 md:p-2 bg-yellow-100 rounded-full">
            <CreditCard className="w-4 h-4 md:w-5 md:h-5 text-yellow-600" />
          </div>
        )
      default:
        return (
          <div className="p-1.5 md:p-2 bg-gray-100 rounded-full">
            <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
        );
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Mobile Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={onClose}
          />

          <motion.div
            initial={{ opacity: 0, y: -10, x: 0 }}
            animate={{ opacity: 1, y: 0, x: 0 }}
            exit={{ opacity: 0, y: -10, x: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed
              md:right-16 md:top-16 md:w-[28rem] md:max-h-[80vh] md:rounded-lg
              right-0 top-0 w-full h-full max-h-screen rounded-none
              bg-white shadow-lg border border-gray-200 z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex justify-between items-center p-3 md:p-4 border-b border-gray-100 bg-white sticky top-0 z-10">
              <h3 className="text-base md:text-lg font-semibold text-gray-800">Thông báo</h3>
              <div className="flex items-center gap-1 md:gap-2">
                {unreadCount > 0 && (
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-xs text-sky-600 hover:text-sky-700 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-sky-50 transition-colors"
                  >
                    <Check size={12} className="md:w-3.5 md:h-3.5" />
                    <span className="hidden sm:inline">Đánh dấu tất cả đã đọc</span>
                    <span className="sm:hidden">Đã đọc</span>
                  </button>
                )}
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <X size={16} className="md:w-4.5 md:h-4.5" />
                </button>
              </div>
            </div>

            {/* Notification list */}
            <div className="overflow-y-auto flex-1 overscroll-contain">
              {loading && page === 1 ? (
                <div className="p-4 md:p-6 flex justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 md:h-6 md:w-6 border-b-2 border-sky-500"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 md:p-6 text-center text-gray-500">
                  <Bell size={32} className="mx-auto mb-3 text-gray-300 md:w-10 md:h-10" />
                  <p className="text-sm md:text-base">Không có thông báo nào.</p>
                </div>
              ) : (
                <>
                  <div className="divide-y divide-gray-100">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        onClick={() => handleNotificationClick(notification)}
                        className={`p-3 md:p-4 hover:bg-gray-50 cursor-pointer transition-colors ${!notification.isRead ? 'bg-sky-50' : ''}`}
                      >
                        <div className="flex gap-2 md:gap-3">
                          <div className="flex-shrink-0">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-start gap-2">
                              <h4 className={`text-sm font-semibold line-clamp-2 ${!notification.isRead ? 'text-sky-700' : 'text-gray-800'}`}>
                                {notification.title}
                              </h4>
                              <span className="text-xs text-gray-500 flex-shrink-0">{notification.time}</span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-3">{notification.message}</p>
                            {notification.link && (
                              <div className="flex items-center mt-2 text-xs text-sky-600 hover:text-sky-700">
                                <span>Xem chi tiết</span>
                                <ExternalLink size={10} className="ml-1 md:w-3 md:h-3" />
                              </div>
                            )}
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-sky-500 rounded-full mt-2 md:hidden"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Load more button */}
                  {hasMore && (
                    <div className="p-3 text-center border-t border-gray-100 bg-white">
                      <button
                        onClick={loadMore}
                        disabled={isLoadingMore}
                        className="px-3 py-2 bg-sky-50 hover:bg-sky-100 text-sky-600 rounded-md transition-colors flex items-center gap-1.5 mx-auto text-xs md:text-sm"
                      >
                        {isLoadingMore ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-sky-500"></div>
                            <span>Đang tải...</span>
                          </>
                        ) : (
                          <>
                            <ChevronDown size={12} className="md:w-3.5 md:h-3.5" />
                            <span>Tải thêm</span>
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Footer */}
            <div className="p-3 border-t border-gray-100 text-center bg-white">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate('/notifications');
                  onClose();
                }}
                className="text-sm text-sky-600 hover:text-sky-700 px-3 py-1 rounded-md hover:bg-sky-50 transition-colors"
              >
                Xem tất cả thông báo
              </button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default NotificationPanel;
