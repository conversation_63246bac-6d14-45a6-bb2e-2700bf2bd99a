// Maintenance Mode Configuration
// Set MAINTENANCE_MODE to true to enable maintenance mode
// Set MAINTENANCE_MODE to false to disable maintenance mode

export const MAINTENANCE_CONFIG = {
    // Main toggle - set this to true to enable maintenance mode
    MAINTENANCE_MODE: false,

    // Maintenance message settings
    TITLE: "Hệ thống đang bảo trì",
    DESCRIPTION: "Chúng tôi đang thực hiện bảo trì hệ thống để mang đến trải nghiệm tốt hơn cho bạn. Vui lòng quay lại sau ít phút.",
    ESTIMATED_TIME: "15-30 phút",
    CONTACT_EMAIL: "<EMAIL>",

    // Auto-detect maintenance from API errors
    AUTO_DETECT_FROM_API: false,

    // Force disable maintenance mode (overrides localStorage)
    FORCE_DISABLE: true,
    
    // Routes that should bypass maintenance mode (for admin access)
    BYPASS_ROUTES: [
        '/admin/maintenance',
        '/api/health'
    ],
    
    // Check if current route should bypass maintenance
    shouldBypassMaintenance: (pathname) => {
        return MAINTENANCE_CONFIG.BYPASS_ROUTES.some(route => 
            pathname.startsWith(route)
        );
    }
};

// Helper functions
export const enableMaintenanceMode = () => {
    MAINTENANCE_CONFIG.MAINTENANCE_MODE = true;
};

export const disableMaintenanceMode = () => {
    MAINTENANCE_CONFIG.MAINTENANCE_MODE = false;
};

export const isMaintenanceMode = () => {
    return MAINTENANCE_CONFIG.MAINTENANCE_MODE;
};

// Check if maintenance mode should be enabled based on current conditions
export const checkMaintenanceStatus = () => {
    // Check localStorage for maintenance flag (can be set by API response)
    const maintenanceFromStorage = localStorage.getItem('maintenanceMode');
    if (maintenanceFromStorage === 'true') {
        return true;
    }
    
    // Return the configured maintenance mode
    return MAINTENANCE_CONFIG.MAINTENANCE_MODE;
};
