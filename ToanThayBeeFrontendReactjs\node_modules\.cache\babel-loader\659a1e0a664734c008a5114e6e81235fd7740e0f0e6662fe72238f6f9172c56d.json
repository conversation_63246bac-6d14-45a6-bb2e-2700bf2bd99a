{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search, DollarSign, TrendingUp } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from \"src/features/tuition/tuitionSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    page,\n    totalPages,\n    total,\n    pageSize\n  } = useSelector(state => state.tuition.pagination);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho thống kê\n  const [showStatistics, setShowStatistics] = useState(false);\n  const [statisticsData, setStatisticsData] = useState(null);\n  const [statisticsLoading, setStatisticsLoading] = useState(false);\n\n  // State cho bộ lọc\n  // const [filterMonth, setFilterMonth] = useState(\"\");\n  // const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\n  // const [filterOverdue, setFilterOverdue] = useState(\"\");\n  // const [filterClass, setFilterClass] = useState(\"\");\n  // const [filterClassId, setFilterClassId] = useState(\"\");\n\n  const {\n    filterMonth,\n    filterIsPaid,\n    filterOverdue,\n    filterClass,\n    filterClassId\n  } = useSelector(state => state.tuition);\n  const setFilterMonth = month => {\n    dispatch(setFilterMonthSlice(month));\n  };\n  const setFilterIsPaid = isPaid => {\n    dispatch(setFilterIsPaidSlice(isPaid));\n  };\n  const setFilterOverdue = overdue => {\n    dispatch(setFilterOverdueSlice(overdue));\n  };\n  const setFilterClass = classValue => {\n    dispatch(setFilterClassSlice(classValue));\n  };\n  const setFilterClassId = classId => {\n    dispatch(setFilterClassIdSlice(classId));\n  };\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\n  const [addNote, setAddNote] = useState(\"\");\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editDueDate, setEditDueDate] = useState(\"\");\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\n  const [editNote, setEditNote] = useState(\"\");\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n\n  // useEffect(() => {\n  //   if (!didInit) {\n  //     dispatch(resetFilters());\n  //     setDidInit(true);\n  //   }\n  // }, [dispatch, didInit]);\n\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      isPaid: filterIsPaid,\n      // Thay đổi từ status thành isPaid\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        isPaid: addIsPaid,\n        // Sử dụng isPaid thay vì status\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddIsPaid(false);\n      setAddNote(\"\");\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  useEffect(() => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: page,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      isPaid: filterIsPaid,\n      // Thay đổi từ status thành isPaid\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, page, pageSize]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : \"\");\n      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status\n      setEditNote(payment.note || \"\");\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddIsPaid(false); // Reset isPaid thay vì status\n    setAddNote(\"\");\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditPaymentDate(\"\");\n    setEditDueDate(\"\");\n    setEditIsPaid(false); // Reset isPaid thay vì status\n    setEditNote(\"\");\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate due date (required)\n    if (!editDueDate) {\n      errors.editDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        isPaid: editIsPaid,\n        // Sử dụng isPaid thay vì status\n        note: editNote,\n        dueDate: editDueDate\n      };\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        isPaid: filterIsPaid,\n        // Sử dụng isPaid thay vì status\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n\n  // Hàm xử lý thao tác nhanh đánh dấu đã thanh toán\n  const handleQuickPayment = async (paymentId, currentIsPaid) => {\n    try {\n      const today = new Date().toISOString().split('T')[0]; // Ngày hiện tại\n\n      const paymentData = {\n        isPaid: !currentIsPaid,\n        // Toggle trạng thái\n        paymentDate: !currentIsPaid ? today : null // Nếu đánh dấu đã thanh toán thì set ngày hiện tại, nếu bỏ đánh dấu thì xóa ngày\n      };\n      await dispatch(updateTuitionPayment({\n        id: paymentId,\n        paymentData\n      }));\n\n      // Refresh data\n      // dispatch(fetchTuitionPayments({\n      //   page: page,\n      //   pageSize,\n      //   search: inputValue,\n      //   sortOrder: \"DESC\",\n      //   isPaid: filterIsPaid,\n      //   month: filterMonth,\n      //   overdue: filterOverdue,\n      //   userClass: filterClass\n      // }));\n    } catch (error) {\n      console.error(\"Error updating payment status:\", error);\n    }\n  };\n\n  // Hàm xử lý hiển thị thống kê\n  const handleShowStatistics = async () => {\n    setStatisticsLoading(true);\n    try {\n      // Sử dụng fetchTuitionStatistics từ Redux với filter parameters\n      const params = {\n        userClass: filterClass || undefined,\n        startMonth: filterMonth || undefined,\n        endMonth: filterMonth || undefined\n      };\n      await dispatch(fetchTuitionStatistics(params));\n\n      // Sử dụng dữ liệu từ tuitionStatistics (đã được cập nhật trong Redux store)\n      if (tuitionStatistics) {\n        const {\n          totalStatistics,\n          monthlyStatistics,\n          classStatistics\n        } = tuitionStatistics;\n\n        // Format dữ liệu cho UI\n        const formattedData = {\n          overview: {\n            totalStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.totalStudents) || 0,\n            paidStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.paidStudents) || 0,\n            unpaidStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.unpaidStudents) || 0,\n            paymentRate: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.paymentRate) || 0\n          },\n          monthlyStats: (monthlyStatistics === null || monthlyStatistics === void 0 ? void 0 : monthlyStatistics.map(stat => ({\n            month: stat.month,\n            monthFormatted: stat.monthFormatted,\n            total: stat.totalStudents,\n            paid: stat.paidStudents,\n            unpaid: stat.unpaidStudents,\n            paymentRate: stat.paymentRate\n          }))) || [],\n          classStats: (classStatistics === null || classStatistics === void 0 ? void 0 : classStatistics.map(stat => ({\n            class: stat.userClass,\n            total: stat.totalStudents,\n            paid: stat.paidStudents,\n            unpaid: stat.unpaidStudents,\n            paymentRate: stat.paymentRate\n          }))) || [],\n          apiData: tuitionStatistics // Lưu toàn bộ API data\n        };\n        setStatisticsData(formattedData);\n        setShowStatistics(true);\n      } else {\n        console.error(\"No tuitionStatistics data available\");\n        // Fallback: Sử dụng dữ liệu local nếu API không trả về\n        const totalStudents = tuitionPayments.length;\n        const paidStudents = tuitionPayments.filter(payment => payment.isPaid).length;\n        const unpaidStudents = totalStudents - paidStudents;\n        const paymentRate = totalStudents > 0 ? (paidStudents / totalStudents * 100).toFixed(1) : 0;\n        setStatisticsData({\n          overview: {\n            totalStudents,\n            paidStudents,\n            unpaidStudents,\n            paymentRate\n          },\n          monthlyStats: [],\n          classStats: [],\n          apiData: null\n        });\n        setShowStatistics(true);\n      }\n    } catch (error) {\n      console.error(\"Error fetching statistics:\", error);\n    } finally {\n      setStatisticsLoading(false);\n    }\n  };\n\n  // getStatusBadge function removed - replaced with inline status display\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 671,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 677,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 684,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 683,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 690,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 689,\n    columnNumber: 5\n  }, this);\n  const iconStatistics = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(BarChart2, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 695,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterIsPaid,\n              onChange: e => setFilterIsPaid(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconStatistics,\n            text: 'Xem thống kê',\n            onClick: handleShowStatistics\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this), tuitionPayments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 827,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 826,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"(S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i c\\u1EE7a ph\\u1EE5 huynh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"(S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i c\\u1EE7a h\\u1ECDc sinh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ghi ch\\xFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user, _payment$user2, _payment$user3, _payment$user4, _payment$user5, _payment$user6;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (page - 1) * pageSize + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.lastName) + \" \" + ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4 align-top\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col gap-y-0.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_payment$user5 = payment.user) === null || _payment$user5 === void 0 ? void 0 : _payment$user5.phone) || \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_payment$user6 = payment.user) === null || _payment$user6 === void 0 ? void 0 : _payment$user6.password) || \"Không có mật khẩu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(payment.isPaid ? 'bg-green-100 text-green-800' : payment.isOverdue ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                  children: payment.isPaid ? 'Đã thanh toán' : payment.isOverdue ? 'Quá hạn' : 'Chưa thanh toán'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  title: payment.note,\n                  children: payment.note ? payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2 items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center cursor-pointer\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: payment.isPaid,\n                      onChange: () => handleQuickPayment(payment.id, payment.isPaid),\n                      className: \"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer\",\n                      title: payment.isPaid ? \"Bỏ đánh dấu đã thanh toán\" : \"Đánh dấu đã thanh toán\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 906,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 905,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleView(payment.id, payment.userId, payment.month),\n                      className: \"text-blue-500 hover:text-blue-700\",\n                      title: \"Xem chi ti\\u1EBFt\",\n                      children: /*#__PURE__*/_jsxDEV(Eye, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 922,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 917,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEdit(payment.id),\n                      className: \"text-yellow-500 hover:text-yellow-700\",\n                      title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                      children: /*#__PURE__*/_jsxDEV(Edit, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 929,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 924,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDelete(payment.id),\n                      className: \"text-red-500 hover:text-red-700\",\n                      title: \"X\\xF3a\",\n                      children: /*#__PURE__*/_jsxDEV(Trash, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 19\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 9\n    }, this), viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: page,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: total,\n        limit: pageSize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 949,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 9\n    }, this), showStatistics && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200 flex justify-between items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-800\",\n            children: \"Th\\u1ED1ng k\\xEA thanh to\\xE1n h\\u1ECDc ph\\xED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowStatistics(false),\n            className: \"text-gray-500 hover:text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 13\n        }, this), statisticsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 15\n        }, this) : statisticsData ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-8 w-8 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-blue-600\",\n                    children: \"T\\u1ED5ng h\\u1ECDc sinh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-blue-900\",\n                    children: statisticsData.overview.totalStudents\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"h-8 w-8 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-green-600\",\n                    children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-green-900\",\n                    children: statisticsData.overview.paidStudents\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"h-8 w-8 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1003,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-red-600\",\n                    children: \"Ch\\u01B0a thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-red-900\",\n                    children: statisticsData.overview.unpaidStudents\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-8 w-8 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-purple-600\",\n                    children: \"T\\u1EF7 l\\u1EC7 thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-purple-900\",\n                    children: [statisticsData.overview.paymentRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 17\n          }, this), statisticsData.monthlyStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4\",\n              children: \"Th\\u1ED1ng k\\xEA theo th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"Th\\xE1ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1ED5ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"\\u0110\\xE3 \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1032,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"Ch\\u01B0a \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1033,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1EF7 l\\u1EC7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  className: \"divide-y divide-gray-200\",\n                  children: statisticsData.monthlyStats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.monthFormatted\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1040,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1041,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-green-600 font-medium\",\n                      children: stat.paid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1042,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-red-600 font-medium\",\n                      children: stat.unpaid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1043,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: [stat.total > 0 ? (stat.paid / stat.total * 100).toFixed(1) : 0, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1044,\n                      columnNumber: 31\n                    }, this)]\n                  }, stat.month, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 19\n          }, this), statisticsData.classStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4\",\n              children: \"Th\\u1ED1ng k\\xEA theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"L\\u1EDBp\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1063,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1ED5ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1064,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"\\u0110\\xE3 \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1065,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"Ch\\u01B0a \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1066,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1EF7 l\\u1EC7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1067,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  className: \"divide-y divide-gray-200\",\n                  children: statisticsData.classStats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.class\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1074,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-green-600 font-medium\",\n                      children: stat.paid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1075,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-red-600 font-medium\",\n                      children: stat.unpaid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: [stat.total > 0 ? (stat.paid / stat.total * 100).toFixed(1) : 0, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1077,\n                      columnNumber: 31\n                    }, this)]\n                  }, stat.class, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1072,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1057,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 text-center text-gray-500\",\n          children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u th\\u1ED1ng k\\xEA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 962,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 961,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1111,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1107,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1100,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1118,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1122,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1123,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1154,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1144,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1161,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1169,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1169,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1177,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1168,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"addIsPaid\",\n                  checked: addIsPaid,\n                  onChange: e => setAddIsPaid(e.target.checked),\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"addIsPaid\",\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1192,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1197,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1208,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1120,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1229,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1229,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1230,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1239,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1238,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1228,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1244,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1244,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1251,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1252,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1253,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1254,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1245,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1258,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1263,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1263,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1272,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1262,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1278,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1279,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1277,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1287,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1224,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1300,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1305,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1303,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editDueDate,\n                onChange: e => setEditDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 21\n              }, this), formErrors.editDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1322,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1321,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"editIsPaid\",\n                  checked: editIsPaid,\n                  onChange: e => setEditIsPaid(e.target.checked),\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1329,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"editIsPaid\",\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1342,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1343,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1341,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1353,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1352,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1356,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1302,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1299,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1371,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1370,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1379,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1383,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1384,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1382,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1377,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1395,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1395,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1396,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1396,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1397,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1397,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1398,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1398,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1394,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1392,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1404,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1406,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1406,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1407,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1407,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1408,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1409,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1411,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1412,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1410,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1423,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1423,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1424,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1424,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1425,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1425,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1405,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1403,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1432,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1437,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1438,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1436,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1442,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1442,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1443,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1443,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1444,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1444,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1435,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1449,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1448,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1431,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1459,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1460,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1458,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1463,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1464,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1465,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1466,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1462,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1457,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1456,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1375,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1474,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1473,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1374,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1483,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1368,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1115,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1099,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1493,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 701,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"V7Isoyd76mT6tv3+TzHLWIIauKg=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "DollarSign", "TrendingUp", "AdminLayout", "FunctionBarAdmin", "Chart", "setFilterClassIdSlice", "setFilterMonthSlice", "setFilterIsPaidSlice", "setFilterOverdueSlice", "setFilterClassSlice", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "page", "totalPages", "total", "pageSize", "pagination", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "showStatistics", "setShowStatistics", "statisticsData", "setStatisticsData", "statisticsLoading", "setStatisticsLoading", "filterMonth", "filterIsPaid", "filterOverdue", "filterClass", "filterClassId", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "month", "setFilterIsPaid", "isPaid", "setFilterOverdue", "overdue", "setFilterClass", "classValue", "setFilterClassId", "classId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addIsPaid", "setAddIsPaid", "addNote", "setAddNote", "editId", "setEditId", "editPaymentDate", "setEditPaymentDate", "editDueDate", "setEditDueDate", "editIsPaid", "setEditIsPaid", "editNote", "setEditNote", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "sortOrder", "userClass", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "dueDate", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "error", "console", "submit", "handleEdit", "response", "unwrap", "payment", "data", "Date", "toISOString", "split", "handleView", "handleAdd", "today", "formattedDate", "year", "getFullYear", "getMonth", "formattedMonth", "concat", "handleBatchAdd", "handleCreateBatchTuition", "validateEditForm", "handleUpdateTuitionPayment", "validateBatchTuitionForm", "isNaN", "Number", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "expectedAmount", "handleDelete", "confirmDelete", "cancelDelete", "handleQuickPayment", "paymentId", "currentIsPaid", "handleShowStatistics", "params", "undefined", "totalStatistics", "monthlyStatistics", "classStatistics", "formattedData", "overview", "totalStudents", "paidStudents", "unpaidStudents", "paymentRate", "monthlyStats", "map", "stat", "monthFormatted", "paid", "unpaid", "classStats", "class", "apiData", "filter", "toFixed", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "className", "children", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "iconStatistics", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "type", "placeholder", "value", "onChange", "target", "Array", "from", "_", "i", "monthStr", "selectedClassId", "onSelect", "onClear", "strokeWidth", "index", "_payment$user", "_payment$user2", "_payment$user3", "_payment$user4", "_payment$user5", "_payment$user6", "user", "highSchool", "phone", "password", "toLocaleDateString", "isOverdue", "title", "substring", "checked", "currentPage", "onPageChange", "totalItems", "limit", "onSubmit", "role", "htmlFor", "rows", "disabled", "required", "ref", "paidAmount", "status", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search, DollarSign, TrendingUp } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\nimport { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from \"src/features/tuition/tuitionSlice\";\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { page, totalPages, total, pageSize } = useSelector(\r\n    (state) => state.tuition.pagination\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho thống kê\r\n  const [showStatistics, setShowStatistics] = useState(false);\r\n  const [statisticsData, setStatisticsData] = useState(null);\r\n  const [statisticsLoading, setStatisticsLoading] = useState(false);\r\n\r\n  // State cho bộ lọc\r\n  // const [filterMonth, setFilterMonth] = useState(\"\");\r\n  // const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\r\n  // const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  // const [filterClass, setFilterClass] = useState(\"\");\r\n  // const [filterClassId, setFilterClassId] = useState(\"\");\r\n\r\n  const { filterMonth, filterIsPaid, filterOverdue, filterClass, filterClassId } = useSelector(\r\n    (state) => state.tuition\r\n  );\r\n\r\n  const setFilterMonth = (month) => {\r\n    dispatch(setFilterMonthSlice(month));\r\n  };\r\n\r\n  const setFilterIsPaid = (isPaid) => {\r\n    dispatch(setFilterIsPaidSlice(isPaid));\r\n  };\r\n\r\n  const setFilterOverdue = (overdue) => {\r\n    dispatch(setFilterOverdueSlice(overdue));\r\n  };\r\n\r\n  const setFilterClass = (classValue) => {\r\n    dispatch(setFilterClassSlice(classValue));\r\n  };\r\n\r\n  const setFilterClassId = (classId) => {\r\n    dispatch(setFilterClassIdSlice(classId));\r\n  };\r\n\r\n\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\r\n  const [addNote, setAddNote] = useState(\"\");\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editDueDate, setEditDueDate] = useState(\"\");\r\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\r\n  const [editNote, setEditNote] = useState(\"\");\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  // useEffect(() => {\r\n  //   if (!didInit) {\r\n  //     dispatch(resetFilters());\r\n  //     setDidInit(true);\r\n  //   }\r\n  // }, [dispatch, didInit]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        isPaid: addIsPaid, // Sử dụng isPaid thay vì status\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddIsPaid(false);\r\n      setAddNote(\"\");\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: page, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, page, pageSize]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : \"\");\r\n      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status\r\n      setEditNote(payment.note || \"\");\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddIsPaid(false); // Reset isPaid thay vì status\r\n    setAddNote(\"\");\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditPaymentDate(\"\");\r\n    setEditDueDate(\"\");\r\n    setEditIsPaid(false); // Reset isPaid thay vì status\r\n    setEditNote(\"\");\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate due date (required)\r\n    if (!editDueDate) {\r\n      errors.editDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        isPaid: editIsPaid, // Sử dụng isPaid thay vì status\r\n        note: editNote,\r\n        dueDate: editDueDate\r\n      };\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Sử dụng isPaid thay vì status\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  // Hàm xử lý thao tác nhanh đánh dấu đã thanh toán\r\n  const handleQuickPayment = async (paymentId, currentIsPaid) => {\r\n    try {\r\n      const today = new Date().toISOString().split('T')[0]; // Ngày hiện tại\r\n\r\n      const paymentData = {\r\n        isPaid: !currentIsPaid, // Toggle trạng thái\r\n        paymentDate: !currentIsPaid ? today : null, // Nếu đánh dấu đã thanh toán thì set ngày hiện tại, nếu bỏ đánh dấu thì xóa ngày\r\n      };\r\n\r\n      await dispatch(updateTuitionPayment({\r\n        id: paymentId,\r\n        paymentData\r\n      }));\r\n\r\n      // Refresh data\r\n      // dispatch(fetchTuitionPayments({\r\n      //   page: page,\r\n      //   pageSize,\r\n      //   search: inputValue,\r\n      //   sortOrder: \"DESC\",\r\n      //   isPaid: filterIsPaid,\r\n      //   month: filterMonth,\r\n      //   overdue: filterOverdue,\r\n      //   userClass: filterClass\r\n      // }));\r\n    } catch (error) {\r\n      console.error(\"Error updating payment status:\", error);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý hiển thị thống kê\r\n  const handleShowStatistics = async () => {\r\n    setStatisticsLoading(true);\r\n    try {\r\n      // Sử dụng fetchTuitionStatistics từ Redux với filter parameters\r\n      const params = {\r\n        userClass: filterClass || undefined,\r\n        startMonth: filterMonth || undefined,\r\n        endMonth: filterMonth || undefined\r\n      };\r\n\r\n      await dispatch(fetchTuitionStatistics(params));\r\n\r\n      // Sử dụng dữ liệu từ tuitionStatistics (đã được cập nhật trong Redux store)\r\n      if (tuitionStatistics) {\r\n        const { totalStatistics, monthlyStatistics, classStatistics } = tuitionStatistics;\r\n\r\n        // Format dữ liệu cho UI\r\n        const formattedData = {\r\n          overview: {\r\n            totalStudents: totalStatistics?.totalStudents || 0,\r\n            paidStudents: totalStatistics?.paidStudents || 0,\r\n            unpaidStudents: totalStatistics?.unpaidStudents || 0,\r\n            paymentRate: totalStatistics?.paymentRate || 0\r\n          },\r\n          monthlyStats: monthlyStatistics?.map(stat => ({\r\n            month: stat.month,\r\n            monthFormatted: stat.monthFormatted,\r\n            total: stat.totalStudents,\r\n            paid: stat.paidStudents,\r\n            unpaid: stat.unpaidStudents,\r\n            paymentRate: stat.paymentRate\r\n          })) || [],\r\n          classStats: classStatistics?.map(stat => ({\r\n            class: stat.userClass,\r\n            total: stat.totalStudents,\r\n            paid: stat.paidStudents,\r\n            unpaid: stat.unpaidStudents,\r\n            paymentRate: stat.paymentRate\r\n          })) || [],\r\n          apiData: tuitionStatistics // Lưu toàn bộ API data\r\n        };\r\n\r\n        setStatisticsData(formattedData);\r\n        setShowStatistics(true);\r\n      } else {\r\n        console.error(\"No tuitionStatistics data available\");\r\n        // Fallback: Sử dụng dữ liệu local nếu API không trả về\r\n        const totalStudents = tuitionPayments.length;\r\n        const paidStudents = tuitionPayments.filter(payment => payment.isPaid).length;\r\n        const unpaidStudents = totalStudents - paidStudents;\r\n        const paymentRate = totalStudents > 0 ? ((paidStudents / totalStudents) * 100).toFixed(1) : 0;\r\n\r\n        setStatisticsData({\r\n          overview: {\r\n            totalStudents,\r\n            paidStudents,\r\n            unpaidStudents,\r\n            paymentRate\r\n          },\r\n          monthlyStats: [],\r\n          classStats: [],\r\n          apiData: null\r\n        });\r\n        setShowStatistics(true);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching statistics:\", error);\r\n    } finally {\r\n      setStatisticsLoading(false);\r\n    }\r\n  };\r\n\r\n  // getStatusBadge function removed - replaced with inline status display\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconStatistics = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <BarChart2 size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterIsPaid}\r\n                onChange={(e) => setFilterIsPaid(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"true\">Đã thanh toán</option>\r\n                <option value=\"false\">Chưa thanh toán</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n            <ButtonFunctionBarAdmin icon={iconStatistics} text={'Xem thống kê'} onClick={handleShowStatistics} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {tuitionPayments.length === 0 ? (\r\n        <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n          <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n            <thead className=\"bg-gray-100\">\r\n              <tr>\r\n                <th className=\"py-3 px-4 text-left\">STT</th>\r\n                <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                <th className=\"py-3 px-4 text-left\">\r\n                  <div className=\"flex flex-col\">\r\n                    <span className=\"font-medium\">Số điện thoại</span>\r\n                    <span className=\"text-xs text-gray-500\">(Số điện thoại của phụ huynh)</span>\r\n                    <span className=\"text-xs text-gray-500\">(Số điện thoại của học sinh)</span>\r\n                  </div>\r\n                </th>\r\n                <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                <th className=\"py-3 px-4 text-left\">Ghi chú</th>\r\n                <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {tuitionPayments.map((payment, index) => (\r\n                <tr\r\n                  key={payment.id}\r\n                  className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                >\r\n                  <td className=\"py-3 px-4\">\r\n                    {(page - 1) * pageSize + index + 1}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4 align-top\">\r\n                    <div className=\"flex flex-col gap-y-0.5\">\r\n                      <span>{payment.user?.phone || \"N/A\"}</span>\r\n                      <span>{payment.user?.password || \"Không có mật khẩu\"}</span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.paymentDate\r\n                      ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                      : \"Chưa thanh toán\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${payment.isPaid\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : payment.isOverdue\r\n                        ? 'bg-red-100 text-red-800'\r\n                        : 'bg-yellow-100 text-yellow-800'\r\n                      }`}>\r\n                      {payment.isPaid\r\n                        ? 'Đã thanh toán'\r\n                        : payment.isOverdue\r\n                          ? 'Quá hạn'\r\n                          : 'Chưa thanh toán'\r\n                      }\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <span className=\"text-sm text-gray-600\" title={payment.note}>\r\n                      {payment.note ? (payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note) : '-'}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <div className=\"flex space-x-2 items-center\">\r\n                      {/* Quick payment checkbox */}\r\n                      <div className=\"flex items-center cursor-pointer\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={payment.isPaid}\r\n                          onChange={() => handleQuickPayment(payment.id, payment.isPaid)}\r\n                          className=\"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer\"\r\n                          title={payment.isPaid ? \"Bỏ đánh dấu đã thanh toán\" : \"Đánh dấu đã thanh toán\"}\r\n                        />\r\n                      </div>\r\n\r\n                      {/* Action buttons */}\r\n                      <div className=\"flex space-x-1\">\r\n                        <button\r\n                          onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                          className=\"text-blue-500 hover:text-blue-700\"\r\n                          title=\"Xem chi tiết\"\r\n                        >\r\n                          <Eye size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleEdit(payment.id)}\r\n                          className=\"text-yellow-500 hover:text-yellow-700\"\r\n                          title=\"Chỉnh sửa\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(payment.id)}\r\n                          className=\"text-red-500 hover:text-red-700\"\r\n                          title=\"Xóa\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={page}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={total}\r\n            limit={pageSize}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n      {/* Statistics Modal */}\r\n      {showStatistics && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6 border-b border-gray-200 flex justify-between items-center\">\r\n              <h2 className=\"text-xl font-semibold text-gray-800\">Thống kê thanh toán học phí</h2>\r\n              <button\r\n                onClick={() => setShowStatistics(false)}\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n              >\r\n                <X size={24} />\r\n              </button>\r\n            </div>\r\n\r\n            {statisticsLoading ? (\r\n              <div className=\"flex justify-center items-center h-64\">\r\n                <LoadingSpinner />\r\n              </div>\r\n            ) : statisticsData ? (\r\n              <div className=\"p-6\">\r\n                {/* Overview Statistics */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\r\n                  <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <Users className=\"h-8 w-8 text-blue-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-blue-600\">Tổng học sinh</p>\r\n                        <p className=\"text-2xl font-bold text-blue-900\">{statisticsData.overview.totalStudents}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <DollarSign className=\"h-8 w-8 text-green-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-green-600\">Đã thanh toán</p>\r\n                        <p className=\"text-2xl font-bold text-green-900\">{statisticsData.overview.paidStudents}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-red-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <AlertCircle className=\"h-8 w-8 text-red-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-red-600\">Chưa thanh toán</p>\r\n                        <p className=\"text-2xl font-bold text-red-900\">{statisticsData.overview.unpaidStudents}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-purple-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <TrendingUp className=\"h-8 w-8 text-purple-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-purple-600\">Tỷ lệ thanh toán</p>\r\n                        <p className=\"text-2xl font-bold text-purple-900\">{statisticsData.overview.paymentRate}%</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Monthly Statistics */}\r\n                {statisticsData.monthlyStats.length > 0 && (\r\n                  <div className=\"mb-8\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Thống kê theo tháng</h3>\r\n                    <div className=\"overflow-x-auto\">\r\n                      <table className=\"min-w-full bg-white border border-gray-200 rounded-lg\">\r\n                        <thead className=\"bg-gray-50\">\r\n                          <tr>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tháng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tổng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Đã đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Chưa đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tỷ lệ</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody className=\"divide-y divide-gray-200\">\r\n                          {statisticsData.monthlyStats.map((stat, index) => (\r\n                            <tr key={stat.month} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.monthFormatted}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.total}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-green-600 font-medium\">{stat.paid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-red-600 font-medium\">{stat.unpaid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">\r\n                                {stat.total > 0 ? ((stat.paid / stat.total) * 100).toFixed(1) : 0}%\r\n                              </td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Class Statistics */}\r\n                {statisticsData.classStats.length > 0 && (\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Thống kê theo lớp</h3>\r\n                    <div className=\"overflow-x-auto\">\r\n                      <table className=\"min-w-full bg-white border border-gray-200 rounded-lg\">\r\n                        <thead className=\"bg-gray-50\">\r\n                          <tr>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Lớp</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tổng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Đã đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Chưa đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tỷ lệ</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody className=\"divide-y divide-gray-200\">\r\n                          {statisticsData.classStats.map((stat, index) => (\r\n                            <tr key={stat.class} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.class}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.total}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-green-600 font-medium\">{stat.paid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-red-600 font-medium\">{stat.unpaid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">\r\n                                {stat.total > 0 ? ((stat.paid / stat.total) * 100).toFixed(1) : 0}%\r\n                              </td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"p-6 text-center text-gray-500\">\r\n                Không có dữ liệu thống kê\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  {/* Removed expectedAmount and paidAmount fields - no longer needed */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái thanh toán</label>\r\n                    <div className=\"flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"addIsPaid\"\r\n                        checked={addIsPaid}\r\n                        onChange={(e) => setAddIsPaid(e.target.checked)}\r\n                        className=\"mr-2\"\r\n                      />\r\n                      <label htmlFor=\"addIsPaid\" className=\"text-sm text-gray-700\">\r\n                        Đã thanh toán\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editDueDate}\r\n                      onChange={(e) => setEditDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.editDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái thanh toán</label>\r\n                    <div className=\"flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"editIsPaid\"\r\n                        checked={editIsPaid}\r\n                        onChange={(e) => setEditIsPaid(e.target.checked)}\r\n                        className=\"mr-2\"\r\n                      />\r\n                      <label htmlFor=\"editIsPaid\" className=\"text-sm text-gray-700\">\r\n                        Đã thanh toán\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,QACvB,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,QAAQ,cAAc;AACjJ,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjK,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAGtD,WAAW,CAAC,CAAC;EAC9B,MAAMuD,QAAQ,GAAGrD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsD,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAG3D,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,IAAI;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGjE,WAAW,CACtD4D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACK,UAC3B,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC5D;EACA,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM;IAAEsF,WAAW;IAAEC,YAAY;IAAEC,aAAa;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGvF,WAAW,CACzF4D,KAAK,IAAKA,KAAK,CAACC,OACnB,CAAC;EAED,MAAM2B,cAAc,GAAIC,KAAK,IAAK;IAChCpC,QAAQ,CAACb,mBAAmB,CAACiD,KAAK,CAAC,CAAC;EACtC,CAAC;EAED,MAAMC,eAAe,GAAIC,MAAM,IAAK;IAClCtC,QAAQ,CAACZ,oBAAoB,CAACkD,MAAM,CAAC,CAAC;EACxC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpCxC,QAAQ,CAACX,qBAAqB,CAACmD,OAAO,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrC1C,QAAQ,CAACV,mBAAmB,CAACoD,UAAU,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC5C,QAAQ,CAACd,qBAAqB,CAAC0D,OAAO,CAAC,CAAC;EAC1C,CAAC;EAGD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuG,cAAc,EAAEC,iBAAiB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyG,cAAc,EAAEC,iBAAiB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAAC2G,QAAQ,EAAEC,WAAW,CAAC,GAAG5G,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6G,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+G,QAAQ,EAAEC,WAAW,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMiH,eAAe,GAAGhH,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMiH,aAAa,GAAGjH,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkH,oBAAoB,GAAGlH,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMmH,kBAAkB,GAAGnH,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACoH,UAAU,EAAEC,aAAa,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuH,WAAW,EAAEC,cAAc,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC2H,YAAY,EAAEC,eAAe,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6H,UAAU,EAAEC,aAAa,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+H,SAAS,EAAEC,YAAY,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiI,UAAU,EAAEC,aAAa,CAAC,GAAGlI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmI,YAAY,EAAEC,eAAe,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACqI,SAAS,EAAEC,YAAY,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyI,QAAQ,EAAEC,WAAW,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2I,cAAc,EAAEC,iBAAiB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6I,UAAU,EAAEC,aAAa,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+I,SAAS,EAAEC,YAAY,CAAC,GAAGhJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiJ,OAAO,EAAEC,UAAU,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACmJ,MAAM,EAAEC,SAAS,CAAC,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuJ,WAAW,EAAEC,cAAc,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyJ,UAAU,EAAEC,aAAa,CAAC,GAAG1J,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC2J,QAAQ,EAAEC,WAAW,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM,CAAC6J,WAAW,EAAEC,cAAc,CAAC,GAAG9J,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhK,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACiK,WAAW,EAAEC,cAAc,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmK,aAAa,EAAEC,gBAAgB,CAAC,GAAGpK,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMqK,mBAAmB,GAAGpK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA;EACA;EACA;EACA;EACA;;EAEAF,SAAS,CAAC,MAAM;IACdyD,QAAQ,CAACpC,WAAW,CAAC,EAAE,CAAC,CAAC;IACzBoC,QAAQ,CAACrC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAACqC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM8G,iBAAiB,GAAIC,SAAS,IAAK;IACvCpE,gBAAgB,CAACoE,SAAS,CAACC,EAAE,CAAC;IAC9BlE,kBAAkB,CAACiE,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCvE,gBAAgB,CAAC,EAAE,CAAC;IACpBG,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAIC,QAAQ,IAAK;IACrCpE,iBAAiB,CAACoE,QAAQ,CAACJ,EAAE,CAAC;IAC9B9D,iBAAiB,CAACkE,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCvE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzBxH,QAAQ,CACN/C,oBAAoB,CAAC;MACnBwK,MAAM,EAAE3G,UAAU;MAClBL,IAAI,EAAE,CAAC;MAAE;MACTG,QAAQ;MACR8G,SAAS,EAAE,MAAM;MACjBpF,MAAM,EAAEP,YAAY;MAAE;MACtBK,KAAK,EAAEN,WAAW;MAClBU,OAAO,EAAER,aAAa;MACtB2F,SAAS,EAAE1F,WAAW;MACtBW,OAAO,EAAEV;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAM0F,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAClD,SAAS,EAAE;MACdkD,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAAC/C,QAAQ,EAAE;MACb8C,MAAM,CAAC3F,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC6F,IAAI,CAAChD,QAAQ,CAAC,EAAE;MAC1C8C,MAAM,CAAC3F,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAACiD,UAAU,EAAE;MACf0C,MAAM,CAACG,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAIC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM0D,WAAW,GAAG;QAClBN,MAAM,EAAEnD,SAAS;QACjBzC,KAAK,EAAE6C,QAAQ;QACf3C,MAAM,EAAEiD,SAAS;QAAE;QACnBgD,IAAI,EAAE9C,OAAO;QACbyC,OAAO,EAAE7C;MACX,CAAC;;MAED;MACA,IAAIF,cAAc,EAAE;QAClBmD,WAAW,CAACE,WAAW,GAAGrD,cAAc;MAC1C;;MAEA;MACA,MAAMnF,QAAQ,CAAC7C,oBAAoB,CAACmL,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjBzI,QAAQ,CAAC/C,oBAAoB,CAAC;QAC5BwD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACR6G,MAAM,EAAE3G,UAAU;QAClB4G,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA5C,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,KAAK,CAAC;MACnBE,UAAU,CAAC,EAAE,CAAC;IAEhB,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDrI,SAAS,CAAC,MAAM;IACdyD,QAAQ,CACN/C,oBAAoB,CAAC;MACnBwK,MAAM,EAAE3G,UAAU;MAClBL,IAAI,EAAEA,IAAI;MAAE;MACZG,QAAQ;MACR8G,SAAS,EAAE,MAAM;MACjBpF,MAAM,EAAEP,YAAY;MAAE;MACtBK,KAAK,EAAEN,WAAW;MAClBU,OAAO,EAAER,aAAa;MACtB2F,SAAS,EAAE1F;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAACjC,QAAQ,EAAES,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAE9B,MAAMiI,UAAU,GAAG,MAAO7B,EAAE,IAAK;IAC/BpC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMkE,QAAQ,GAAG,MAAM9I,QAAQ,CAACzC,4BAA4B,CAACyJ,EAAE,CAAC,CAAC,CAAC+B,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAACG,IAAI;;MAE7B;MACArD,SAAS,CAACoB,EAAE,CAAC;MACblB,kBAAkB,CAACkD,OAAO,CAACR,WAAW,GAAG,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACW,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxGpD,cAAc,CAACgD,OAAO,CAACd,OAAO,GAAG,IAAIgB,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MAC5FlD,aAAa,CAAC8C,OAAO,CAAC1G,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC;MACxC8D,WAAW,CAAC4C,OAAO,CAACT,IAAI,IAAI,EAAE,CAAC;;MAE/B;MACAhH,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOqH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACR9D,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMyE,UAAU,GAAG,MAAAA,CAAOrC,EAAE,EAAEgB,MAAM,EAAE5F,KAAK,KAAK;IAC9CsE,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM1G,QAAQ,CAACzC,4BAA4B,CAACyJ,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACAzF,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOqH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRhC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM4C,SAAS,GAAGA,CAAA,KAAM;IACtB;IACAxE,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IACdhB,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAM6E,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;IACxB,MAAMM,aAAa,GAAGD,KAAK,CAACJ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD9D,aAAa,CAACkE,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;IAChC,MAAMtH,KAAK,GAAGmH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAC,MAAA,CAAMJ,IAAI,OAAAI,MAAA,CAAIzH,KAAK,GAAG,EAAE,OAAAyH,MAAA,CAAOzH,KAAK,IAAKA,KAAK,CAAE;IACpE8C,WAAW,CAAC0E,cAAc,CAAC;;IAE3B;IACArI,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyI,cAAc,GAAGA,CAAA,KAAM;IAC3BvI,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0I,wBAAwB,GAAGA,CAAA,KAAM;IACrCxI,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EACD,MAAMoH,eAAe,GAAGA,CAAA,KAAM;IAC5BpH,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACAuC,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBoB,SAAS,CAAC,IAAI,CAAC;IACfE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtBE,WAAW,CAAC,EAAE,CAAC;IACf;IACAE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1B9B,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtB8B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMjC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAChC,WAAW,EAAE;MAChBgC,MAAM,CAAChC,WAAW,GAAG,oCAAoC;IAC3D;IAEA,OAAOgC,MAAM;EACf,CAAC;;EAED;EACA,MAAMkC,0BAA0B,GAAG,MAAOpC,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAGiC,gBAAgB,CAAC,CAAC;IACjC,IAAI7B,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM0D,WAAW,GAAG;QAClBhG,MAAM,EAAE2D,UAAU;QAAE;QACpBsC,IAAI,EAAEpC,QAAQ;QACd+B,OAAO,EAAEnC;MACX,CAAC;;MAED;MACA,IAAIF,eAAe,EAAE;QACnByC,WAAW,CAACE,WAAW,GAAG3C,eAAe;MAC3C;;MAEA;MACA,MAAM7F,QAAQ,CAAC1C,oBAAoB,CAAC;QAClC0J,EAAE,EAAErB,MAAM;QACV2C;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjBzI,QAAQ,CAAC/C,oBAAoB,CAAC;QAC5BwD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACR6G,MAAM,EAAE3G,UAAU;QAClB4G,SAAS,EAAE,MAAM;QACjBpF,MAAM,EAAEP,YAAY;QAAE;QACtBK,KAAK,EAAEN,WAAW;QAClBU,OAAO,EAAER,aAAa;QACtB2F,SAAS,EAAE1F;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOyG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMnC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAClE,UAAU,EAAE;MACfkE,MAAM,CAAClE,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAACoE,IAAI,CAACpE,UAAU,CAAC,EAAE;MAC5CkE,MAAM,CAAClE,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAKoG,KAAK,CAACpG,WAAW,CAAC,IAAIqG,MAAM,CAACrG,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEgE,MAAM,CAAChE,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACf0D,MAAM,CAAC1D,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjB4D,MAAM,CAAC5D,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAO4D,MAAM;EACf,CAAC;;EAED;EACA,MAAMsC,wBAAwB,GAAG,MAAOxC,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMwC,iBAAiB,GAAGrG,oBAAoB,GAAGvG,kBAAkB,CAACuG,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACsG,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAMvC,MAAM,GAAGmC,wBAAwB,CAAC,CAAC;IACzC,IAAI/B,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM2F,SAAS,GAAG;QAChBnI,KAAK,EAAEyB,UAAU;QACjBqE,OAAO,EAAE/D,YAAY;QACrBE,UAAU;QACVkE,IAAI,EAAEhE;MACR,CAAC;;MAED;MACA,IAAI+F,iBAAiB,EAAE;QACrBC,SAAS,CAACC,cAAc,GAAGJ,MAAM,CAACE,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAMtK,QAAQ,CAAC5C,0BAA0B,CAACmN,SAAS,CAAC,CAAC;;MAErD;MACA9B,eAAe,CAAC,CAAC;MACjBzI,QAAQ,CAAC/C,oBAAoB,CAAC;QAC5BwD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACR6G,MAAM,EAAE3G,UAAU;QAClB4G,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM6F,YAAY,GAAIzD,EAAE,IAAK;IAC3B7F,kBAAkB,CAAC6F,EAAE,CAAC;IACtB/F,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMyJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC1K,QAAQ,CAAC9C,oBAAoB,CAACgE,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM0J,YAAY,GAAGA,CAAA,KAAM;IACzB1J,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMyJ,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,aAAa,KAAK;IAC7D,IAAI;MACF,MAAMvB,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtD,MAAMd,WAAW,GAAG;QAClBhG,MAAM,EAAE,CAACwI,aAAa;QAAE;QACxBtC,WAAW,EAAE,CAACsC,aAAa,GAAGvB,KAAK,GAAG,IAAI,CAAE;MAC9C,CAAC;MAED,MAAMvJ,QAAQ,CAAC1C,oBAAoB,CAAC;QAClC0J,EAAE,EAAE6D,SAAS;QACbvC;MACF,CAAC,CAAC,CAAC;;MAEH;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvClJ,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF;MACA,MAAMmJ,MAAM,GAAG;QACbrD,SAAS,EAAE1F,WAAW,IAAIgJ,SAAS;QACnC5H,UAAU,EAAEvB,WAAW,IAAImJ,SAAS;QACpC1H,QAAQ,EAAEzB,WAAW,IAAImJ;MAC3B,CAAC;MAED,MAAMjL,QAAQ,CAAC3C,sBAAsB,CAAC2N,MAAM,CAAC,CAAC;;MAE9C;MACA,IAAI7K,iBAAiB,EAAE;QACrB,MAAM;UAAE+K,eAAe;UAAEC,iBAAiB;UAAEC;QAAgB,CAAC,GAAGjL,iBAAiB;;QAEjF;QACA,MAAMkL,aAAa,GAAG;UACpBC,QAAQ,EAAE;YACRC,aAAa,EAAE,CAAAL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,aAAa,KAAI,CAAC;YAClDC,YAAY,EAAE,CAAAN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,YAAY,KAAI,CAAC;YAChDC,cAAc,EAAE,CAAAP,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,cAAc,KAAI,CAAC;YACpDC,WAAW,EAAE,CAAAR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEQ,WAAW,KAAI;UAC/C,CAAC;UACDC,YAAY,EAAE,CAAAR,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAES,GAAG,CAACC,IAAI,KAAK;YAC5CzJ,KAAK,EAAEyJ,IAAI,CAACzJ,KAAK;YACjB0J,cAAc,EAAED,IAAI,CAACC,cAAc;YACnCnL,KAAK,EAAEkL,IAAI,CAACN,aAAa;YACzBQ,IAAI,EAAEF,IAAI,CAACL,YAAY;YACvBQ,MAAM,EAAEH,IAAI,CAACJ,cAAc;YAC3BC,WAAW,EAAEG,IAAI,CAACH;UACpB,CAAC,CAAC,CAAC,KAAI,EAAE;UACTO,UAAU,EAAE,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEQ,GAAG,CAACC,IAAI,KAAK;YACxCK,KAAK,EAAEL,IAAI,CAAClE,SAAS;YACrBhH,KAAK,EAAEkL,IAAI,CAACN,aAAa;YACzBQ,IAAI,EAAEF,IAAI,CAACL,YAAY;YACvBQ,MAAM,EAAEH,IAAI,CAACJ,cAAc;YAC3BC,WAAW,EAAEG,IAAI,CAACH;UACpB,CAAC,CAAC,CAAC,KAAI,EAAE;UACTS,OAAO,EAAEhM,iBAAiB,CAAC;QAC7B,CAAC;QAEDwB,iBAAiB,CAAC0J,aAAa,CAAC;QAChC5J,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAC,MAAM;QACLkH,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;QACpD;QACA,MAAM6C,aAAa,GAAGrL,eAAe,CAACmI,MAAM;QAC5C,MAAMmD,YAAY,GAAGtL,eAAe,CAACkM,MAAM,CAACpD,OAAO,IAAIA,OAAO,CAAC1G,MAAM,CAAC,CAAC+F,MAAM;QAC7E,MAAMoD,cAAc,GAAGF,aAAa,GAAGC,YAAY;QACnD,MAAME,WAAW,GAAGH,aAAa,GAAG,CAAC,GAAG,CAAEC,YAAY,GAAGD,aAAa,GAAI,GAAG,EAAEc,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAE7F1K,iBAAiB,CAAC;UAChB2J,QAAQ,EAAE;YACRC,aAAa;YACbC,YAAY;YACZC,cAAc;YACdC;UACF,CAAC;UACDC,YAAY,EAAE,EAAE;UAChBM,UAAU,EAAE,EAAE;UACdE,OAAO,EAAE;QACX,CAAC,CAAC;QACF1K,iBAAiB,CAAC,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOiH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACR7G,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;;EAEA,IAAIzB,OAAO,EAAE;IACX,oBAAOZ,OAAA,CAAC1B,cAAc;MAAAwO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACEnN,OAAA;MACEsN,OAAO,EAAEA,OAAQ;MACjBC,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJJ,IAAI,eACLpN,OAAA;QAAAwN,QAAA,EAAOH;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMQ,OAAO,gBACXzN,OAAA;IAAK,wBAAgB;IAACuN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCxN,OAAA,CAACrB,IAAI;MAAC+O,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMU,SAAS,gBACb3N,OAAA;IAAK,wBAAgB;IAACuN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCxN,OAAA,CAACnB,QAAQ;MAAC6O,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMW,YAAY,gBAChB5N,OAAA;IAAK,wBAAgB;IAACuN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCxN,OAAA,CAAClB,QAAQ;MAAC4O,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMY,SAAS,gBACb7N,OAAA;IAAK,wBAAgB;IAACuN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCxN,OAAA,CAACjB,KAAK;MAAC2O,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,MAAMa,cAAc,gBAClB9N,OAAA;IAAK,wBAAgB;IAACuN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCxN,OAAA,CAACd,SAAS;MAACwO,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CACN;EAED,oBACEjN,OAAA,CAACT,WAAW;IAAAiO,QAAA,gBACVxN,OAAA;MAAKuN,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENjN,OAAA;MAAKuN,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFxN,OAAA;QAAKuN,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBxN,OAAA;UAAKuN,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxN,OAAA;YAAKuN,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxN,OAAA;cACE+N,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXZ,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnExN,OAAA;gBACEoO,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjN,OAAA;cACEwO,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kEAAwC;cACpDC,KAAK,EAAEpN,UAAW;cAClBqN,QAAQ,EAAGtG,CAAC,IAAK9G,aAAa,CAAC8G,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAC/CnB,SAAS,EAAC;YAAsI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjN,OAAA;YAAKuN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxN,OAAA;cACEuN,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAEpM,WAAY;cACnBqM,QAAQ,EAAGtG,CAAC,IAAK1F,cAAc,CAAC0F,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAEhDxN,OAAA;gBAAQ0O,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9B4B,KAAK,CAACC,IAAI,CAAC;gBAAEjG,MAAM,EAAE;cAAG,CAAC,EAAE,CAACkG,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAMpM,KAAK,GAAGoM,CAAC,GAAG,CAAC;gBACnB,MAAM/E,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;gBACrC,MAAM+E,QAAQ,GAAGrM,KAAK,GAAG,EAAE,OAAAyH,MAAA,CAAOzH,KAAK,OAAAyH,MAAA,CAAQzH,KAAK,CAAE;gBACtD,oBACE5C,OAAA;kBAAoB0O,KAAK,KAAArE,MAAA,CAAKJ,IAAI,OAAAI,MAAA,CAAI4E,QAAQ,CAAG;kBAAAzB,QAAA,cAAAnD,MAAA,CACrCzH,KAAK,OAAAyH,MAAA,CAAIJ,IAAI;gBAAA,GADZrH,KAAK;kBAAAkK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjN,OAAA;YAAKuN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxN,OAAA;cACEuN,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAEnM,YAAa;cACpBoM,QAAQ,EAAGtG,CAAC,IAAKxF,eAAe,CAACwF,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAEjDxN,OAAA;gBAAQ0O,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCjN,OAAA;gBAAQ0O,KAAK,EAAC,MAAM;gBAAAlB,QAAA,EAAC;cAAa;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CjN,OAAA;gBAAQ0O,KAAK,EAAC,OAAO;gBAAAlB,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjN,OAAA;YAAKuN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxN,OAAA;cACEuN,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAElM,aAAc;cACrBmM,QAAQ,EAAGtG,CAAC,IAAKtF,gBAAgB,CAACsF,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAElDxN,OAAA;gBAAQ0O,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjN,OAAA;gBAAQ0O,KAAK,EAAC,MAAM;gBAAAlB,QAAA,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjN,OAAA;gBAAQ0O,KAAK,EAAC,OAAO;gBAAAlB,QAAA,EAAC;cAAY;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjN,OAAA;YAAKuN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxN,OAAA;cACEuN,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAEjM,WAAY;cACnBkM,QAAQ,EAAGtG,CAAC,IAAKpF,cAAc,CAACoF,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAEhDxN,OAAA;gBAAQ0O,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BjN,OAAA;gBAAQ0O,KAAK,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCjN,OAAA;gBAAQ0O,KAAK,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCjN,OAAA;gBAAQ0O,KAAK,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjN,OAAA;YAAKuN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxN,OAAA,CAACzC,gBAAgB;cACfmR,KAAK,EAAErL,eAAgB;cACvB6L,eAAe,EAAExM,aAAc;cAC/BiM,QAAQ,EAAErL,kBAAmB;cAC7B6L,QAAQ,EAAE7H,iBAAkB;cAC5B8H,OAAO,EAAE1H,yBAA0B;cACnC+G,WAAW,EAAC;YAAqB;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjN,OAAA;YACEsN,OAAO,EAAEtF,YAAa;YACtBuF,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3FxN,OAAA;cAAKuN,SAAS,EAAC,cAAc;cAACY,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAP,QAAA,eACpHxN,OAAA;gBAAMsO,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACc,WAAW,EAAC,GAAG;gBAACjB,CAAC,EAAC;cAA6C;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CAAC,eAENjN,OAAA;UAAKuN,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxN,OAAA,CAACkN,sBAAsB;YAACE,IAAI,EAAEK,OAAQ;YAACJ,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAExD;UAAU;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtFjN,OAAA,CAACkN,sBAAsB;YAACE,IAAI,EAAEQ,YAAa;YAACP,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAE/C;UAAyB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChHjN,OAAA,CAACkN,sBAAsB;YAACE,IAAI,EAAEU,cAAe;YAACT,IAAI,EAAE,cAAe;YAACC,OAAO,EAAE/B;UAAqB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELvM,eAAe,CAACmI,MAAM,KAAK,CAAC,gBAC3B7I,OAAA;MAAKuN,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DxN,OAAA;QAAGuN,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,gBAENjN,OAAA;MAAKuN,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BxN,OAAA;QAAOuN,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzExN,OAAA;UAAOuN,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5BxN,OAAA;YAAAwN,QAAA,gBACExN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eACjCxN,OAAA;gBAAKuN,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxN,OAAA;kBAAMuN,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClDjN,OAAA;kBAAMuN,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA6B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5EjN,OAAA;kBAAMuN,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDjN,OAAA;cAAIuN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRjN,OAAA;UAAAwN,QAAA,EACG9M,eAAe,CAAC0L,GAAG,CAAC,CAAC5C,OAAO,EAAE8F,KAAK;YAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClC5P,OAAA;cAEEuN,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAA9B,QAAA,gBAEvDxN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAACvM,IAAI,GAAG,CAAC,IAAIG,QAAQ,GAAGkO,KAAK,GAAG;cAAC;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAA+B,aAAA,GAAA/F,OAAO,CAACqG,IAAI,cAAAN,aAAA,uBAAZA,aAAA,CAAczH,QAAQ,IAAG,GAAG,KAAA0H,cAAA,GAAGhG,OAAO,CAACqG,IAAI,cAAAL,cAAA,uBAAZA,cAAA,CAAc3H,SAAS,KAAI;cAAK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAiC,cAAA,GAAAjG,OAAO,CAACqG,IAAI,cAAAJ,cAAA,uBAAZA,cAAA,CAAc/C,KAAK,KAAI;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAkC,cAAA,GAAAlG,OAAO,CAACqG,IAAI,cAAAH,cAAA,uBAAZA,cAAA,CAAcI,UAAU,KAAI;cAAK;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEjN,OAAA;gBAAIuN,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eACjCxN,OAAA;kBAAKuN,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCxN,OAAA;oBAAAwN,QAAA,EAAO,EAAAmC,cAAA,GAAAnG,OAAO,CAACqG,IAAI,cAAAF,cAAA,uBAAZA,cAAA,CAAcI,KAAK,KAAI;kBAAK;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3CjN,OAAA;oBAAAwN,QAAA,EAAO,EAAAoC,cAAA,GAAApG,OAAO,CAACqG,IAAI,cAAAD,cAAA,uBAAZA,cAAA,CAAcI,QAAQ,KAAI;kBAAmB;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhE,OAAO,CAAC8C;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhE,OAAO,CAACR,WAAW,GAChB,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACiH,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAI9D,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAACuH,kBAAkB,CAAC,OAAO;cAAC;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBxN,OAAA;kBAAMuN,SAAS,gDAAAlD,MAAA,CAAgDb,OAAO,CAAC1G,MAAM,GACzE,6BAA6B,GAC7B0G,OAAO,CAAC0G,SAAS,GACf,yBAAyB,GACzB,+BAA+B,CAChC;kBAAA1C,QAAA,EACFhE,OAAO,CAAC1G,MAAM,GACX,eAAe,GACf0G,OAAO,CAAC0G,SAAS,GACf,SAAS,GACT;gBAAiB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBxN,OAAA;kBAAMuN,SAAS,EAAC,uBAAuB;kBAAC4C,KAAK,EAAE3G,OAAO,CAACT,IAAK;kBAAAyE,QAAA,EACzDhE,OAAO,CAACT,IAAI,GAAIS,OAAO,CAACT,IAAI,CAACF,MAAM,GAAG,EAAE,GAAGW,OAAO,CAACT,IAAI,CAACqH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG5G,OAAO,CAACT,IAAI,GAAI;gBAAG;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLjN,OAAA;gBAAIuN,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBxN,OAAA;kBAAKuN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAE1CxN,OAAA;oBAAKuN,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,eAC/CxN,OAAA;sBACEwO,IAAI,EAAC,UAAU;sBACf6B,OAAO,EAAE7G,OAAO,CAAC1G,MAAO;sBACxB6L,QAAQ,EAAEA,CAAA,KAAMvD,kBAAkB,CAAC5B,OAAO,CAAChC,EAAE,EAAEgC,OAAO,CAAC1G,MAAM,CAAE;sBAC/DyK,SAAS,EAAC,6GAA6G;sBACvH4C,KAAK,EAAE3G,OAAO,CAAC1G,MAAM,GAAG,2BAA2B,GAAG;oBAAyB;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAGNjN,OAAA;oBAAKuN,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BxN,OAAA;sBACEsN,OAAO,EAAEA,CAAA,KAAMzD,UAAU,CAACL,OAAO,CAAChC,EAAE,EAAEgC,OAAO,CAAChB,MAAM,EAAEgB,OAAO,CAAC5G,KAAK,CAAE;sBACrE2K,SAAS,EAAC,mCAAmC;sBAC7C4C,KAAK,EAAC,mBAAc;sBAAA3C,QAAA,eAEpBxN,OAAA,CAACpB,GAAG;wBAAC8O,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACTjN,OAAA;sBACEsN,OAAO,EAAEA,CAAA,KAAMjE,UAAU,CAACG,OAAO,CAAChC,EAAE,CAAE;sBACtC+F,SAAS,EAAC,uCAAuC;sBACjD4C,KAAK,EAAC,qBAAW;sBAAA3C,QAAA,eAEjBxN,OAAA,CAACvB,IAAI;wBAACiP,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACTjN,OAAA;sBACEsN,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAACzB,OAAO,CAAChC,EAAE,CAAE;sBACxC+F,SAAS,EAAC,iCAAiC;sBAC3C4C,KAAK,EAAC,QAAK;sBAAA3C,QAAA,eAEXxN,OAAA,CAACtB,KAAK;wBAACgP,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GApFAzD,OAAO,CAAChC,EAAE;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqFb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAtJ,QAAQ,KAAK,OAAO,iBACnB3D,OAAA;MAAKuN,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBxN,OAAA,CAACzB,UAAU;QACT+R,WAAW,EAAErP,IAAK;QAClBsP,YAAY,EAAGtP,IAAI,IAAKT,QAAQ,CAACnC,cAAc,CAAC4C,IAAI,CAAC,CAAE;QACvDuP,UAAU,EAAErP,KAAM;QAClBsP,KAAK,EAAErP;MAAS;QAAA0L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAGAjL,cAAc,iBACbhC,OAAA;MAAKuN,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFxN,OAAA;QAAKuN,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAC/FxN,OAAA;UAAKuN,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7ExN,OAAA;YAAIuN,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA2B;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpFjN,OAAA;YACEsN,OAAO,EAAEA,CAAA,KAAMrL,iBAAiB,CAAC,KAAK,CAAE;YACxCsL,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CxN,OAAA,CAAChB,CAAC;cAAC0O,IAAI,EAAE;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL7K,iBAAiB,gBAChBpC,OAAA;UAAKuN,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDxN,OAAA,CAAC1B,cAAc;YAAAwO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,GACJ/K,cAAc,gBAChBlC,OAAA;UAAKuN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAElBxN,OAAA;YAAKuN,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDxN,OAAA;cAAKuN,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxCxN,OAAA;gBAAKuN,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCxN,OAAA,CAACjB,KAAK;kBAACwO,SAAS,EAAC;gBAAuB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3CjN,OAAA;kBAAKuN,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxN,OAAA;oBAAGuN,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClEjN,OAAA;oBAAGuN,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAEtL,cAAc,CAAC4J,QAAQ,CAACC;kBAAa;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjN,OAAA;cAAKuN,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCxN,OAAA;gBAAKuN,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCxN,OAAA,CAACX,UAAU;kBAACkO,SAAS,EAAC;gBAAwB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDjN,OAAA;kBAAKuN,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxN,OAAA;oBAAGuN,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnEjN,OAAA;oBAAGuN,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEtL,cAAc,CAAC4J,QAAQ,CAACE;kBAAY;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjN,OAAA;cAAKuN,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCxN,OAAA;gBAAKuN,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCxN,OAAA,CAACf,WAAW;kBAACsO,SAAS,EAAC;gBAAsB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDjN,OAAA;kBAAKuN,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxN,OAAA;oBAAGuN,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnEjN,OAAA;oBAAGuN,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEtL,cAAc,CAAC4J,QAAQ,CAACG;kBAAc;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjN,OAAA;cAAKuN,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC1CxN,OAAA;gBAAKuN,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCxN,OAAA,CAACV,UAAU;kBAACiO,SAAS,EAAC;gBAAyB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClDjN,OAAA;kBAAKuN,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxN,OAAA;oBAAGuN,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACvEjN,OAAA;oBAAGuN,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAEtL,cAAc,CAAC4J,QAAQ,CAACI,WAAW,EAAC,GAAC;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL/K,cAAc,CAACiK,YAAY,CAACtD,MAAM,GAAG,CAAC,iBACrC7I,OAAA;YAAKuN,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxN,OAAA;cAAIuN,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFjN,OAAA;cAAKuN,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BxN,OAAA;gBAAOuN,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACtExN,OAAA;kBAAOuN,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAC3BxN,OAAA;oBAAAwN,QAAA,gBACExN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1FjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzFjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5FjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAS;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9FjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRjN,OAAA;kBAAOuN,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACxCtL,cAAc,CAACiK,YAAY,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEiD,KAAK,kBAC3CtP,OAAA;oBAAqBuN,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;oBAAA9B,QAAA,gBAC1ExN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEnB,IAAI,CAACC;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1EjN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEnB,IAAI,CAAClL;oBAAK;sBAAA2L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjEjN,OAAA;sBAAIuN,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAEnB,IAAI,CAACE;oBAAI;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7EjN,OAAA;sBAAIuN,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAEnB,IAAI,CAACG;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7EjN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAC5CnB,IAAI,CAAClL,KAAK,GAAG,CAAC,GAAG,CAAEkL,IAAI,CAACE,IAAI,GAAGF,IAAI,CAAClL,KAAK,GAAI,GAAG,EAAE0L,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,GACpE;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GAPEZ,IAAI,CAACzJ,KAAK;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA/K,cAAc,CAACuK,UAAU,CAAC5D,MAAM,GAAG,CAAC,iBACnC7I,OAAA;YAAAwN,QAAA,gBACExN,OAAA;cAAIuN,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EjN,OAAA;cAAKuN,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BxN,OAAA;gBAAOuN,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACtExN,OAAA;kBAAOuN,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAC3BxN,OAAA;oBAAAwN,QAAA,gBACExN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxFjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzFjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5FjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAS;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9FjN,OAAA;sBAAIuN,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRjN,OAAA;kBAAOuN,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACxCtL,cAAc,CAACuK,UAAU,CAACL,GAAG,CAAC,CAACC,IAAI,EAAEiD,KAAK,kBACzCtP,OAAA;oBAAqBuN,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;oBAAA9B,QAAA,gBAC1ExN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEnB,IAAI,CAACK;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjEjN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEnB,IAAI,CAAClL;oBAAK;sBAAA2L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjEjN,OAAA;sBAAIuN,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAEnB,IAAI,CAACE;oBAAI;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7EjN,OAAA;sBAAIuN,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAEnB,IAAI,CAACG;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7EjN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAC5CnB,IAAI,CAAClL,KAAK,GAAG,CAAC,GAAG,CAAEkL,IAAI,CAACE,IAAI,GAAGF,IAAI,CAAClL,KAAK,GAAI,GAAG,EAAE0L,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAC,GACpE;oBAAA;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GAPEZ,IAAI,CAACK,KAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENjN,OAAA;UAAKuN,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE/C;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArL,cAAc,iBACb5B,OAAA;MAAKuN,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFxN,OAAA;QAAKuN,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7ExN,OAAA;UAAIuN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClC1L,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACLjN,OAAA;UACEsN,OAAO,EAAErE,eAAgB;UACzBsE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CxN,OAAA,CAAChB,CAAC;YAAC0O,IAAI,EAAE;UAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjN,OAAA;QAAAwN,QAAA,GACG1L,cAAc,KAAK,KAAK,iBACvB9B,OAAA;UAAKuN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxN,OAAA;YAAGuN,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvEjN,OAAA;YAAMuN,SAAS,EAAC,WAAW;YAACmD,QAAQ,EAAEtI,sBAAuB;YAAAoF,QAAA,gBAC3DxN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxHjN,OAAA,CAACxC,eAAe;gBACdkR,KAAK,EAAEnJ,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1BsJ,QAAQ,EAAEnJ,oBAAqB;gBAC/B2J,QAAQ,EAAGU,IAAI,IAAK;kBAClBvK,YAAY,CAACuK,IAAI,CAACrI,EAAE,CAAC;kBACrBhC,oBAAoB,IAAA6E,MAAA,CAAIwF,IAAI,CAAC/H,QAAQ,OAAAuC,MAAA,CAAIwF,IAAI,CAAChI,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACFuH,OAAO,EAAEA,CAAA,KAAM;kBACb9J,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACFiJ,WAAW,EAAC,mCAAsB;gBAClCkC,IAAI,EAAC;cAAS;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDhI,UAAU,CAACuD,MAAM,iBAChBxI,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACuD,MAAM;cAAA;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrHjN,OAAA;gBACEwO,IAAI,EAAC,OAAO;gBACZjB,SAAS,6BAAAlD,MAAA,CAA6BpF,UAAU,CAACrC,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3G8L,KAAK,EAAEjJ,QAAS;gBAChBkJ,QAAQ,EAAGtG,CAAC,IAAK3C,WAAW,CAAC2C,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACDhI,UAAU,CAACrC,KAAK,iBACf5C,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACrC,KAAK;cAAA;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFjN,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXjB,SAAS,EAAC,oDAAoD;gBAC9DmB,KAAK,EAAE/I,cAAe;gBACtBgJ,QAAQ,EAAGtG,CAAC,IAAKzC,iBAAiB,CAACyC,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HjN,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXjB,SAAS,6BAAAlD,MAAA,CAA6BpF,UAAU,CAACyD,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7GgG,KAAK,EAAE7I,UAAW;gBAClB8I,QAAQ,EAAGtG,CAAC,IAAKvC,aAAa,CAACuC,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDhI,UAAU,CAACyD,OAAO,iBACjB1I,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACyD,OAAO;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7FjN,OAAA;gBAAKuN,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxN,OAAA;kBACEwO,IAAI,EAAC,UAAU;kBACfhH,EAAE,EAAC,WAAW;kBACd6I,OAAO,EAAEtK,SAAU;kBACnB4I,QAAQ,EAAGtG,CAAC,IAAKrC,YAAY,CAACqC,CAAC,CAACuG,MAAM,CAACyB,OAAO,CAAE;kBAChD9C,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFjN,OAAA;kBAAO4Q,OAAO,EAAC,WAAW;kBAACrD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EjN,OAAA;gBACEuN,SAAS,EAAC,oDAAoD;gBAC9DsD,IAAI,EAAC,GAAG;gBACRpC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEzI,OAAQ;gBACf0I,QAAQ,EAAGtG,CAAC,IAAKnC,UAAU,CAACmC,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLhI,UAAU,CAACmE,MAAM,iBAChBpJ,OAAA;cAAKuN,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjGxN,OAAA,CAACf,WAAW;gBAACyO,IAAI,EAAE,EAAG;gBAACH,SAAS,EAAC;cAA2B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DjN,OAAA;gBAAAwN,QAAA,EAAIvI,UAAU,CAACmE;cAAM;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACDjN,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,uHAAuH;cACjIuD,QAAQ,EAAE3L,YAAa;cAAAqI,QAAA,EAEtBrI,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACAnL,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKuN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxN,OAAA;YAAGuN,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElFjN,OAAA;YAAMuN,SAAS,EAAC,WAAW;YAACmD,QAAQ,EAAGrI,CAAC,IAAKwC,wBAAwB,CAACxC,CAAC,CAAE;YAAAmF,QAAA,gBACvExN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrHjN,OAAA;gBACEwO,IAAI,EAAC,OAAO;gBACZjB,SAAS,6BAAAlD,MAAA,CAA6BpF,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHqK,KAAK,EAAErK,UAAW;gBAClBsK,QAAQ,EAAGtG,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDhI,UAAU,CAACZ,UAAU,iBACpBrE,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACZ,UAAU;cAAA;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnHjN,OAAA;gBACEuN,SAAS,6BAAAlD,MAAA,CAA6BpF,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH6J,KAAK,EAAE7J,UAAW;gBAClB8J,QAAQ,EAAGtG,CAAC,IAAKvD,aAAa,CAACuD,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;gBAAAvD,QAAA,gBAERxN,OAAA;kBAAQ0O,KAAK,EAAC,EAAE;kBAAAlB,QAAA,EAAC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjN,OAAA;kBAAQ0O,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjN,OAAA;kBAAQ0O,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjN,OAAA;kBAAQ0O,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACRhI,UAAU,CAACJ,UAAU,iBACpB7E,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACJ,UAAU;cAAA;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HjN,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXjB,SAAS,6BAAAlD,MAAA,CAA6BpF,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClH+J,KAAK,EAAE/J,YAAa;gBACpBgK,QAAQ,EAAGtG,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBACjDqC,QAAQ;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDhI,UAAU,CAACN,YAAY,iBACtB3E,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACN,YAAY;cAAA;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EjN,OAAA;gBACEuN,SAAS,EAAC,oDAAoD;gBAC9DsD,IAAI,EAAC,GAAG;gBACRpC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAE3J,SAAU;gBACjB4J,QAAQ,EAAGtG,CAAC,IAAKrD,YAAY,CAACqD,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNjN,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,uHAAuH;cACjIuD,QAAQ,EAAE3L,YAAa;cAAAqI,QAAA,EAEtBrI,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAnL,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAKuN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxN,OAAA;YAAGuN,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/DjN,OAAA;YAAMuN,SAAS,EAAC,WAAW;YAACmD,QAAQ,EAAGrI,CAAC,IAAKoC,0BAA0B,CAACpC,CAAC,CAAE;YAAAmF,QAAA,gBACzExN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFjN,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXjB,SAAS,EAAC,oDAAoD;gBAC9DmB,KAAK,EAAErI,eAAgB;gBACvBsI,QAAQ,EAAGtG,CAAC,IAAK/B,kBAAkB,CAAC+B,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAxN,OAAA;kBAAMuN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HjN,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXjB,SAAS,6BAAAlD,MAAA,CAA6BpF,UAAU,CAACsB,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjHmI,KAAK,EAAEnI,WAAY;gBACnBoI,QAAQ,EAAGtG,CAAC,IAAK7B,cAAc,CAAC6B,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,EACDhI,UAAU,CAACsB,WAAW,iBACrBvG,OAAA;gBAAGuN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDxN,OAAA,CAACf,WAAW;kBAACyO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACsB,WAAW;cAAA;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7FjN,OAAA;gBAAKuN,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxN,OAAA;kBACEwO,IAAI,EAAC,UAAU;kBACfhH,EAAE,EAAC,YAAY;kBACf6I,OAAO,EAAE5J,UAAW;kBACpBkI,QAAQ,EAAGtG,CAAC,IAAK3B,aAAa,CAAC2B,CAAC,CAACuG,MAAM,CAACyB,OAAO,CAAE;kBACjD9C,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFjN,OAAA;kBAAO4Q,OAAO,EAAC,YAAY;kBAACrD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjN,OAAA;cAAAwN,QAAA,gBACExN,OAAA;gBAAOuN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EjN,OAAA;gBACEuN,SAAS,EAAC,oDAAoD;gBAC9DsD,IAAI,EAAC,GAAG;gBACRpC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAE/H,QAAS;gBAChBgI,QAAQ,EAAGtG,CAAC,IAAKzB,WAAW,CAACyB,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLhI,UAAU,CAACmE,MAAM,iBAChBpJ,OAAA;cAAGuN,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDxN,OAAA,CAACf,WAAW;gBAACyO,IAAI,EAAE,EAAG;gBAACH,SAAS,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAAChI,UAAU,CAACmE,MAAM;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACDjN,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,uHAAuH;cACjIuD,QAAQ,EAAE3L,YAAa;cAAAqI,QAAA,EAEtBrI,YAAY,GAAG,eAAe,GAAG;YAAc;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAnL,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAAwN,QAAA,EACGvG,WAAW,gBACVjH,OAAA;YAAKuN,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDxN,OAAA,CAAC1B,cAAc;cAAAwO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJpM,cAAc,gBAChBb,OAAA;YAAKuN,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxN,OAAA;cAAKgR,GAAG,EAAE3J,mBAAoB;cAACkG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1ExN,OAAA;gBAAKuN,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnExN,OAAA;kBAAKuN,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCxN,OAAA;oBAAKuN,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNjN,OAAA;oBAAAwN,QAAA,gBACExN,OAAA;sBAAIuN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClEjN,OAAA;sBAAGuN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEjN,OAAA;sBAAGuN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAI9D,IAAI,CAAC,CAAC,CAACuG,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGNjN,OAAA;gBAAKuN,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCxN,OAAA;kBAAIuN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEjN,OAAA;kBAAKuN,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBxN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAA9M,oBAAA,GAACU,cAAc,CAACgP,IAAI,cAAA1P,oBAAA,uBAAnBA,oBAAA,CAAqB2H,QAAQ,EAAC,GAAC,GAAA1H,qBAAA,GAACS,cAAc,CAACgP,IAAI,cAAAzP,qBAAA,uBAAnBA,qBAAA,CAAqByH,SAAS;kBAAA;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpHjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAA5M,qBAAA,GAAAQ,cAAc,CAACgP,IAAI,cAAAxP,qBAAA,uBAAnBA,qBAAA,CAAqB0P,KAAK,KAAI,UAAU;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrGjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAA3M,qBAAA,GAAAO,cAAc,CAACgP,IAAI,cAAAvP,qBAAA,uBAAnBA,qBAAA,CAAqBoM,KAAK,KAAI,UAAU;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3FjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAA1M,qBAAA,GAAAM,cAAc,CAACgP,IAAI,cAAAtP,qBAAA,uBAAnBA,qBAAA,CAAqBuP,UAAU,KAAI,UAAU;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjN,OAAA;gBAAAwN,QAAA,gBACExN,OAAA;kBAAIuN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEjN,OAAA;kBAAKuN,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvExN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpM,cAAc,CAACyL,cAAc;kBAAA;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClFjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjP,cAAc,CAAC6C,cAAc,CAACmK,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClHjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjP,cAAc,CAAC6C,cAAc,CAACoQ,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7GjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjP,cAAc,CAAC,CAAC6C,cAAc,CAACmK,cAAc,IAAI,CAAC,KAAKnK,cAAc,CAACoQ,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9IjN,OAAA;oBAAAwN,QAAA,gBACExN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrDjN,OAAA;sBAAMuN,SAAS,oCAAAlD,MAAA,CAAoCxJ,cAAc,CAACqQ,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjHrQ,cAAc,CAACqQ,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5DrQ,cAAc,CAACqQ,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAA1D,QAAA,EACF3M,cAAc,CAACqQ,MAAM,KAAK,MAAM,GAAG,eAAe,GACjDrQ,cAAc,CAACqQ,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpDrQ,cAAc,CAACqQ,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpM,cAAc,CAACmI,WAAW,GAAG,IAAIU,IAAI,CAAC7I,cAAc,CAACmI,WAAW,CAAC,CAACiH,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzKjN,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpM,cAAc,CAAC6H,OAAO,GAAG,IAAIgB,IAAI,CAAC7I,cAAc,CAAC6H,OAAO,CAAC,CAACuH,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJpM,cAAc,CAACkI,IAAI,iBAAI/I,OAAA;oBAAAwN,QAAA,gBAAGxN,OAAA;sBAAMuN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACpM,cAAc,CAACkI,IAAI;kBAAA;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLnM,yBAAyB,IAAIA,yBAAyB,CAACqQ,aAAa,IAAIrQ,yBAAyB,CAACqQ,aAAa,CAACtI,MAAM,GAAG,CAAC,iBACzH7I,OAAA;gBAAAwN,QAAA,gBACExN,OAAA;kBAAIuN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEjN,OAAA;kBAAKuN,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvB1M,yBAAyB,CAACqQ,aAAa,CAAC/E,GAAG,CAAEpL,OAAO,iBACnDhB,OAAA;oBAAsBuN,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9ExN,OAAA;sBAAKuN,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDxN,OAAA;wBAAIuN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAExM,OAAO,CAACuM;sBAAS;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpDjN,OAAA;wBAAMuN,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAACxM,OAAO,CAACoQ,UAAU;sBAAA;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNjN,OAAA;sBAAAwN,QAAA,gBAAGxN,OAAA;wBAAMuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACjP,cAAc,CAACgD,OAAO,CAACqQ,MAAM,CAAC;oBAAA;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/FjN,OAAA;sBAAAwN,QAAA,gBAAGxN,OAAA;wBAAMuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAIvD,IAAI,CAAC1I,OAAO,CAACsQ,QAAQ,CAAC,CAACrB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrHjM,OAAO,CAAC+H,IAAI,iBAAI/I,OAAA;sBAAAwN,QAAA,gBAAGxN,OAAA;wBAAMuN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACjM,OAAO,CAAC+H,IAAI;oBAAA;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtFjM,OAAO,CAACwG,EAAE;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjN,OAAA;kBAAKuN,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CxN,OAAA;oBAAGuN,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAACxP,cAAc,CAAC8C,yBAAyB,CAACyQ,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjHjN,OAAA;oBAAGuN,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAACxP,cAAc,CAAC6C,cAAc,CAACmK,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGDjN,OAAA;gBAAKuN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCxN,OAAA;kBAAKuN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDxN,OAAA;oBAAAwN,QAAA,gBACExN,OAAA;sBAAGuN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClEjN,OAAA;sBAAGuN,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACNjN,OAAA;oBAAKuN,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BxN,OAAA;sBAAGuN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxDjN,OAAA;sBAAKuN,SAAS,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5BjN,OAAA;sBAAGuN,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxDjN,OAAA;sBAAGuN,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGPjN,OAAA;cAAKuN,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCxN,OAAA;gBACEsN,OAAO,EAAEA,CAAA,KAAMjE,UAAU,CAACxI,cAAc,CAAC2G,EAAE,CAAE;gBAC7C+F,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjN,OAAA;YAAKuN,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDjN,OAAA,CAACxB,YAAY;MACXgT,MAAM,EAAEhQ,gBAAiB;MACzBiQ,SAAS,EAAEvG,aAAc;MACzBmC,IAAI,EAAC,qGAAmD;MACxDqE,OAAO,EAAEvG;IAAa;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAC/M,EAAA,CA/7CID,kBAAkB;EAAA,QACL/C,WAAW,EACXE,WAAW,EACuED,WAAW,EAEhEA,WAAW,EAsBwBA,WAAW;AAAA;AAAAwU,EAAA,GA3BxF1R,kBAAkB;AAi8CxB,eAAeA,kBAAkB;AAAC,IAAA0R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}