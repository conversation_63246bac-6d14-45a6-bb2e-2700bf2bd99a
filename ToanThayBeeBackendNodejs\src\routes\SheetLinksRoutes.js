import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import validate from '../middlewares/validate.js';
import UserType from '../constants/UserType.js';
import { requireRoles } from '../middlewares/jwtMiddleware.js';
import * as SheetLinksController from '../controllers/SheetLinksController.js';
import PostSheetLinkRequest from '../dtos/requests/sheetlinks/PostSheetLinkRequest.js';

const router = express.Router();

// Tạo sheet link mới (chỉ admin/teacher/assistant)
router.post('/v1/admin/sheet-links',
    // validate(PostSheetLinkRequest),
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(SheetLinksController.createSheetLink)
);

// <PERSON><PERSON><PERSON> danh sách sheet links
router.get('/v1/sheet-links',
    requireRoles([]), // Tất cả user đã đăng nhập
    asyncHandler(SheetLinksController.getSheetLinks)
);

// Lấy chi tiết sheet link theo ID
router.get('/v1/sheet-links/:id',
    requireRoles([]), // Tất cả user đã đăng nhập
    asyncHandler(SheetLinksController.getSheetLinkById)
);

// Test Google Sheets connection
router.post('/v1/admin/sheet-links/:sheetId/test-connection',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(SheetLinksController.testGoogleSheetsConnection)
);

// Test thêm user vào sheet
router.post('/v1/admin/sheet-links/:sheetId/test-add-user',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(SheetLinksController.testAddUserToSheet)
);

// CHẠY MỘT LẦN RỒI XÓA ĐI - Thêm username/password vào tất cả Google Sheets
router.post('/v1/admin/sheet-links/add-username-password-all',
    requireRoles([UserType.ADMIN]),
    asyncHandler(SheetLinksController.addUsernamePasswordToSheets)
);

// CHẠY MỘT LẦN RỒI XÓA ĐI - Thêm username/password vào một sheet cụ thể
router.post('/v1/admin/sheet-links/:sheetId/add-username-password',
    requireRoles([UserType.ADMIN]),
    asyncHandler(SheetLinksController.addUsernamePasswordToSingleSheet)
);

export default router;
