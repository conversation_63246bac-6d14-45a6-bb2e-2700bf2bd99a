import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import UserType from '../constants/UserType.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as TuitionPaymentController from '../controllers/TuitionPaymentController.js'

const router = express.Router()

// Lấy danh sách tất cả các khoản đóng học phí (chỉ admin, giáo viên, trợ giảng)
router.get('/v1/admin/tuition-payment',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(TuitionPaymentController.getAllTuitionPayments)
)

// Lấy danh sách đóng học phí của một lớp cụ thể (chỉ admin, gi<PERSON><PERSON> viên, trợ giảng)
router.get('/v1/admin/class/:classId/tuition-payment',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(TuitionPaymentController.getTuitionPaymentsByClassId)
)

// Lấy danh sách đóng học phí của một học sinh cụ thể (chỉ admin, giáo viên, trợ giảng và chính học sinh đó)
router.get('/v1/admin/user/:userId/tuition-payment',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(TuitionPaymentController.getTuitionPaymentsByUserId)
)


// Thống kê doanh thu học phí (chỉ admin, giáo viên)
router.get('/v1/admin/tuition-payment/statistics',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(TuitionPaymentController.getTuitionStatistics)
)

// Lấy thống kê tổng quan về các khoản học phí của một học sinh (chỉ admin, giáo viên, trợ giảng)
router.get('/v1/admin/user/:userId/tuition-summary',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(TuitionPaymentController.getUserTuitionSummary)
)

// Học sinh xem danh sách đóng học phí của mình
router.get('/v1/user/tuition-payment',
    requireRoles([]),
    asyncHandler((req, res) => {
        req.params.userId = req.user.id;
        return TuitionPaymentController.getTuitionPaymentsByUserId(req, res);
    })
)

router.get('/v1/user/tuition-payment/not-paid',
    requireRoles([]),
    asyncHandler(TuitionPaymentController.checkTuitionPaymentNotPaid)
)

// Học sinh xem thống kê tổng quan về các khoản học phí của mình
router.get('/v1/user/tuition-summary',
    requireRoles([]),
    asyncHandler((req, res) => {
        req.params.userId = req.user.id;
        return TuitionPaymentController.getUserTuitionSummary(req, res);
    })
)

// Học sinh xem danh sách học phí của các lớp đã tham gia trong một tháng cụ thể
router.get('/v1/user/class-tuition/:month',
    requireRoles([]),
    asyncHandler(TuitionPaymentController.getStudentClassTuitionsByMonth)
)

// Admin xem danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
router.get('/v1/admin/user/:userId/class-tuition/:month',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(TuitionPaymentController.getStudentClassTuitionsByMonthAdmin)
)

// Lấy thông tin chi tiết một khoản đóng học phí (chỉ admin, giáo viên, trợ giảng và chính học sinh đó)
router.get('/v1/admin/tuition-payment/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(TuitionPaymentController.getTuitionPaymentById)
)

// Học sinh xem chi tiết khoản đóng học phí của mình
router.get('/v1/user/tuition-payment/:id',
    requireRoles([]),
    asyncHandler(TuitionPaymentController.getUserTuitionPaymentById)
)

// Tạo mới khoản đóng học phí cho học sinh (chỉ admin, giáo viên)
router.post('/v1/admin/tuition-payment',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(TuitionPaymentController.createTuitionPayment)
)

// Tạo khoản đóng học phí cho tất cả học sinh trong hệ thống (chỉ admin, giáo viên)
router.post('/v1/admin/tuition-payment/batch',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(TuitionPaymentController.createTuitionPaymentsForAllStudents)
)

// Cập nhật thông tin khoản đóng học phí (chỉ admin, giáo viên)
router.put('/v1/admin/tuition-payment/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(TuitionPaymentController.updateTuitionPayment)
)

// Tính toán lại số tiền học phí dự kiến cho một khoản đóng học phí (chỉ admin, giáo viên)
router.post('/v1/admin/tuition-payment/:id/recalculate',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(TuitionPaymentController.recalculateExpectedAmount)
)

// Xóa khoản đóng học phí (chỉ admin, giáo viên)
router.delete('/v1/admin/tuition-payment/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(TuitionPaymentController.deleteTuitionPayment)
)


export default router
