import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import UserType from '../constants/UserType.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import uploadGoogleImageMiddleware from '../middlewares/imageGoogleUpload.js'
import * as ExamController from '../controllers/ExamController.js'
import uploadPDF from '../middlewares/pdfGoogleUpload.js'
import { checkAndSubmitTimedOutExams } from '../utils/examAutoSubmit.js'

const router = express.Router()

router.get('/v1/user/exam/newest',
    // requireRoles([]),
    asyncHandler(ExamController.getNewestExam)
)
router.get('/v1/user/exam/saved',
    requireRoles([]),
    async<PERSON>and<PERSON>(ExamController.getSavedExams)
)
router.get('/v1/admin/exam',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ExamController.getExam)
)

router.get('/v1/user/exam/:examId/related',
    requireRoles([]),
    asyncHandler(ExamController.getRelatedExams)
)

router.get('/v1/user/exam',
    requireRoles([]),
    asyncHandler(ExamController.getExamPublic)
)

router.get('/v1/admin/exam/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT, UserType.STUDENT]),
    asyncHandler(ExamController.getExamById)
)

router.get('/v1/admin/exam/:examId/questions',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ExamController.getQuestionByExamId)
)

router.get('/v1/user/exam/:examId/questions',
    requireRoles([]),
    asyncHandler(ExamController.getPublicQuestionByExamId)
)

router.get('/v1/user/exam/:id',
    requireRoles([]),
    asyncHandler(ExamController.getExamPublicById)
)

router.get('/v1/user/exam/:examId/related',
    requireRoles([]),
    asyncHandler(ExamController.getRelatedExams)
)

router.post('/v1/admin/exam',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    uploadGoogleImageMiddleware.fields([
        { name: 'examImage', maxCount: 1 },
        { name: 'questionImages', maxCount: 20 },
        { name: 'statementImages', maxCount: 20 },
        { name: 'solutionImages', maxCount: 20 }
    ]),
    asyncHandler(ExamController.postExam)
)

router.post('/v1/admin/exam/:id/upload-pdf',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    uploadPDF.single('pdf'),
    asyncHandler(ExamController.uploadSolutionPdf)
)

router.post('/v1/user/save-exam',
    requireRoles([]),
    asyncHandler(ExamController.saveExamForUser)
)

router.post('/v1/user/submit-exam',
    requireRoles([]),
    asyncHandler(ExamController.submitExamAPI)
)


router.put('/v1/admin/exam/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ExamController.putExam)
)
router.put('/v1/admin/exam/:id/image',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    uploadGoogleImageMiddleware.single('examImage'),
    asyncHandler(ExamController.putImageExam)
)
router.delete('/v1/admin/exam/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ExamController.deleteExam)
)

// Route để tự động check và nộp các bài thi đã hết thời gian (POST)
router.post('/v1/public/exam/check-and-submit-timed-out',
    asyncHandler(async (req, res) => {
        try {
            console.log('🔍 Checking for timed-out exams via POST...');

            // Gọi hàm check và submit các bài thi đã hết thời gian
            const autoSubmittedAttempts = await checkAndSubmitTimedOutExams();

            // Trả về kết quả
            res.status(200).json({
                success: true,
                message: `Đã kiểm tra và tự động nộp ${autoSubmittedAttempts.length} bài thi hết thời gian`,
                data: {
                    totalChecked: autoSubmittedAttempts.length,
                    autoSubmittedAttempts: autoSubmittedAttempts.map(attempt => ({
                        attemptId: attempt.id,
                        studentId: attempt.studentId,
                        examId: attempt.examId,
                        score: attempt.score,
                        endTime: attempt.endTime,
                        autoSubmitted: true
                    }))
                }
            });

        } catch (error) {
            console.error('❌ Error in check-and-submit-timed-out POST route:', error);
            res.status(500).json({
                success: false,
                message: 'Lỗi khi kiểm tra và nộp bài thi hết thời gian',
                error: error.message
            });
        }
    })
)

// Route để tự động check và nộp các bài thi đã hết thời gian (GET - dễ test hơn)
router.get('/v1/public/exam/check-and-submit-timed-out',
    asyncHandler(async (req, res) => {
        try {
            console.log('🔍 Checking for timed-out exams via GET...');

            // Gọi hàm check và submit các bài thi đã hết thời gian
            const autoSubmittedAttempts = await checkAndSubmitTimedOutExams();

            // Trả về kết quả
            res.status(200).json({
                success: true,
                message: `Đã kiểm tra và tự động nộp ${autoSubmittedAttempts.length} bài thi hết thời gian`,
                data: {
                    totalChecked: autoSubmittedAttempts.length,
                    autoSubmittedAttempts: autoSubmittedAttempts.map(attempt => ({
                        attemptId: attempt.id,
                        studentId: attempt.studentId,
                        examId: attempt.examId,
                        score: attempt.score,
                        endTime: attempt.endTime,
                        autoSubmitted: true
                    }))
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('❌ Error in check-and-submit-timed-out GET route:', error);
            res.status(500).json({
                success: false,
                message: 'Lỗi khi kiểm tra và nộp bài thi hết thời gian',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    })
)

export default router