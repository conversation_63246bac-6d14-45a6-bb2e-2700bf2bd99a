import { BeeMathLogo } from '../logo/BeeMathLogo';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toggleCloseSidebar, toggleDropdown, toggleTuitionDropdown } from '../../features/sidebar/sidebarSlice';
import { logout } from '../../features/auth/authSlice';
import MenuSidebar from './MenuSidebar';
import Choice from './Choice';
import HeaderSidebar from './HeaderSidebar';
import UserSidebar from './UserSidebar';

const AdminSidebar = () => {
    const dropdown = useSelector(state => state.sidebar.dropdownOpen);
    const tuitionDropdown = useSelector(state => state.sidebar.tuitionDropdownOpen);
    const closeSidebar = useSelector(state => state.sidebar.closeSidebar);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const handleLogout = async () => {
        await dispatch(logout());
        navigate('/login');
    };

    const icon1 = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.33333 10.8333H3.33333C3.11232 10.8333 2.90036 10.9211 2.74408 11.0774C2.5878 11.2337 2.5 11.4457 2.5 11.6667V16.6667C2.5 16.8877 2.5878 17.0996 2.74408 17.2559C2.90036 17.4122 3.11232 17.5 3.33333 17.5H8.33333C8.55435 17.5 8.76631 17.4122 8.92259 17.2559C9.07887 17.0996 9.16667 16.8877 9.16667 16.6667V11.6667C9.16667 11.4457 9.07887 11.2337 8.92259 11.0774C8.76631 10.9211 8.55435 10.8333 8.33333 10.8333ZM7.5 15.8333H4.16667V12.5H7.5V15.8333ZM16.6667 2.5H11.6667C11.4457 2.5 11.2337 2.5878 11.0774 2.74408C10.9211 2.90036 10.8333 3.11232 10.8333 3.33333V8.33333C10.8333 8.55435 10.9211 8.76631 11.0774 8.92259C11.2337 9.07887 11.4457 9.16667 11.6667 9.16667H16.6667C16.8877 9.16667 17.0996 9.07887 17.2559 8.92259C17.4122 8.76631 17.5 8.55435 17.5 8.33333V3.33333C17.5 3.11232 17.4122 2.90036 17.2559 2.74408C17.0996 2.5878 16.8877 2.5 16.6667 2.5V2.5ZM15.8333 7.5H12.5V4.16667H15.8333V7.5ZM16.6667 13.3333H15V11.6667C15 11.4457 14.9122 11.2337 14.7559 11.0774C14.5996 10.9211 14.3877 10.8333 14.1667 10.8333C13.9457 10.8333 13.7337 10.9211 13.5774 11.0774C13.4211 11.2337 13.3333 11.4457 13.3333 11.6667V13.3333H11.6667C11.4457 13.3333 11.2337 13.4211 11.0774 13.5774C10.9211 13.7337 10.8333 13.9457 10.8333 14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15H13.3333V16.6667C13.3333 16.8877 13.4211 17.0996 13.5774 17.2559C13.7337 17.4122 13.9457 17.5 14.1667 17.5C14.3877 17.5 14.5996 17.4122 14.7559 17.2559C14.9122 17.0996 15 16.8877 15 16.6667V15H16.6667C16.8877 15 17.0996 14.9122 17.2559 14.7559C17.4122 14.5996 17.5 14.3877 17.5 14.1667C17.5 13.9457 17.4122 13.7337 17.2559 13.5774C17.0996 13.4211 16.8877 13.3333 16.6667 13.3333ZM8.33333 2.5H3.33333C3.11232 2.5 2.90036 2.5878 2.74408 2.74408C2.5878 2.90036 2.5 3.11232 2.5 3.33333V8.33333C2.5 8.55435 2.5878 8.76631 2.74408 8.92259C2.90036 9.07887 3.11232 9.16667 3.33333 9.16667H8.33333C8.55435 9.16667 8.76631 9.07887 8.92259 8.92259C9.07887 8.76631 9.16667 8.55435 9.16667 8.33333V3.33333C9.16667 3.11232 9.07887 2.90036 8.92259 2.74408C8.76631 2.5878 8.55435 2.5 8.33333 2.5V2.5ZM7.5 7.5H4.16667V4.16667H7.5V7.5Z" fill="#71839B" />
            </svg>
        </div>
    )

    const icon2 = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.0917 6.07499C18.0142 5.99688 17.9221 5.93489 17.8205 5.89258C17.719 5.85027 17.61 5.82849 17.5 5.82849C17.39 5.82849 17.2811 5.85027 17.1795 5.89258C17.078 5.93489 16.9858 5.99688 16.9084 6.07499L11.6667 11.325L8.0917 7.74166C8.01423 7.66355 7.92206 7.60155 7.82051 7.55925C7.71896 7.51694 7.61004 7.49516 7.50003 7.49516C7.39002 7.49516 7.2811 7.51694 7.17955 7.55925C7.078 7.60155 6.98583 7.66355 6.90836 7.74166L1.90836 12.7417C1.83026 12.8191 1.76826 12.9113 1.72595 13.0128C1.68365 13.1144 1.66187 13.2233 1.66187 13.3333C1.66187 13.4433 1.68365 13.5523 1.72595 13.6538C1.76826 13.7554 1.83026 13.8475 1.90836 13.925C1.98583 14.0031 2.078 14.0651 2.17955 14.1074C2.2811 14.1497 2.39002 14.1715 2.50003 14.1715C2.61004 14.1715 2.71896 14.1497 2.82051 14.1074C2.92206 14.0651 3.01423 14.0031 3.0917 13.925L7.50003 9.50832L11.075 13.0917C11.1525 13.1698 11.2447 13.2318 11.3462 13.2741C11.4478 13.3164 11.5567 13.3382 11.6667 13.3382C11.7767 13.3382 11.8856 13.3164 11.9872 13.2741C12.0887 13.2318 12.1809 13.1698 12.2584 13.0917L18.0917 7.25832C18.1698 7.18085 18.2318 7.08869 18.2741 6.98714C18.3164 6.88559 18.3382 6.77667 18.3382 6.66666C18.3382 6.55665 18.3164 6.44773 18.2741 6.34618C18.2318 6.24463 18.1698 6.15246 18.0917 6.07499V6.07499Z" fill="#71839B" />
            </svg>
        </div>
    )

    const iconTuition = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17317C0.00433284 8.00043 -0.193701 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8079C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34872 18.9426 4.80684 17.0679 2.9321C15.1932 1.05736 12.6513 0.00286757 10 0ZM10 18.3333C8.35183 18.3333 6.74066 17.8446 5.37025 16.9289C3.99984 16.0132 2.93174 14.7117 2.30101 13.189C1.67028 11.6663 1.50525 9.99076 1.82679 8.37425C2.14834 6.75774 2.94201 5.27288 4.10745 4.10744C5.27289 2.94201 6.75774 2.14833 8.37425 1.82679C9.99076 1.50525 11.6663 1.67027 13.189 2.301C14.7118 2.93173 16.0132 3.99984 16.9289 5.37025C17.8446 6.74066 18.3333 8.35182 18.3333 10C18.3309 12.2094 17.4522 14.3276 15.8899 15.8899C14.3276 17.4522 12.2094 18.3309 10 18.3333ZM13.3333 10C13.3333 10.442 13.1577 10.866 12.8452 11.1785C12.5326 11.4911 12.1087 11.6667 11.6667 11.6667H10V14.1667C10 14.3877 9.9122 14.5996 9.75592 14.7559C9.59964 14.9122 9.38768 15 9.16667 15C8.94566 15 8.73369 14.9122 8.57741 14.7559C8.42113 14.5996 8.33334 14.3877 8.33334 14.1667V11.6667C8.33334 11.2246 8.50893 10.8007 8.82149 10.4882C9.13405 10.1756 9.55798 10 10 10H11.6667V5.83333H8.33334C8.11232 5.83333 7.90036 5.74554 7.74408 5.58926C7.5878 5.43297 7.5 5.22101 7.5 5C7.5 4.77899 7.5878 4.56702 7.74408 4.41074C7.90036 4.25446 8.11232 4.16667 8.33334 4.16667H11.6667C12.1087 4.16667 12.5326 4.34226 12.8452 4.65482C13.1577 4.96738 13.3333 5.39131 13.3333 5.83333V10Z" fill="#71839B" />
            </svg>
        </div>
    )

    const icon3 = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 10.9834V8.33335C14.9988 7.15262 14.5798 6.01041 13.8172 5.109C13.0546 4.20759 11.9976 3.60514 10.8334 3.40835V2.50002C10.8334 2.27901 10.7456 2.06704 10.5893 1.91076C10.433 1.75448 10.2211 1.66669 10 1.66669C9.77903 1.66669 9.56707 1.75448 9.41079 1.91076C9.2545 2.06704 9.16671 2.27901 9.16671 2.50002V3.40835C8.00249 3.60514 6.94548 4.20759 6.18286 5.109C5.42025 6.01041 5.00124 7.15262 5.00004 8.33335V10.9834C4.51375 11.1553 4.09254 11.4734 3.79416 11.8941C3.49577 12.3149 3.33482 12.8176 3.33337 13.3334V15C3.33337 15.221 3.42117 15.433 3.57745 15.5893C3.73373 15.7456 3.94569 15.8334 4.16671 15.8334H6.78337C6.97528 16.5395 7.39421 17.1629 7.97554 17.6073C8.55687 18.0517 9.26829 18.2925 10 18.2925C10.7318 18.2925 11.4432 18.0517 12.0245 17.6073C12.6059 17.1629 13.0248 16.5395 13.2167 15.8334H15.8334C16.0544 15.8334 16.2663 15.7456 16.4226 15.5893C16.5789 15.433 16.6667 15.221 16.6667 15V13.3334C16.6653 12.8176 16.5043 12.3149 16.2059 11.8941C15.9075 11.4734 15.4863 11.1553 15 10.9834ZM6.66671 8.33335C6.66671 7.4493 7.0179 6.60145 7.64302 5.97633C8.26814 5.35121 9.11599 5.00002 10 5.00002C10.8841 5.00002 11.7319 5.35121 12.3571 5.97633C12.9822 6.60145 13.3334 7.4493 13.3334 8.33335V10.8334H6.66671V8.33335ZM10 16.6667C9.70918 16.6649 9.42385 16.5871 9.1724 16.4409C8.92095 16.2947 8.71213 16.0852 8.56671 15.8334H11.4334C11.2879 16.0852 11.0791 16.2947 10.8277 16.4409C10.5762 16.5871 10.2909 16.6649 10 16.6667ZM15 14.1667H5.00004V13.3334C5.00004 13.1123 5.08784 12.9004 5.24412 12.7441C5.4004 12.5878 5.61236 12.5 5.83337 12.5H14.1667C14.3877 12.5 14.5997 12.5878 14.756 12.7441C14.9122 12.9004 15 13.1123 15 13.3334V14.1667Z" fill="#71839B" />
            </svg>
        </div>
    )
    const icon4 = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.5833 10.55C16.4497 10.3979 16.3761 10.2024 16.3761 9.99999C16.3761 9.79757 16.4497 9.60207 16.5833 9.44999L17.65 8.24999C17.7675 8.11888 17.8405 7.95391 17.8585 7.77875C17.8765 7.60359 17.8385 7.42723 17.75 7.27499L16.0833 4.39166C15.9957 4.2396 15.8624 4.11906 15.7023 4.04723C15.5422 3.97541 15.3635 3.95596 15.1917 3.99166L13.625 4.30833C13.4256 4.34952 13.2181 4.31632 13.0416 4.21499C12.865 4.11367 12.7317 3.95123 12.6667 3.75833L12.1583 2.23333C12.1024 2.06781 11.9959 1.92405 11.8539 1.82236C11.7118 1.72068 11.5414 1.66621 11.3667 1.66666H8.03333C7.85161 1.65718 7.67177 1.70743 7.5213 1.80976C7.37082 1.91208 7.25798 2.06084 7.19999 2.23333L6.73333 3.75833C6.66833 3.95123 6.53497 4.11367 6.35842 4.21499C6.18187 4.31632 5.97434 4.34952 5.77499 4.30833L4.16666 3.99166C4.00379 3.96864 3.83774 3.99435 3.68945 4.06553C3.54116 4.13671 3.41725 4.25018 3.33333 4.39166L1.66666 7.27499C1.57596 7.42554 1.53518 7.6009 1.55015 7.77601C1.56511 7.95113 1.63506 8.11703 1.74999 8.24999L2.80833 9.44999C2.94193 9.60207 3.01561 9.79757 3.01561 9.99999C3.01561 10.2024 2.94193 10.3979 2.80833 10.55L1.74999 11.75C1.63506 11.883 1.56511 12.0489 1.55015 12.224C1.53518 12.3991 1.57596 12.5745 1.66666 12.725L3.33333 15.6083C3.42091 15.7604 3.55426 15.8809 3.71437 15.9528C3.87448 16.0246 4.05318 16.044 4.225 16.0083L5.79166 15.6917C5.99101 15.6505 6.19854 15.6837 6.37509 15.785C6.55164 15.8863 6.685 16.0488 6.74999 16.2417L7.25833 17.7667C7.31631 17.9391 7.42916 18.0879 7.57963 18.1902C7.73011 18.2926 7.90994 18.3428 8.09166 18.3333H11.425C11.5997 18.3338 11.7701 18.2793 11.9122 18.1776C12.0542 18.0759 12.1608 17.9322 12.2167 17.7667L12.725 16.2417C12.79 16.0488 12.9234 15.8863 13.0999 15.785C13.2764 15.6837 13.484 15.6505 13.6833 15.6917L15.25 16.0083C15.4218 16.044 15.6005 16.0246 15.7606 15.9528C15.9207 15.8809 16.0541 15.7604 16.1417 15.6083L17.8083 12.725C17.8968 12.5728 17.9348 12.3964 17.9168 12.2212C17.8989 12.0461 17.8259 11.8811 17.7083 11.75L16.5833 10.55ZM15.3417 11.6667L16.0083 12.4167L14.9417 14.2667L13.9583 14.0667C13.3581 13.944 12.7338 14.0459 12.2038 14.3532C11.6738 14.6604 11.2751 15.1515 11.0833 15.7333L10.7667 16.6667H8.63333L8.33333 15.7167C8.14154 15.1349 7.74281 14.6437 7.21283 14.3365C6.68285 14.0293 6.05851 13.9273 5.45833 14.05L4.47499 14.25L3.39166 12.4083L4.05833 11.6583C4.46829 11.2 4.69494 10.6066 4.69494 9.99166C4.69494 9.37672 4.46829 8.78335 4.05833 8.32499L3.39166 7.57499L4.45833 5.74166L5.44166 5.94166C6.04185 6.06435 6.66618 5.96239 7.19617 5.65516C7.72615 5.34792 8.12487 4.85679 8.31666 4.27499L8.63333 3.33333H10.7667L11.0833 4.28333C11.2751 4.86513 11.6738 5.35626 12.2038 5.66349C12.7338 5.97073 13.3581 6.07268 13.9583 5.94999L14.9417 5.74999L16.0083 7.59999L15.3417 8.34999C14.9363 8.80729 14.7125 9.39723 14.7125 10.0083C14.7125 10.6194 14.9363 11.2094 15.3417 11.6667V11.6667ZM9.69999 6.66666C9.04072 6.66666 8.39626 6.86216 7.84809 7.22843C7.29993 7.5947 6.87269 8.1153 6.6204 8.72438C6.3681 9.33347 6.30209 10.0037 6.43071 10.6503C6.55933 11.2969 6.8768 11.8908 7.34297 12.357C7.80915 12.8232 8.40309 13.1407 9.04969 13.2693C9.6963 13.3979 10.3665 13.3319 10.9756 13.0796C11.5847 12.8273 12.1053 12.4001 12.4716 11.8519C12.8378 11.3037 13.0333 10.6593 13.0333 9.99999C13.0333 9.11594 12.6821 8.26809 12.057 7.64297C11.4319 7.01785 10.584 6.66666 9.69999 6.66666V6.66666ZM9.69999 11.6667C9.37036 11.6667 9.04813 11.5689 8.77404 11.3858C8.49996 11.2026 8.28634 10.9423 8.1602 10.6378C8.03405 10.3333 8.00104 9.99815 8.06535 9.67484C8.12966 9.35154 8.2884 9.05457 8.52148 8.82148C8.75457 8.5884 9.05154 8.42966 9.37484 8.36535C9.69815 8.30104 10.0333 8.33405 10.3378 8.46019C10.6423 8.58634 10.9026 8.79996 11.0858 9.07404C11.2689 9.34813 11.3667 9.67036 11.3667 9.99999C11.3667 10.442 11.1911 10.8659 10.8785 11.1785C10.5659 11.4911 10.142 11.6667 9.69999 11.6667Z" fill="#71839B" />
            </svg>
        </div>
    )
    const iconAttendance = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.6667 2.5H15V1.66667C15 1.44565 14.9122 1.23369 14.7559 1.07741C14.5996 0.921133 14.3877 0.833333 14.1667 0.833333C13.9457 0.833333 13.7337 0.921133 13.5774 1.07741C13.4211 1.23369 13.3333 1.44565 13.3333 1.66667V2.5H6.66667V1.66667C6.66667 1.44565 6.5789 1.23369 6.42262 1.07741C6.26634 0.921133 6.05435 0.833333 5.83333 0.833333C5.61232 0.833333 5.40036 0.921133 5.24408 1.07741C5.0878 1.23369 5 1.44565 5 1.66667V2.5H3.33333C2.89131 2.5 2.46738 2.67559 2.15482 2.98816C1.84226 3.30072 1.66667 3.72464 1.66667 4.16667V16.6667C1.66667 17.1087 1.84226 17.5326 2.15482 17.8452C2.46738 18.1577 2.89131 18.3333 3.33333 18.3333H16.6667C17.1087 18.3333 17.5326 18.1577 17.8452 17.8452C18.1577 17.5326 18.3333 17.1087 18.3333 16.6667V4.16667C18.3333 3.72464 18.1577 3.30072 17.8452 2.98816C17.5326 2.67559 17.1087 2.5 16.6667 2.5ZM16.6667 16.6667H3.33333V8.33333H16.6667V16.6667ZM16.6667 6.66667H3.33333V4.16667H5V5C5 5.22101 5.0878 5.43298 5.24408 5.58926C5.40036 5.74554 5.61232 5.83333 5.83333 5.83333C6.05435 5.83333 6.26634 5.74554 6.42262 5.58926C6.5789 5.43298 6.66667 5.22101 6.66667 5V4.16667H13.3333V5C13.3333 5.22101 13.4211 5.43298 13.5774 5.58926C13.7337 5.74554 13.9457 5.83333 14.1667 5.83333C14.3877 5.83333 14.5996 5.74554 14.7559 5.58926C14.9122 5.43298 15 5.22101 15 5V4.16667H16.6667V6.66667Z" fill="#71839B" />
            </svg>
        </div>
    )

    const icon5 = (
        <div data-svg-wrapper className="relative">
            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.4916 10.8333L8.57496 12.7416C8.49685 12.8191 8.43486 12.9113 8.39255 13.0128C8.35024 13.1144 8.32846 13.2233 8.32846 13.3333C8.32846 13.4433 8.35024 13.5522 8.39255 13.6538C8.43486 13.7553 8.49685 13.8475 8.57496 13.925C8.65243 14.0031 8.7446 14.0651 8.84615 14.1074C8.9477 14.1497 9.05662 14.1715 9.16663 14.1715C9.27664 14.1715 9.38556 14.1497 9.48711 14.1074C9.58866 14.0651 9.68082 14.0031 9.75829 13.925L13.0916 10.5916C13.1675 10.5124 13.227 10.4189 13.2666 10.3166C13.35 10.1138 13.35 9.8862 13.2666 9.68331C13.227 9.58102 13.1675 9.48757 13.0916 9.40831L9.75829 6.07498C9.68059 5.99728 9.58835 5.93565 9.48683 5.8936C9.38532 5.85155 9.27651 5.8299 9.16663 5.8299C9.05674 5.8299 8.94794 5.85155 8.84642 5.8936C8.7449 5.93565 8.65266 5.99728 8.57496 6.07498C8.49726 6.15268 8.43563 6.24492 8.39358 6.34644C8.35153 6.44796 8.32988 6.55677 8.32988 6.66665C8.32988 6.77653 8.35153 6.88534 8.39358 6.98686C8.43563 7.08837 8.49726 7.18062 8.57496 7.25831L10.4916 9.16665H2.49996C2.27895 9.16665 2.06698 9.25445 1.9107 9.41073C1.75442 9.56701 1.66663 9.77897 1.66663 9.99998C1.66663 10.221 1.75442 10.433 1.9107 10.5892C2.06698 10.7455 2.27895 10.8333 2.49996 10.8333H10.4916ZM9.99996 1.66665C8.44254 1.65969 6.91433 2.08933 5.58868 2.90681C4.26304 3.72429 3.193 4.89691 2.49996 6.29165C2.4005 6.49056 2.38414 6.72083 2.45446 6.93181C2.52479 7.14279 2.67605 7.31719 2.87496 7.41665C3.07387 7.5161 3.30415 7.53247 3.51512 7.46214C3.7261 7.39182 3.9005 7.24056 3.99996 7.04165C4.52679 5.97775 5.32815 5.07383 6.32125 4.4233C7.31435 3.77278 8.46314 3.39925 9.64892 3.34131C10.8347 3.28337 12.0144 3.54313 13.0662 4.09374C14.118 4.64435 15.0037 5.46584 15.6317 6.47331C16.2598 7.48078 16.6074 8.63769 16.6386 9.82447C16.6699 11.0112 16.3837 12.1848 15.8096 13.224C15.2354 14.2631 14.3942 15.1301 13.3729 15.7353C12.3516 16.3406 11.1871 16.6621 9.99996 16.6666C8.75736 16.672 7.53842 16.327 6.48304 15.671C5.42765 15.0151 4.57859 14.0749 4.03329 12.9583C3.93384 12.7594 3.75944 12.6081 3.54846 12.5378C3.33748 12.4675 3.10721 12.4839 2.90829 12.5833C2.70938 12.6828 2.55812 12.8572 2.4878 13.0681C2.41747 13.2791 2.43384 13.5094 2.53329 13.7083C3.19398 15.0379 4.19789 16.1668 5.44119 16.9784C6.68448 17.7899 8.122 18.2545 9.60506 18.3241C11.0881 18.3938 12.5629 18.066 13.8767 17.3746C15.1906 16.6832 16.2959 15.6533 17.0784 14.3915C17.8608 13.1297 18.2919 11.6818 18.327 10.1975C18.3622 8.7132 18.0002 7.24647 17.2785 5.94901C16.5568 4.65154 15.5015 3.57045 14.2219 2.81757C12.9422 2.06469 11.4847 1.66735 9.99996 1.66665V1.66665Z" fill="#71839B" />
            </svg>
        </div>
    )

    const notification = (
        <div className="w-5 h-5 px-1.5 py-0.5 bg-[#ff472e] rounded-full flex-col justify-center items-center gap-2.5 inline-flex">
            <div className="text-center text-white text-xs font-normal font-bevietnam leading-3">2</div>
        </div>
    )


    return (
        <div className={`fixed left-0 flex flex-col min-h-screen justify-between bg-white ${closeSidebar ? '' : 'w-[16rem]'} p-[1.25rem] shadow-[0px_1px_8px_2px_rgba(20,20,20,0.08)]`}>
            <div className="flex-col w-full justify-start items-start gap-5 inline-flex">
                <HeaderSidebar />
                <div className="flex flex-col gap-3 w-full">
                    {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon1} text={'Tổng quan'} /> */}

                    <button
                        className={`p-3 w-full justify-center items-center rounded-lg gap-4 inline-flex
                            ${dropdown
                                ? 'bg-[#253f61] text-white'
                                : 'bg-white text-[#253f61] hover:bg-[#f0f4fa] hover:text-[#253f61]'
                            }`}
                        onClick={() => dispatch(toggleDropdown())}
                    >
                        <div data-svg-wrapper className="relative">
                            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.0751 6.26667C17.0703 6.24469 17.0703 6.22197 17.0751 6.2C17.071 6.18076 17.071 6.1609 17.0751 6.14166V6.06667L17.0251 5.94167C17.0047 5.90756 16.9795 5.87666 16.9501 5.85L16.8751 5.78333H16.8334L13.5501 3.70833L10.4501 1.79167C10.3783 1.73477 10.2963 1.69232 10.2084 1.66667H10.1417C10.0672 1.65423 9.99121 1.65423 9.91672 1.66667H9.83339C9.73659 1.68808 9.64378 1.72464 9.55839 1.775L3.33339 5.65L3.25839 5.70833L3.18339 5.775L3.10006 5.83333L3.05839 5.88333L3.00839 6.00833V6.08333V6.13333C3.0003 6.18859 3.0003 6.24474 3.00839 6.3V13.575C3.00811 13.7166 3.04392 13.856 3.11245 13.9799C3.18098 14.1038 3.27996 14.2083 3.40006 14.2833L9.65006 18.15L9.77506 18.2H9.84172C9.98271 18.2447 10.1341 18.2447 10.2751 18.2H10.3417L10.4667 18.15L16.6667 14.3417C16.7868 14.2666 16.8858 14.1622 16.9543 14.0382C17.0229 13.9143 17.0587 13.775 17.0584 13.6333V6.35833C17.0584 6.35833 17.0751 6.3 17.0751 6.26667ZM10.0001 3.475L11.4834 4.39167L6.82506 7.275L5.33339 6.35833L10.0001 3.475ZM9.16672 15.975L4.58339 13.175V7.85L9.16672 10.6833V15.975ZM10.0001 9.21667L8.40839 8.25833L13.0667 5.36667L14.6667 6.35833L10.0001 9.21667ZM15.4167 13.15L10.8334 16V10.6833L15.4167 7.85V13.15Z" fill="#71839B" />
                            </svg>
                        </div>
                        {!closeSidebar && (
                            <div className='flex justify-between items-center flex-row w-[15rem]'>
                                <div className={` text-sm font-medium font-bevietnam leading-none`}>
                                    Quản lý
                                </div>
                                <div data-svg-wrapper className={`relative transition-transform ${dropdown ? "" : "rotate-180"}`} >
                                    <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.1667 7.64166C14.0106 7.48646 13.7994 7.39934 13.5792 7.39934C13.3591 7.39934 13.1478 7.48646 12.9917 7.64166L10 10.5917L7.05004 7.64166C6.8939 7.48646 6.68269 7.39934 6.46254 7.39934C6.24238 7.39934 6.03117 7.48646 5.87504 7.64166C5.79693 7.71913 5.73494 7.8113 5.69263 7.91285C5.65032 8.0144 5.62854 8.12332 5.62854 8.23333C5.62854 8.34334 5.65032 8.45226 5.69263 8.55381C5.73494 8.65536 5.79693 8.74753 5.87504 8.825L9.40837 12.3583C9.48584 12.4364 9.57801 12.4984 9.67956 12.5407C9.78111 12.583 9.89003 12.6048 10 12.6048C10.11 12.6048 10.219 12.583 10.3205 12.5407C10.4221 12.4984 10.5142 12.4364 10.5917 12.3583L14.1667 8.825C14.2448 8.74753 14.3068 8.65536 14.3491 8.55381C14.3914 8.45226 14.4132 8.34334 14.4132 8.23333C14.4132 8.12332 14.3914 8.0144 14.3491 7.91285C14.3068 7.8113 14.2448 7.71913 14.1667 7.64166Z"
                                            fill={dropdown ? "white" : "#253f61"}
                                        />
                                    </svg>
                                </div>
                            </div>
                        )}

                    </button>

                    {dropdown && !closeSidebar && (
                        <div className='flex flex-col w-full'>
                            <Choice route={'/admin/student-management'} text={'Học sinh'} />
                            <Choice route={'/admin/class-management'} text={'Lớp học'} />
                            <Choice route={'/admin/question-management'} text={'Câu hỏi'} />
                            <Choice route={'/admin/question-report-management'} text={'Báo cáo câu hỏi'} />
                            <Choice route={'/admin/exam-management'} text={'Đề thi'} />
                            <Choice route={'/admin/code-management'} text={'Code'} />
                            <Choice route={'/admin/achievement-management'} text={'Thành tích'} />
                            {/* <Choice route={'/admin/theory-management'} text={'Lý thuyết'} /> */}

                        </div>
                    )}
                    <MenuSidebar onClick={() => navigate('/admin/tuition-payment')} route={'/admin/tuition-payment'} icon={iconTuition} text={'Học phí'} />


                    {/* <MenuSidebar onClick={() => navigate('/admin/attendance')} route={'/admin/attendance'} icon={icon3} text={'Điểm danh'} /> */}
                    <MenuSidebar onClick={() => navigate('/admin/article-management')} route={'/admin/article-management'} icon={icon1} text={'Bài viết'} />
                    <MenuSidebar onClick={() => navigate('/admin/homepage-management')} route={'/admin/homepage-management'} icon={icon2} text={'Trang chủ'} />
                    {/* <MenuSidebar onClick={() => navigate('/')} icon={icon2} text={'Báo cáo thống kê'} /> */}

                </div>
            </div>
            <div className="flex-col w-full justify-start items-start gap-3 inline-flex">
                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon3} text={'Thông báo'} icon2={notification} /> */}
                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon4} text={'Trợ giúp'} /> */}
                <MenuSidebar onClick={handleLogout} route={'/'} icon={icon5} text={'Đăng xuất'} />
                <UserSidebar />
            </div>
        </div>
    );
}

export default AdminSidebar;
