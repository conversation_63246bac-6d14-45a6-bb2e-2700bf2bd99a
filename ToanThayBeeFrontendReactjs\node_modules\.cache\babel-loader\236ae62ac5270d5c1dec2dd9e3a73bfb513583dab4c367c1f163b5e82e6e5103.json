{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\PaymentModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { X, Copy, Check, QrCode } from 'lucide-react';\n// import QRCode from 'qrcode.react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentModal = _ref => {\n  _s();\n  let {\n    isOpen,\n    onClose,\n    paymentInfo\n  } = _ref;\n  const [copiedAccount, setCopiedAccount] = useState(null);\n  const [activeTab, setActiveTab] = useState('mbbank');\n  if (!isOpen) return null;\n  const bankAccounts = [{\n    id: 'mbbank',\n    name: 'MB Bank',\n    accountName: 'Ong <PERSON>',\n    accountNumber: '**********',\n    qrValue: \"Ong <PERSON>c <PERSON>oc - MB Bank - ********** - \".concat((paymentInfo === null || paymentInfo === void 0 ? void 0 : paymentInfo.description) || 'Thanh toan hoc phi')\n  }, {\n    id: 'vietcombank',\n    name: 'Vietcombank',\n    accountName: 'Nguyễn Thị Thu Hiền',\n    accountNumber: '*************',\n    qrValue: \"Nguyen Thi Thu Hien - Vietcombank - ************* - \".concat((paymentInfo === null || paymentInfo === void 0 ? void 0 : paymentInfo.description) || 'Thanh toan hoc phi')\n  }];\n  const handleCopy = (text, accountId) => {\n    navigator.clipboard.writeText(text);\n    setCopiedAccount(accountId);\n    setTimeout(() => setCopiedAccount(null), 2000);\n  };\n  const activeBank = bankAccounts.find(bank => bank.id === activeTab);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center p-4 sm:p-6 border-b flex-shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-base sm:text-lg font-semibold text-gray-800\",\n          children: \"Th\\xF4ng tin thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-500 hover:text-gray-700 transition-colors p-1\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [paymentInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 bg-blue-50 p-4 rounded-lg space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-medium text-gray-800 mb-2\",\n            children: \"Th\\xF4ng tin h\\u1ECDc ph\\xED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Th\\xE1ng:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium\",\n                children: paymentInfo.month\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n thanh to\\xE1n:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-red-600\",\n                children: paymentInfo.amount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: \"Ghi ch\\xFA:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium\",\n                children: paymentInfo.note\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"N\\u1ED9i dung chuy\\u1EC3n kho\\u1EA3n:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium\",\n                children: paymentInfo.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleCopy(paymentInfo.description, 'description'),\n                className: \"text-blue-600 hover:text-blue-800\",\n                title: \"Sao ch\\xE9p\",\n                children: copiedAccount === 'description' ? /*#__PURE__*/_jsxDEV(Check, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 56\n                }, this) : /*#__PURE__*/_jsxDEV(Copy, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 78\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex border-b border-gray-200 mb-4\",\n          children: bankAccounts.map(bank => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(bank.id),\n            className: \"px-4 py-2 text-sm font-medium \".concat(activeTab === bank.id ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'),\n            children: bank.name\n          }, bank.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-medium text-gray-800 mb-4\",\n              children: \"Th\\xF4ng tin t\\xE0i kho\\u1EA3n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"Ng\\xE2n h\\xE0ng:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: activeBank.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"T\\xEAn t\\xE0i kho\\u1EA3n:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: activeBank.accountName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"S\\u1ED1 t\\xE0i kho\\u1EA3n:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: activeBank.accountNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleCopy(activeBank.accountNumber, activeBank.id),\n                    className: \"text-blue-600 hover:text-blue-800\",\n                    title: \"Sao ch\\xE9p s\\u1ED1 t\\xE0i kho\\u1EA3n\",\n                    children: copiedAccount === activeBank.id ? /*#__PURE__*/_jsxDEV(Check, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 58\n                    }, this) : /*#__PURE__*/_jsxDEV(Copy, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 124,\n                      columnNumber: 80\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-3 rounded-lg border border-gray-200 shadow-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-gray-500 text-center\",\n              children: \"Qu\\xE9t m\\xE3 QR \\u0111\\u1EC3 thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"N\\u1ED9i dung chuy\\u1EC3n kho\\u1EA3n:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: (paymentInfo === null || paymentInfo === void 0 ? void 0 : paymentInfo.description) || 'Thanh toán học phí'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 p-4 bg-yellow-50 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-yellow-800\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"L\\u01B0u \\xFD:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \" Sau khi chuy\\u1EC3n kho\\u1EA3n, vui l\\xF2ng ch\\u1EE5p m\\xE0n h\\xECnh bi\\xEAn lai v\\xE0 g\\u1EEDi cho gi\\xE1o vi\\xEAn qua Zalo \\u0111\\u1EC3 x\\xE1c nh\\u1EADn thanh to\\xE1n. S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i: **********.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t bg-gray-50 flex justify-end\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors\",\n          children: \"\\u0110\\xF3ng\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentModal, \"9CmN+DSbmkUbbNXg5HrR8wdTwCo=\");\n_c = PaymentModal;\nexport default PaymentModal;\nvar _c;\n$RefreshReg$(_c, \"PaymentModal\");", "map": {"version": 3, "names": ["React", "useState", "X", "Copy", "Check", "QrCode", "jsxDEV", "_jsxDEV", "PaymentModal", "_ref", "_s", "isOpen", "onClose", "paymentInfo", "copiedAccount", "set<PERSON><PERSON><PERSON>A<PERSON>unt", "activeTab", "setActiveTab", "bankAccounts", "id", "name", "accountName", "accountNumber", "qrValue", "concat", "description", "handleCopy", "text", "accountId", "navigator", "clipboard", "writeText", "setTimeout", "activeBank", "find", "bank", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "month", "amount", "note", "title", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/PaymentModal.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { X, Copy, Check, QrCode } from 'lucide-react';\n// import QRCode from 'qrcode.react';\n\nconst PaymentModal = ({ isOpen, onClose, paymentInfo }) => {\n  const [copiedAccount, setCopiedAccount] = useState(null);\n  const [activeTab, setActiveTab] = useState('mbbank');\n\n  if (!isOpen) return null;\n\n  const bankAccounts = [\n    {\n      id: 'mbbank',\n      name: 'MB Bank',\n      accountName: 'Ong K<PERSON>ắ<PERSON> Ng<PERSON>',\n      accountNumber: '**********',\n      qrValue: `Ong Khac Ngoc - MB Bank - ********** - ${paymentInfo?.description || 'Thanh toan hoc phi'}`\n    },\n    {\n      id: 'vietcombank',\n      name: 'Vietcombank',\n      accountName: '<PERSON>uyễ<PERSON>h<PERSON> Thu Hiền',\n      accountNumber: '*************',\n      qrValue: `<PERSON><PERSON><PERSON> - Vietcombank - ************* - ${paymentInfo?.description || 'Thanh toan hoc phi'}`\n    }\n  ];\n\n  const handleCopy = (text, accountId) => {\n    navigator.clipboard.writeText(text);\n    setCopiedAccount(accountId);\n    setTimeout(() => setCopiedAccount(null), 2000);\n  };\n\n  const activeBank = bankAccounts.find(bank => bank.id === activeTab);\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center p-4 sm:p-6 border-b flex-shrink-0\">\n          <h2 className=\"text-base sm:text-lg font-semibold text-gray-800\">Thông tin thanh toán</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700 transition-colors p-1\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {/* Payment Info */}\n          {paymentInfo && (\n            <div className=\"mb-6 bg-blue-50 p-4 rounded-lg space-y-4\">\n              <h3 className=\"font-medium text-gray-800 mb-2\">Thông tin học phí</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <p className=\"text-sm text-gray-500\">Tháng:</p>\n                  <p className=\"font-medium\">{paymentInfo.month}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-500\">Số tiền cần thanh toán:</p>\n                  <p className=\"font-medium text-red-600\">{paymentInfo.amount}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-500\">Ghi chú:</p>\n                  <p className=\"font-medium\">{paymentInfo.note}</p>\n                </div>\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-500\">Nội dung chuyển khoản:</p>\n                <div className=\"flex items-center gap-2\">\n                  <p className=\"font-medium\">{paymentInfo.description}</p>\n                  <button\n                    onClick={() => handleCopy(paymentInfo.description, 'description')}\n                    className=\"text-blue-600 hover:text-blue-800\"\n                    title=\"Sao chép\"\n                  >\n                    {copiedAccount === 'description' ? <Check size={16} /> : <Copy size={16} />}\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Bank Tabs */}\n          <div className=\"flex border-b border-gray-200 mb-4\">\n            {bankAccounts.map(bank => (\n              <button\n                key={bank.id}\n                onClick={() => setActiveTab(bank.id)}\n                className={`px-4 py-2 text-sm font-medium ${activeTab === bank.id\n                  ? 'text-blue-600 border-b-2 border-blue-600'\n                  : 'text-gray-500 hover:text-gray-700'\n                  }`}\n              >\n                {bank.name}\n              </button>\n            ))}\n          </div>\n\n          {/* Bank Account Info */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"font-medium text-gray-800 mb-4\">Thông tin tài khoản</h3>\n              <div className=\"space-y-4\">\n                <div>\n                  <p className=\"text-sm text-gray-500\">Ngân hàng:</p>\n                  <p className=\"font-medium\">{activeBank.name}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-500\">Tên tài khoản:</p>\n                  <p className=\"font-medium\">{activeBank.accountName}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-500\">Số tài khoản:</p>\n                  <div className=\"flex items-center gap-2\">\n                    <p className=\"font-medium\">{activeBank.accountNumber}</p>\n                    <button\n                      onClick={() => handleCopy(activeBank.accountNumber, activeBank.id)}\n                      className=\"text-blue-600 hover:text-blue-800\"\n                      title=\"Sao chép số tài khoản\"\n                    >\n                      {copiedAccount === activeBank.id ? <Check size={16} /> : <Copy size={16} />}\n                    </button>\n                  </div>\n                </div>\n\n              </div>\n\n            </div>\n\n\n            <div className=\"flex flex-col items-center justify-center\">\n              <div className=\"bg-white p-3 rounded-lg border border-gray-200 shadow-sm\">\n                {/* <QRCode \n                  value={activeBank.qrValue}\n                  size={180}\n                  level=\"H\"\n                  includeMargin={true}\n                  renderAs=\"svg\"\n                /> */}\n              </div>\n              <p className=\"mt-2 text-sm text-gray-500 text-center\">Quét mã QR để thanh toán</p>\n            </div>\n          </div>\n          <div className=\"mt-4\">\n            <p className=\"text-sm text-gray-500\">Nội dung chuyển khoản:</p>\n            <p className=\"font-medium\">{paymentInfo?.description || 'Thanh toán học phí'}</p>\n          </div>\n\n          <div className=\"mt-6 p-4 bg-yellow-50 rounded-lg\">\n            <p className=\"text-sm text-yellow-800\">\n              <strong>Lưu ý:</strong> Sau khi chuyển khoản, vui lòng chụp màn hình biên lai và gửi cho giáo viên qua Zalo để xác nhận thanh toán. Số điện thoại: **********.\n            </p>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t bg-gray-50 flex justify-end\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors\"\n          >\n            Đóng\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaymentModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,QAAQ,cAAc;AACrD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,YAAY,GAAGC,IAAA,IAAsC;EAAAC,EAAA;EAAA,IAArC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAY,CAAC,GAAAJ,IAAA;EACpD,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,QAAQ,CAAC;EAEpD,IAAI,CAACU,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMO,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,eAAe;IAC5BC,aAAa,EAAE,YAAY;IAC3BC,OAAO,4CAAAC,MAAA,CAA4C,CAAAX,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,WAAW,KAAI,oBAAoB;EACrG,CAAC,EACD;IACEN,EAAE,EAAE,aAAa;IACjBC,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,qBAAqB;IAClCC,aAAa,EAAE,eAAe;IAC9BC,OAAO,yDAAAC,MAAA,CAAyD,CAAAX,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,WAAW,KAAI,oBAAoB;EAClH,CAAC,CACF;EAED,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;IACtCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,IAAI,CAAC;IACnCZ,gBAAgB,CAACa,SAAS,CAAC;IAC3BI,UAAU,CAAC,MAAMjB,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;EAChD,CAAC;EAED,MAAMkB,UAAU,GAAGf,YAAY,CAACgB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAChB,EAAE,KAAKH,SAAS,CAAC;EAEnE,oBACET,OAAA;IAAK6B,SAAS,EAAC,gFAAgF;IAAAC,QAAA,eAC7F9B,OAAA;MAAK6B,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExG9B,OAAA;QAAK6B,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClF9B,OAAA;UAAI6B,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1FlC,OAAA;UACEmC,OAAO,EAAE9B,OAAQ;UACjBwB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,eAEnE9B,OAAA,CAACL,CAAC;YAACyC,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,KAAK;QAAAC,QAAA,GAEjBxB,WAAW,iBACVN,OAAA;UAAK6B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvD9B,OAAA;YAAI6B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrElC,OAAA;YAAK6B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD9B,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAG6B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/ClC,OAAA;gBAAG6B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAExB,WAAW,CAAC+B;cAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAG6B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChElC,OAAA;gBAAG6B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExB,WAAW,CAACgC;cAAM;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNlC,OAAA;cAAA8B,QAAA,gBACE9B,OAAA;gBAAG6B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjDlC,OAAA;gBAAG6B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAExB,WAAW,CAACiC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAG6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/DlC,OAAA;cAAK6B,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9B,OAAA;gBAAG6B,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAExB,WAAW,CAACY;cAAW;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDlC,OAAA;gBACEmC,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAACb,WAAW,CAACY,WAAW,EAAE,aAAa,CAAE;gBAClEW,SAAS,EAAC,mCAAmC;gBAC7CW,KAAK,EAAC,aAAU;gBAAAV,QAAA,EAEfvB,aAAa,KAAK,aAAa,gBAAGP,OAAA,CAACH,KAAK;kBAACuC,IAAI,EAAE;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGlC,OAAA,CAACJ,IAAI;kBAACwC,IAAI,EAAE;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDlC,OAAA;UAAK6B,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDnB,YAAY,CAAC8B,GAAG,CAACb,IAAI,iBACpB5B,OAAA;YAEEmC,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAACkB,IAAI,CAAChB,EAAE,CAAE;YACrCiB,SAAS,mCAAAZ,MAAA,CAAmCR,SAAS,KAAKmB,IAAI,CAAChB,EAAE,GAC7D,0CAA0C,GAC1C,mCAAmC,CAClC;YAAAkB,QAAA,EAEJF,IAAI,CAACf;UAAI,GAPLe,IAAI,CAAChB,EAAE;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQN,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAI6B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvElC,OAAA;cAAK6B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9B,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAG6B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnDlC,OAAA;kBAAG6B,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEJ,UAAU,CAACb;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAG6B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACvDlC,OAAA;kBAAG6B,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEJ,UAAU,CAACZ;gBAAW;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNlC,OAAA;gBAAA8B,QAAA,gBACE9B,OAAA;kBAAG6B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtDlC,OAAA;kBAAK6B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC9B,OAAA;oBAAG6B,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEJ,UAAU,CAACX;kBAAa;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzDlC,OAAA;oBACEmC,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAACO,UAAU,CAACX,aAAa,EAAEW,UAAU,CAACd,EAAE,CAAE;oBACnEiB,SAAS,EAAC,mCAAmC;oBAC7CW,KAAK,EAAC,uCAAuB;oBAAAV,QAAA,EAE5BvB,aAAa,KAAKmB,UAAU,CAACd,EAAE,gBAAGZ,OAAA,CAACH,KAAK;sBAACuC,IAAI,EAAE;oBAAG;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGlC,OAAA,CAACJ,IAAI;sBAACwC,IAAI,EAAE;oBAAG;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC,eAGNlC,OAAA;YAAK6B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxD9B,OAAA;cAAK6B,SAAS,EAAC;YAA0D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQpE,CAAC,eACNlC,OAAA;cAAG6B,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlC,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB9B,OAAA;YAAG6B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/DlC,OAAA;YAAG6B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE,CAAAxB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,WAAW,KAAI;UAAoB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAENlC,OAAA;UAAK6B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/C9B,OAAA;YAAG6B,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpC9B,OAAA;cAAA8B,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6NACzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlC,OAAA;QAAK6B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eACvD9B,OAAA;UACEmC,OAAO,EAAE9B,OAAQ;UACjBwB,SAAS,EAAC,oFAAoF;UAAAC,QAAA,EAC/F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAtKIF,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAwKlB,eAAeA,YAAY;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}