{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\layouts\\\\UserLayout.jsx\",\n  _s = $RefreshSig$();\nimport Header from \"../components/header/Header\";\nimport UnpaidTuitionModal from \"../components/UnpaidTuitionModal\";\nimport { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { checkTuitionPaymentNotPaid } from \"../features/tuition/tuitionSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserLayout = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [headerHeight, setHeaderHeight] = useState(0);\n  const [showUnpaidModal, setShowUnpaidModal] = useState(false);\n  const dispatch = useDispatch();\n  const {\n    isFirstTimeCheckTuition,\n    tuitionPaymentNotPaid\n  } = useSelector(state => state.tuition);\n\n  // Check if modal has been shown in this session\n  const hasShownUnpaidModal = sessionStorage.getItem('hasShownUnpaidModal') === 'true';\n  useEffect(() => {\n    console.log(\"isFirstTimeCheckTuition\", isFirstTimeCheckTuition);\n    if (!isFirstTimeCheckTuition) return;\n    dispatch(checkTuitionPaymentNotPaid());\n  }, [dispatch, isFirstTimeCheckTuition]);\n\n  // Show modal when unpaid tuition payments are detected (only once per session)\n  useEffect(() => {\n    if (tuitionPaymentNotPaid && tuitionPaymentNotPaid.length > 0 && !hasShownUnpaidModal && !isFirstTimeCheckTuition) {\n      setShowUnpaidModal(true);\n      sessionStorage.setItem('hasShownUnpaidModal', 'true');\n    }\n  }, [tuitionPaymentNotPaid, hasShownUnpaidModal, isFirstTimeCheckTuition]);\n  useEffect(() => {\n    // Get the header height after it's rendered\n    const header = document.querySelector('header');\n    if (header) {\n      setHeaderHeight(header.offsetHeight);\n\n      // Update header height on window resize\n      const handleResize = () => {\n        setHeaderHeight(header.offsetHeight);\n      };\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-row w-full bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-full justify-center sm:mt-0 mt-4\",\n      style: {\n        paddingTop: \"\".concat(headerHeight, \"px\")\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(UnpaidTuitionModal, {\n      isOpen: showUnpaidModal,\n      onClose: () => setShowUnpaidModal(false),\n      unpaidPayments: tuitionPaymentNotPaid\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 9\n  }, this);\n};\n_s(UserLayout, \"3PeQlYIJ0ZV9le4FFciFmQX8q7I=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = UserLayout;\nexport default UserLayout;\nvar _c;\n$RefreshReg$(_c, \"UserLayout\");", "map": {"version": 3, "names": ["Header", "UnpaidTuitionModal", "useEffect", "useState", "useDispatch", "useSelector", "checkTuitionPaymentNotPaid", "jsxDEV", "_jsxDEV", "UserLayout", "_ref", "_s", "children", "headerHeight", "setHeaderHeight", "showUnpaidModal", "setShowUnpaidModal", "dispatch", "isFirstTimeCheckTuition", "tuitionPaymentNotPaid", "state", "tuition", "hasShownUnpaidModal", "sessionStorage", "getItem", "console", "log", "length", "setItem", "header", "document", "querySelector", "offsetHeight", "handleResize", "window", "addEventListener", "removeEventListener", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "paddingTop", "concat", "isOpen", "onClose", "unpaidPayments", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/layouts/UserLayout.jsx"], "sourcesContent": ["import Header from \"../components/header/Header\";\r\nimport UnpaidTuitionModal from \"../components/UnpaidTuitionModal\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { checkTuitionPaymentNotPaid } from \"../features/tuition/tuitionSlice\";\r\n\r\nconst UserLayout = ({ children }) => {\r\n    const [headerHeight, setHeaderHeight] = useState(0);\r\n    const [showUnpaidModal, setShowUnpaidModal] = useState(false);\r\n    const dispatch = useDispatch();\r\n    const { isFirstTimeCheckTuition, tuitionPaymentNotPaid } = useSelector((state) => state.tuition);\r\n\r\n    // Check if modal has been shown in this session\r\n    const hasShownUnpaidModal = sessionStorage.getItem('hasShownUnpaidModal') === 'true';\r\n\r\n    useEffect(() => {\r\n        console.log(\"isFirstTimeCheckTuition\", isFirstTimeCheckTuition);\r\n        if (!isFirstTimeCheckTuition) return;\r\n        dispatch(checkTuitionPaymentNotPaid());\r\n    }, [dispatch, isFirstTimeCheckTuition]);\r\n\r\n    // Show modal when unpaid tuition payments are detected (only once per session)\r\n    useEffect(() => {\r\n        if (tuitionPaymentNotPaid &&\r\n            tuitionPaymentNotPaid.length > 0 &&\r\n            !hasShownUnpaidModal &&\r\n            !isFirstTimeCheckTuition) {\r\n            setShowUnpaidModal(true);\r\n            sessionStorage.setItem('hasShownUnpaidModal', 'true');\r\n        }\r\n    }, [tuitionPaymentNotPaid, hasShownUnpaidModal, isFirstTimeCheckTuition]);\r\n\r\n    useEffect(() => {\r\n        // Get the header height after it's rendered\r\n        const header = document.querySelector('header');\r\n        if (header) {\r\n            setHeaderHeight(header.offsetHeight);\r\n\r\n            // Update header height on window resize\r\n            const handleResize = () => {\r\n                setHeaderHeight(header.offsetHeight);\r\n            };\r\n\r\n            window.addEventListener('resize', handleResize);\r\n            return () => window.removeEventListener('resize', handleResize);\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"flex flex-row w-full bg-gray-50\">\r\n            <Header />\r\n            <div className=\"flex flex-col w-full justify-center sm:mt-0 mt-4\" style={{ paddingTop: `${headerHeight}px` }}>\r\n                {children}\r\n            </div>\r\n\r\n            {/* Unpaid Tuition Modal */}\r\n            <UnpaidTuitionModal\r\n                isOpen={showUnpaidModal}\r\n                onClose={() => setShowUnpaidModal(false)}\r\n                unpaidPayments={tuitionPaymentNotPaid}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\n\r\nexport default UserLayout;"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,6BAA6B;AAChD,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,0BAA0B,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,UAAU,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC5B,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc,uBAAuB;IAAEC;EAAsB,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;;EAEhG;EACA,MAAMC,mBAAmB,GAAGC,cAAc,CAACC,OAAO,CAAC,qBAAqB,CAAC,KAAK,MAAM;EAEpFtB,SAAS,CAAC,MAAM;IACZuB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,uBAAuB,CAAC;IAC/D,IAAI,CAACA,uBAAuB,EAAE;IAC9BD,QAAQ,CAACX,0BAA0B,CAAC,CAAC,CAAC;EAC1C,CAAC,EAAE,CAACW,QAAQ,EAAEC,uBAAuB,CAAC,CAAC;;EAEvC;EACAhB,SAAS,CAAC,MAAM;IACZ,IAAIiB,qBAAqB,IACrBA,qBAAqB,CAACQ,MAAM,GAAG,CAAC,IAChC,CAACL,mBAAmB,IACpB,CAACJ,uBAAuB,EAAE;MAC1BF,kBAAkB,CAAC,IAAI,CAAC;MACxBO,cAAc,CAACK,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;IACzD;EACJ,CAAC,EAAE,CAACT,qBAAqB,EAAEG,mBAAmB,EAAEJ,uBAAuB,CAAC,CAAC;EAEzEhB,SAAS,CAAC,MAAM;IACZ;IACA,MAAM2B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIF,MAAM,EAAE;MACRf,eAAe,CAACe,MAAM,CAACG,YAAY,CAAC;;MAEpC;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACvBnB,eAAe,CAACe,MAAM,CAACG,YAAY,CAAC;MACxC,CAAC;MAEDE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MAC/C,OAAO,MAAMC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACnE;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIzB,OAAA;IAAK6B,SAAS,EAAC,iCAAiC;IAAAzB,QAAA,gBAC5CJ,OAAA,CAACR,MAAM;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVjC,OAAA;MAAK6B,SAAS,EAAC,kDAAkD;MAACK,KAAK,EAAE;QAAEC,UAAU,KAAAC,MAAA,CAAK/B,YAAY;MAAK,CAAE;MAAAD,QAAA,EACxGA;IAAQ;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNjC,OAAA,CAACP,kBAAkB;MACf4C,MAAM,EAAE9B,eAAgB;MACxB+B,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAAC,KAAK,CAAE;MACzC+B,cAAc,EAAE5B;IAAsB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAA9B,EAAA,CAzDKF,UAAU;EAAA,QAGKL,WAAW,EAC+BC,WAAW;AAAA;AAAA2C,EAAA,GAJpEvC,UAAU;AA4DhB,eAAeA,UAAU;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}