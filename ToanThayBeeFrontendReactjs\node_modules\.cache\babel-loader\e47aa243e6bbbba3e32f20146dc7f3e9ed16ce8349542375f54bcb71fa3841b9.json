{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    page,\n    totalPages,\n    total,\n    pageSize\n  } = useSelector(state => state.tuition.pagination);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho bộ lọc\n  const [filterMonth, setFilterMonth] = useState(\"\");\n  const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\n  const [filterClass, setFilterClass] = useState(\"\");\n  const [filterClassId, setFilterClassId] = useState(\"\");\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\n  const [addNote, setAddNote] = useState(\"\");\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editDueDate, setEditDueDate] = useState(\"\");\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\n  const [editNote, setEditNote] = useState(\"\");\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n\n  // useEffect(() => {\n  //   if (!didInit) {\n  //     dispatch(resetFilters());\n  //     setDidInit(true);\n  //   }\n  // }, [dispatch, didInit]);\n\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate expected amount (required if not calculating automatically)\n    if (!addCalculateExpected && !addExpectedAmount) {\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\n      errors.expectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate paid amount (must be a positive number if provided)\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\n      errors.paidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // Validate status (required)\n    if (!addStatus) {\n      errors.status = \"Vui lòng chọn trạng thái\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        status: addStatus,\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Only include expectedAmount if not calculating automatically\n      if (!addCalculateExpected && addExpectedAmount) {\n        paymentData.expectedAmount = Number(addExpectedAmount);\n      }\n\n      // Include paidAmount if provided\n      if (addPaidAmount) {\n        paymentData.paidAmount = Number(addPaidAmount);\n      }\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddExpectedAmount(\"\");\n      setAddExpectedAmountFormatted(\"\");\n      setAddPaidAmount(\"\");\n      setAddPaidAmountFormatted(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddStatus(\"\");\n      setAddNote(\"\");\n      setAddCalculateExpected(false);\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  useEffect(() => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: page,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, page, pageSize]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditExpectedAmount(payment.expectedAmount || \"\");\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\n      setEditPaidAmount(payment.paidAmount || \"\");\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditStatus(payment.status || \"\");\n      setEditNote(payment.note || \"\");\n      setCalculateExpected(false);\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddExpectedAmount(\"\");\n    setAddExpectedAmountFormatted(\"\");\n    setAddPaidAmount(\"\");\n    setAddPaidAmountFormatted(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddStatus(\"\");\n    setAddNote(\"\");\n    setAddCalculateExpected(false);\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const handleCreateByClass = () => {\n    setRightPanelType(\"batchByClass\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditExpectedAmount(\"\");\n    setEditExpectedAmountFormatted(\"\");\n    setEditPaidAmount(\"\");\n    setEditPaidAmountFormatted(\"\");\n    setEditPaymentDate(\"\");\n    setEditStatus(\"\");\n    setEditNote(\"\");\n    setCalculateExpected(false);\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate paid amount (must be a positive number)\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate expected amount (must be a positive number if provided)\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate status (required)\n    if (!editStatus) {\n      errors.editStatus = \"Trạng thái không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\n\n    // Update the actual values with parsed values\n    if (editPaidAmountFormatted) {\n      setEditPaidAmount(parsedPaidAmount);\n    }\n    if (editExpectedAmountFormatted && !calculateExpected) {\n      setEditExpectedAmount(parsedExpectedAmount);\n    }\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\n        status: editStatus,\n        note: editNote,\n        calculateExpected: calculateExpected\n      };\n\n      // Only include expectedAmount if it's provided and not calculating automatically\n      if (parsedExpectedAmount && !calculateExpected) {\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\n      }\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        status: filterStatus,\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n  const getStatusBadge = status => {\n    switch (status) {\n      case \"PAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n          children: \"\\u0110\\xE3 thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this);\n      case \"UNPAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\",\n          children: \"Ch\\u01B0a thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this);\n      case \"OVERDUE\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\",\n          children: \"Qu\\xE1 h\\u1EA1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this);\n      case \"PARTIAL\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\",\n          children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\",\n          children: status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 648,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 654,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 660,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 666,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterStatus,\n              onChange: e => setFilterStatus(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PAID\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"UNPAID\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PARTIAL\",\n                children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconUsers,\n            text: 'Tạo học phí theo lớp',\n            onClick: handleCreateByClass\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 7\n    }, this), tuitionPayments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 798,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xF3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user, _payment$user2, _payment$user3, _payment$user4;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (page - 1) * pageSize + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.lastName) + \" \" + ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.expectedAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.paidAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleView(payment.id, payment.userId, payment.month),\n                    className: \"text-blue-500 hover:text-blue-700\",\n                    title: \"Xem chi ti\\u1EBFt\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 853,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(payment.id),\n                    className: \"text-yellow-500 hover:text-yellow-700\",\n                    title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 865,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(payment.id),\n                    className: \"text-red-500 hover:text-red-700\",\n                    title: \"X\\xF3a\",\n                    children: /*#__PURE__*/_jsxDEV(Trash, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 872,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 19\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 802,\n      columnNumber: 9\n    }, this), viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: page,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: total,\n        limit: pageSize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 883,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 917,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"addCalculateExpected\",\n                    checked: addCalculateExpected,\n                    onChange: e => setAddCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"addCalculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(addCalculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: addExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddExpectedAmount(value);\n                    setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: addCalculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 21\n              }, this), formErrors.expectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 25\n                }, this), \" \", formErrors.expectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.paidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: addPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddPaidAmount(value);\n                    setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 21\n              }, this), formErrors.paidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1010,\n                  columnNumber: 25\n                }, this), \" \", formErrors.paidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 993,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1025,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1032,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.status ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addStatus,\n                onChange: e => setAddStatus(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 21\n              }, this), formErrors.status && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 25\n                }, this), \" \", formErrors.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1071,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1087,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1097,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1086,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500 text-xs font-normal\",\n                  children: \"(kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1102,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n ho\\u1EB7c \\u0111\\u1EC3 tr\\u1ED1ng \\u0111\\u1EC3 t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh\",\n                value: batchAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                    setBatchAmount(value ? parseInt(value, 10) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1105,\n                columnNumber: 21\n              }, this), formErrors.batchAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"N\\u1EBFu \\u0111\\u1EC3 tr\\u1ED1ng, h\\u1EC7 th\\u1ED1ng s\\u1EBD t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1123,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1101,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1128,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1136,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1142,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1146,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1161,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1085,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByClass\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t l\\u1EDBp h\\u1ECDc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1184,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"L\\u1EDBp h\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp h\\u1ECDc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1\",\n                  children: \"L\\u1EDBp 10A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1191,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2\",\n                  children: \"L\\u1EDBp 11A2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1192,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3\",\n                  children: \"L\\u1EDBp 12A3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1193,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1187,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1204,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1205,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1203,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"H\\u1EA1n thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1213,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1211,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n              children: \"T\\u1EA1o h\\u1ECDc ph\\xED theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1183,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1238,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"calculateExpected\",\n                    checked: calculateExpected,\n                    onChange: e => setCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1245,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"calculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh l\\u1EA1i d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1252,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1244,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(calculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: editExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: calculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 21\n              }, this), formErrors.editExpectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editExpectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1241,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1277,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: editPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1278,\n                columnNumber: 21\n              }, this), formErrors.editPaidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editPaidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1291,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1297,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1296,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editStatus ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editStatus,\n                onChange: e => setEditStatus(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1314,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1315,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1316,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1307,\n                columnNumber: 21\n              }, this), formErrors.editStatus && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1320,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editStatus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1319,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1305,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1326,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1339,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1237,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1353,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1362,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1366,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1367,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1368,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1365,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1360,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1376,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1378,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1378,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1379,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1379,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1380,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1380,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1381,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1381,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1375,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1387,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1389,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1391,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1391,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1392,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1392,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1394,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1395,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1393,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1406,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1406,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1407,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1407,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1408,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1388,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1386,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1415,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1420,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1421,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1419,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1425,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1425,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1426,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1426,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1427,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1427,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1418,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1416,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1432,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1433,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1431,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1414,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1442,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1443,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1441,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1446,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1447,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1448,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1449,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1445,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1439,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1358,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1456,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1357,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1466,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1351,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 914,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 897,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1476,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 672,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"ONXDfo4ppg7qx/48eMxseRGgkoc=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "AdminLayout", "FunctionBarAdmin", "Chart", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "page", "totalPages", "total", "pageSize", "pagination", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "filterMonth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterIsPaid", "setFilterIsPaid", "filterOverdue", "setFilterOverdue", "filterClass", "setFilterClass", "filterClassId", "setFilterClassId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addIsPaid", "setAddIsPaid", "addNote", "setAddNote", "editId", "setEditId", "editPaymentDate", "setEditPaymentDate", "editDueDate", "setEditDueDate", "editIsPaid", "setEditIsPaid", "editNote", "setEditNote", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "sortOrder", "status", "filterStatus", "month", "overdue", "userClass", "classId", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "addCalculateExpected", "addExpectedAmount", "expectedAmount", "isNaN", "Number", "addPaidAmount", "paidAmount", "dueDate", "addStatus", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "setAddExpectedAmount", "setAddExpectedAmountFormatted", "setAddPaidAmount", "setAddPaidAmountFormatted", "setAddStatus", "setAddCalculateExpected", "error", "console", "submit", "handleEdit", "response", "unwrap", "payment", "data", "setEditExpectedAmount", "setEditExpectedAmountFormatted", "setEditPaidAmount", "setEditPaidAmountFormatted", "Date", "toISOString", "split", "setEditStatus", "setCalculateExpected", "handleView", "handleAdd", "today", "formattedDate", "year", "getFullYear", "getMonth", "formattedMonth", "concat", "handleBatchAdd", "handleCreateBatchTuition", "handleCreateByClass", "validateEditForm", "editPaidAmount", "editExpectedAmount", "editStatus", "handleUpdateTuitionPayment", "parsedPaidAmount", "editPaidAmountFormatted", "parsedExpectedAmount", "editExpectedAmountFormatted", "calculateExpected", "validateBatchTuitionForm", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "handleDelete", "confirmDelete", "cancelDelete", "getStatusBadge", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "type", "placeholder", "value", "onChange", "target", "Array", "from", "_", "i", "monthStr", "setFilterStatus", "selectedClassId", "onSelect", "onClear", "strokeWidth", "map", "index", "_payment$user", "_payment$user2", "_payment$user3", "_payment$user4", "user", "class", "highSchool", "monthFormatted", "toLocaleDateString", "isOverdue", "title", "currentPage", "onPageChange", "totalItems", "limit", "onSubmit", "role", "checked", "htmlFor", "addExpectedAmountFormatted", "replace", "parseInt", "disabled", "addPaidAmountFormatted", "rows", "required", "ref", "phone", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { page, totalPages, total, pageSize } = useSelector(\r\n    (state) => state.tuition.pagination\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho bộ lọc\r\n  const [filterMonth, setFilterMonth] = useState(\"\");\r\n  const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\r\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  const [filterClass, setFilterClass] = useState(\"\");\r\n  const [filterClassId, setFilterClassId] = useState(\"\");\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\r\n  const [addNote, setAddNote] = useState(\"\");\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editDueDate, setEditDueDate] = useState(\"\");\r\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\r\n  const [editNote, setEditNote] = useState(\"\");\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  // useEffect(() => {\r\n  //   if (!didInit) {\r\n  //     dispatch(resetFilters());\r\n  //     setDidInit(true);\r\n  //   }\r\n  // }, [dispatch, didInit]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate expected amount (required if not calculating automatically)\r\n    if (!addCalculateExpected && !addExpectedAmount) {\r\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\r\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\r\n      errors.expectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate paid amount (must be a positive number if provided)\r\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\r\n      errors.paidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!addStatus) {\r\n      errors.status = \"Vui lòng chọn trạng thái\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        status: addStatus,\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Only include expectedAmount if not calculating automatically\r\n      if (!addCalculateExpected && addExpectedAmount) {\r\n        paymentData.expectedAmount = Number(addExpectedAmount);\r\n      }\r\n\r\n      // Include paidAmount if provided\r\n      if (addPaidAmount) {\r\n        paymentData.paidAmount = Number(addPaidAmount);\r\n      }\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddExpectedAmount(\"\");\r\n      setAddExpectedAmountFormatted(\"\");\r\n      setAddPaidAmount(\"\");\r\n      setAddPaidAmountFormatted(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddStatus(\"\");\r\n      setAddNote(\"\");\r\n      setAddCalculateExpected(false);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: page, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, page, pageSize]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditExpectedAmount(payment.expectedAmount || \"\");\r\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\r\n      setEditPaidAmount(payment.paidAmount || \"\");\r\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditStatus(payment.status || \"\");\r\n      setEditNote(payment.note || \"\");\r\n      setCalculateExpected(false);\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddExpectedAmount(\"\");\r\n    setAddExpectedAmountFormatted(\"\");\r\n    setAddPaidAmount(\"\");\r\n    setAddPaidAmountFormatted(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddStatus(\"\");\r\n    setAddNote(\"\");\r\n    setAddCalculateExpected(false);\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateByClass = () => {\r\n    setRightPanelType(\"batchByClass\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditExpectedAmount(\"\");\r\n    setEditExpectedAmountFormatted(\"\");\r\n    setEditPaidAmount(\"\");\r\n    setEditPaidAmountFormatted(\"\");\r\n    setEditPaymentDate(\"\");\r\n    setEditStatus(\"\");\r\n    setEditNote(\"\");\r\n    setCalculateExpected(false);\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate paid amount (must be a positive number)\r\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\r\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate expected amount (must be a positive number if provided)\r\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\r\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!editStatus) {\r\n      errors.editStatus = \"Trạng thái không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\r\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\r\n\r\n    // Update the actual values with parsed values\r\n    if (editPaidAmountFormatted) {\r\n      setEditPaidAmount(parsedPaidAmount);\r\n    }\r\n\r\n    if (editExpectedAmountFormatted && !calculateExpected) {\r\n      setEditExpectedAmount(parsedExpectedAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\r\n        status: editStatus,\r\n        note: editNote,\r\n        calculateExpected: calculateExpected\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided and not calculating automatically\r\n      if (parsedExpectedAmount && !calculateExpected) {\r\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\r\n      }\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  const getStatusBadge = (status) => {\r\n    switch (status) {\r\n      case \"PAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\">\r\n            Đã thanh toán\r\n          </span>\r\n        );\r\n      case \"UNPAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\">\r\n            Chưa thanh toán\r\n          </span>\r\n        );\r\n      case \"OVERDUE\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\">\r\n            Quá hạn\r\n          </span>\r\n        );\r\n      case \"PARTIAL\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\">\r\n            Thanh toán một phần\r\n          </span>\r\n        );\r\n      default:\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\">\r\n            {status}\r\n          </span>\r\n        );\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterStatus}\r\n                onChange={(e) => setFilterStatus(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"PAID\">Đã thanh toán</option>\r\n                <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n            <ButtonFunctionBarAdmin icon={iconUsers} text={'Tạo học phí theo lớp'} onClick={handleCreateByClass} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {tuitionPayments.length === 0 ? (\r\n        <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n          <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n            <thead className=\"bg-gray-100\">\r\n              <tr>\r\n                <th className=\"py-3 px-4 text-left\">STT</th>\r\n                <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                <th className=\"py-3 px-4 text-left\">Số tiền cần</th>\r\n                <th className=\"py-3 px-4 text-left\">Số tiền đóng</th>\r\n                <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {tuitionPayments.map((payment, index) => (\r\n                <tr\r\n                  key={payment.id}\r\n                  className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                >\r\n                  <td className=\"py-3 px-4\">\r\n                    {(page - 1) * pageSize + index + 1}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {formatCurrency(payment.expectedAmount)}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {formatCurrency(payment.paidAmount)}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.paymentDate\r\n                      ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                      : \"Chưa thanh toán\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <div className=\"flex space-x-2\">\r\n                      <button\r\n                        onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                        className=\"text-blue-500 hover:text-blue-700\"\r\n                        title=\"Xem chi tiết\"\r\n                      >\r\n                        <Eye size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleEdit(payment.id)}\r\n                        className=\"text-yellow-500 hover:text-yellow-700\"\r\n                        title=\"Chỉnh sửa\"\r\n                      >\r\n                        <Edit size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDelete(payment.id)}\r\n                        className=\"text-red-500 hover:text-red-700\"\r\n                        title=\"Xóa\"\r\n                      >\r\n                        <Trash size={16} />\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={page}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={total}\r\n            limit={pageSize}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"addCalculateExpected\"\r\n                          checked={addCalculateExpected}\r\n                          onChange={(e) => setAddCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"addCalculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${addCalculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={addExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddExpectedAmount(value);\r\n                          setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={addCalculateExpected}\r\n                    />\r\n                    {formErrors.expectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.expectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.paidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={addPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddPaidAmount(value);\r\n                          setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.paidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.paidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.status ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addStatus}\r\n                      onChange={(e) => setAddStatus(e.target.value)}\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.status && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.status}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng <span className=\"text-gray-500 text-xs font-normal\">(không bắt buộc)</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền hoặc để trống để tự động tính\"\r\n                      value={batchAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                          setBatchAmount(value ? parseInt(value, 10) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.batchAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchAmount}\r\n                      </p>\r\n                    )}\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      Nếu để trống, hệ thống sẽ tự động tính dựa trên các lớp học sinh đã tham gia\r\n                    </p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"batchByClass\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí cho tất cả học sinh trong một lớp học.</p>\r\n                {/* Form content for batch tuition by class */}\r\n                <form className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp học</label>\r\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\r\n                      <option value=\"\">Chọn lớp học</option>\r\n                      <option value=\"1\">Lớp 10A1</option>\r\n                      <option value=\"2\">Lớp 11A2</option>\r\n                      <option value=\"3\">Lớp 12A3</option>\r\n                    </select>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng</label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền cần đóng</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      placeholder=\"Nhập số tiền\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                  >\r\n                    Tạo học phí theo lớp\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"calculateExpected\"\r\n                          checked={calculateExpected}\r\n                          onChange={(e) => setCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"calculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính lại dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${calculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={editExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={calculateExpected}\r\n                    />\r\n                    {formErrors.editExpectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editExpectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={editPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.editPaidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editPaidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.editStatus ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editStatus}\r\n                      onChange={(e) => setEditStatus(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.editStatus && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editStatus}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,QACvB,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACzH,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAMgD,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+C,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAGpD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,IAAI;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAG1D,WAAW,CACtDqD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACK,UAC3B,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC5D;EACA,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2E,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM+F,eAAe,GAAG9F,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM+F,aAAa,GAAG/F,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMgG,oBAAoB,GAAGhG,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMiG,kBAAkB,GAAGjG,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACyG,YAAY,EAAEC,eAAe,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2G,UAAU,EAAEC,aAAa,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6G,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+G,UAAU,EAAEC,aAAa,CAAC,GAAGhH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiH,YAAY,EAAEC,eAAe,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACmH,SAAS,EAAEC,YAAY,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuH,QAAQ,EAAEC,WAAW,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyH,cAAc,EAAEC,iBAAiB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2H,UAAU,EAAEC,aAAa,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6H,SAAS,EAAEC,YAAY,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC+H,OAAO,EAAEC,UAAU,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACiI,MAAM,EAAEC,SAAS,CAAC,GAAGlI,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACmI,eAAe,EAAEC,kBAAkB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqI,WAAW,EAAEC,cAAc,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuI,UAAU,EAAEC,aAAa,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACyI,QAAQ,EAAEC,WAAW,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM,CAAC2I,WAAW,EAAEC,cAAc,CAAC,GAAG5I,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9I,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC+I,WAAW,EAAEC,cAAc,CAAC,GAAGhJ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMmJ,mBAAmB,GAAGlJ,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA;EACA;EACA;EACA;EACA;;EAEAF,SAAS,CAAC,MAAM;IACdkD,QAAQ,CAAC7B,WAAW,CAAC,EAAE,CAAC,CAAC;IACzB6B,QAAQ,CAAC9B,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC8B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMmG,iBAAiB,GAAIC,SAAS,IAAK;IACvCnE,gBAAgB,CAACmE,SAAS,CAACC,EAAE,CAAC;IAC9BlE,kBAAkB,CAACiE,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCtE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAIC,QAAQ,IAAK;IACrCpE,iBAAiB,CAACoE,QAAQ,CAACJ,EAAE,CAAC;IAC9B9D,iBAAiB,CAACkE,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCvE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzB7G,QAAQ,CACNxC,oBAAoB,CAAC;MACnBsJ,MAAM,EAAEhG,UAAU;MAClBL,IAAI,EAAE,CAAC;MAAE;MACTG,QAAQ;MACRmG,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAEC,YAAY;MACpBC,KAAK,EAAE1F,WAAW;MAClB2F,OAAO,EAAEvF,aAAa;MACtBwF,SAAS,EAAEtF,WAAW;MACtBuF,OAAO,EAAErF;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAMsF,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACvD,SAAS,EAAE;MACduD,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAACpD,QAAQ,EAAE;MACbmD,MAAM,CAACP,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAACS,IAAI,CAACrD,QAAQ,CAAC,EAAE;MAC1CmD,MAAM,CAACP,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAACU,oBAAoB,IAAI,CAACC,iBAAiB,EAAE;MAC/CJ,MAAM,CAACK,cAAc,GAAG,uDAAuD;IACjF,CAAC,MAAM,IAAID,iBAAiB,KAAKE,KAAK,CAACF,iBAAiB,CAAC,IAAIG,MAAM,CAACH,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3FJ,MAAM,CAACK,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAIG,aAAa,KAAKF,KAAK,CAACE,aAAa,CAAC,IAAID,MAAM,CAACC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MACxER,MAAM,CAACS,UAAU,GAAG,0BAA0B;IAChD;;IAEA;IACA,IAAI,CAACxD,UAAU,EAAE;MACf+C,MAAM,CAACU,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAI,CAACC,SAAS,EAAE;MACdX,MAAM,CAACT,MAAM,GAAG,0BAA0B;IAC5C;;IAEA;IACA,IAAIqB,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACc,MAAM,GAAG,CAAC,EAAE;MAClCxE,aAAa,CAAC0D,MAAM,CAAC;MACrB;IACF;IAEAxD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMuE,WAAW,GAAG;QAClBd,MAAM,EAAExD,SAAS;QACjBgD,KAAK,EAAE5C,QAAQ;QACf0C,MAAM,EAAEoB,SAAS;QACjBK,IAAI,EAAE3D,OAAO;QACbqD,OAAO,EAAEzD;MACX,CAAC;;MAED;MACA,IAAI,CAACkD,oBAAoB,IAAIC,iBAAiB,EAAE;QAC9CW,WAAW,CAACV,cAAc,GAAGE,MAAM,CAACH,iBAAiB,CAAC;MACxD;;MAEA;MACA,IAAII,aAAa,EAAE;QACjBO,WAAW,CAACN,UAAU,GAAGF,MAAM,CAACC,aAAa,CAAC;MAChD;;MAEA;MACA,IAAIzD,cAAc,EAAE;QAClBgE,WAAW,CAACE,WAAW,GAAGlE,cAAc;MAC1C;;MAEA;MACA,MAAMxE,QAAQ,CAACtC,oBAAoB,CAAC8K,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjB3I,QAAQ,CAACxC,oBAAoB,CAAC;QAC5BiD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRkG,MAAM,EAAEhG,UAAU;QAClBiG,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA5C,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfqE,oBAAoB,CAAC,EAAE,CAAC;MACxBC,6BAA6B,CAAC,EAAE,CAAC;MACjCC,gBAAgB,CAAC,EAAE,CAAC;MACpBC,yBAAyB,CAAC,EAAE,CAAC;MAC7BtE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBqE,YAAY,CAAC,EAAE,CAAC;MAChBjE,UAAU,CAAC,EAAE,CAAC;MACdkE,uBAAuB,CAAC,KAAK,CAAC;IAEhC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDnF,aAAa,CAAC;QAAEqF,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRnF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDnH,SAAS,CAAC,MAAM;IACdkD,QAAQ,CACNxC,oBAAoB,CAAC;MACnBsJ,MAAM,EAAEhG,UAAU;MAClBL,IAAI,EAAEA,IAAI;MAAE;MACZG,QAAQ;MACRmG,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAEC,YAAY;MACpBC,KAAK,EAAE1F,WAAW;MAClB2F,OAAO,EAAEvF,aAAa;MACtBwF,SAAS,EAAEtF;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAAC9B,QAAQ,EAAES,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAE9B,MAAMyI,UAAU,GAAG,MAAOhD,EAAE,IAAK;IAC/BpC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMqF,QAAQ,GAAG,MAAMtJ,QAAQ,CAAClC,4BAA4B,CAACuI,EAAE,CAAC,CAAC,CAACkD,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAACG,IAAI;;MAE7B;MACAxE,SAAS,CAACoB,EAAE,CAAC;MACbqD,qBAAqB,CAACF,OAAO,CAAC1B,cAAc,IAAI,EAAE,CAAC;MACnD6B,8BAA8B,CAACH,OAAO,CAAC1B,cAAc,GAAG9J,oBAAoB,CAACwL,OAAO,CAAC1B,cAAc,CAAC,GAAG,EAAE,CAAC;MAC1G8B,iBAAiB,CAACJ,OAAO,CAACtB,UAAU,IAAI,EAAE,CAAC;MAC3C2B,0BAA0B,CAACL,OAAO,CAACtB,UAAU,GAAGlK,oBAAoB,CAACwL,OAAO,CAACtB,UAAU,CAAC,GAAG,EAAE,CAAC;MAC9F/C,kBAAkB,CAACqE,OAAO,CAACd,WAAW,GAAG,IAAIoB,IAAI,CAACN,OAAO,CAACd,WAAW,CAAC,CAACqB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxGC,aAAa,CAACT,OAAO,CAACxC,MAAM,IAAI,EAAE,CAAC;MACnCvB,WAAW,CAAC+D,OAAO,CAACf,IAAI,IAAI,EAAE,CAAC;MAC/ByB,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA3I,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAO6H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRjF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMkG,UAAU,GAAG,MAAAA,CAAO9D,EAAE,EAAEqB,MAAM,EAAER,KAAK,KAAK;IAC9CnB,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM/F,QAAQ,CAAClC,4BAA4B,CAACuI,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACA9E,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAO6H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRnD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMqE,SAAS,GAAGA,CAAA,KAAM;IACtB;IACAjG,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfqE,oBAAoB,CAAC,EAAE,CAAC;IACxBC,6BAA6B,CAAC,EAAE,CAAC;IACjCC,gBAAgB,CAAC,EAAE,CAAC;IACpBC,yBAAyB,CAAC,EAAE,CAAC;IAC7BtE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBqE,YAAY,CAAC,EAAE,CAAC;IAChBjE,UAAU,CAAC,EAAE,CAAC;IACdkE,uBAAuB,CAAC,KAAK,CAAC;IAC9BlF,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAMsG,KAAK,GAAG,IAAIP,IAAI,CAAC,CAAC;IACxB,MAAMQ,aAAa,GAAGD,KAAK,CAACN,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvDrF,aAAa,CAAC2F,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;IAChC,MAAMtD,KAAK,GAAGmD,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAC,MAAA,CAAMJ,IAAI,OAAAI,MAAA,CAAIzD,KAAK,GAAG,EAAE,OAAAyD,MAAA,CAAOzD,KAAK,IAAKA,KAAK,CAAE;IACpE3C,WAAW,CAACmG,cAAc,CAAC;;IAE3B;IACAnJ,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMuJ,cAAc,GAAGA,CAAA,KAAM;IAC3BrJ,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwJ,wBAAwB,GAAGA,CAAA,KAAM;IACrCtJ,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyJ,mBAAmB,GAAGA,CAAA,KAAM;IAChCvJ,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMsH,eAAe,GAAGA,CAAA,KAAM;IAC5BtH,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACA4B,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBoB,SAAS,CAAC,IAAI,CAAC;IACfyE,qBAAqB,CAAC,EAAE,CAAC;IACzBC,8BAA8B,CAAC,EAAE,CAAC;IAClCC,iBAAiB,CAAC,EAAE,CAAC;IACrBC,0BAA0B,CAAC,EAAE,CAAC;IAC9B1E,kBAAkB,CAAC,EAAE,CAAC;IACtB8E,aAAa,CAAC,EAAE,CAAC;IACjBxE,WAAW,CAAC,EAAE,CAAC;IACfyE,oBAAoB,CAAC,KAAK,CAAC;IAC3B;IACAvE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1B9B,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtB8B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMgF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMtD,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAIuD,cAAc,KAAKjD,KAAK,CAACiD,cAAc,CAAC,IAAIhD,MAAM,CAACgD,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3EvD,MAAM,CAACuD,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAIC,kBAAkB,KAAKlD,KAAK,CAACkD,kBAAkB,CAAC,IAAIjD,MAAM,CAACiD,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;MACvFxD,MAAM,CAACwD,kBAAkB,GAAG,0BAA0B;IACxD;;IAEA;IACA,IAAI,CAACC,UAAU,EAAE;MACfzD,MAAM,CAACyD,UAAU,GAAG,gCAAgC;IACtD;IAEA,OAAOzD,MAAM;EACf,CAAC;;EAED;EACA,MAAM0D,0BAA0B,GAAG,MAAO5D,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAM4D,gBAAgB,GAAGC,uBAAuB,GAAGpN,kBAAkB,CAACoN,uBAAuB,CAAC,GAAGL,cAAc;IAC/G,MAAMM,oBAAoB,GAAGC,2BAA2B,GAAGtN,kBAAkB,CAACsN,2BAA2B,CAAC,GAAGN,kBAAkB;;IAE/H;IACA,IAAII,uBAAuB,EAAE;MAC3BzB,iBAAiB,CAACwB,gBAAgB,CAAC;IACrC;IAEA,IAAIG,2BAA2B,IAAI,CAACC,iBAAiB,EAAE;MACrD9B,qBAAqB,CAAC4B,oBAAoB,CAAC;IAC7C;;IAEA;IACA,MAAM7D,MAAM,GAAGsD,gBAAgB,CAAC,CAAC;IACjC,IAAI1C,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACc,MAAM,GAAG,CAAC,EAAE;MAClCxE,aAAa,CAAC0D,MAAM,CAAC;MACrB;IACF;IAEAxD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMuE,WAAW,GAAG;QAClBN,UAAU,EAAEkD,gBAAgB,GAAGpD,MAAM,CAACoD,gBAAgB,CAAC,GAAG,CAAC;QAC3DpE,MAAM,EAAEkE,UAAU;QAClBzC,IAAI,EAAEjD,QAAQ;QACdgG,iBAAiB,EAAEA;MACrB,CAAC;;MAED;MACA,IAAIF,oBAAoB,IAAI,CAACE,iBAAiB,EAAE;QAC9ChD,WAAW,CAACV,cAAc,GAAGE,MAAM,CAACsD,oBAAoB,CAAC;MAC3D;;MAEA;MACA,IAAIpG,eAAe,EAAE;QACnBsD,WAAW,CAACE,WAAW,GAAGxD,eAAe;MAC3C;;MAEA;MACA,MAAMlF,QAAQ,CAACnC,oBAAoB,CAAC;QAClCwI,EAAE,EAAErB,MAAM;QACVwD;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjB3I,QAAQ,CAACxC,oBAAoB,CAAC;QAC5BiD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRkG,MAAM,EAAEhG,UAAU;QAClBiG,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAEC,YAAY;QACpBC,KAAK,EAAE1F,WAAW;QAClB2F,OAAO,EAAEvF,aAAa;QACtBwF,SAAS,EAAEtF;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOoH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDnF,aAAa,CAAC;QAAEqF,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRnF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMwH,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMhE,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACvE,UAAU,EAAE;MACfuE,MAAM,CAACvE,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAACyE,IAAI,CAACzE,UAAU,CAAC,EAAE;MAC5CuE,MAAM,CAACvE,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAK2E,KAAK,CAAC3E,WAAW,CAAC,IAAI4E,MAAM,CAAC5E,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEqE,MAAM,CAACrE,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACf+D,MAAM,CAAC/D,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjBiE,MAAM,CAACjE,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAOiE,MAAM;EACf,CAAC;;EAED;EACA,MAAMiE,wBAAwB,GAAG,MAAOnE,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMmE,iBAAiB,GAAGrI,oBAAoB,GAAGrF,kBAAkB,CAACqF,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACsI,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAMlE,MAAM,GAAGgE,wBAAwB,CAAC,CAAC;IACzC,IAAIpD,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACc,MAAM,GAAG,CAAC,EAAE;MAClCxE,aAAa,CAAC0D,MAAM,CAAC;MACrB;IACF;IAEAxD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM2H,SAAS,GAAG;QAChB1E,KAAK,EAAEhE,UAAU;QACjBiF,OAAO,EAAE3E,YAAY;QACrBE,UAAU;QACV+E,IAAI,EAAE7E;MACR,CAAC;;MAED;MACA,IAAI+H,iBAAiB,EAAE;QACrBC,SAAS,CAAC9D,cAAc,GAAGE,MAAM,CAAC2D,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAM3L,QAAQ,CAACrC,0BAA0B,CAACiO,SAAS,CAAC,CAAC;;MAErD;MACAjD,eAAe,CAAC,CAAC;MACjB3I,QAAQ,CAACxC,oBAAoB,CAAC;QAC5BiD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRkG,MAAM,EAAEhG,UAAU;QAClBiG,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DnF,aAAa,CAAC;QAAEqF,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACRnF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM4H,YAAY,GAAIxF,EAAE,IAAK;IAC3BlF,kBAAkB,CAACkF,EAAE,CAAC;IACtBpF,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM6K,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC9L,QAAQ,CAACvC,oBAAoB,CAACyD,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM8K,YAAY,GAAGA,CAAA,KAAM;IACzB9K,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM6K,cAAc,GAAIhF,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,oBACExH,OAAA;UAAMyM,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE7E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,QAAQ;QACX,oBACE9M,OAAA;UAAMyM,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACE9M,OAAA;UAAMyM,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACE9M,OAAA;UAAMyM,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX;QACE,oBACE9M,OAAA;UAAMyM,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACvElF;QAAM;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEb;EACF,CAAC;EAED,IAAIlM,OAAO,EAAE;IACX,oBAAOZ,OAAA,CAACnB,cAAc;MAAA8N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACEhN,OAAA;MACEmN,OAAO,EAAEA,OAAQ;MACjBV,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJO,IAAI,eACLjN,OAAA;QAAA0M,QAAA,EAAOQ;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMM,OAAO,gBACXpN,OAAA;IAAK,wBAAgB;IAACyM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC1M,OAAA,CAACd,IAAI;MAACmO,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMQ,SAAS,gBACbtN,OAAA;IAAK,wBAAgB;IAACyM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC1M,OAAA,CAACZ,QAAQ;MAACiO,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMS,YAAY,gBAChBvN,OAAA;IAAK,wBAAgB;IAACyM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC1M,OAAA,CAACX,QAAQ;MAACgO,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMU,SAAS,gBACbxN,OAAA;IAAK,wBAAgB;IAACyM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC1M,OAAA,CAACV,KAAK;MAAC+N,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,oBACE9M,OAAA,CAACJ,WAAW;IAAA8M,QAAA,gBACV1M,OAAA;MAAKyM,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAEN9M,OAAA;MAAKyM,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpF1M,OAAA;QAAKyM,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB1M,OAAA;UAAKyM,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD1M,OAAA;YAAKyM,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC1M,OAAA;cACEyN,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXpB,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnE1M,OAAA;gBACE8N,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9M,OAAA;cACEkO,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kEAAwC;cACpDC,KAAK,EAAE9M,UAAW;cAClB+M,QAAQ,EAAGtG,CAAC,IAAKxG,aAAa,CAACwG,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAC/C3B,SAAS,EAAC;YAAsI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9M,OAAA;YAAKyM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB1M,OAAA;cACEyM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAEpM,WAAY;cACnBqM,QAAQ,EAAGtG,CAAC,IAAK9F,cAAc,CAAC8F,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAEhD1M,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9ByB,KAAK,CAACC,IAAI,CAAC;gBAAEzF,MAAM,EAAE;cAAG,CAAC,EAAE,CAAC0F,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAMhH,KAAK,GAAGgH,CAAC,GAAG,CAAC;gBACnB,MAAM3D,IAAI,GAAG,IAAIT,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC;gBACrC,MAAM2D,QAAQ,GAAGjH,KAAK,GAAG,EAAE,OAAAyD,MAAA,CAAOzD,KAAK,OAAAyD,MAAA,CAAQzD,KAAK,CAAE;gBACtD,oBACE1H,OAAA;kBAAoBoO,KAAK,KAAAjD,MAAA,CAAKJ,IAAI,OAAAI,MAAA,CAAIwD,QAAQ,CAAG;kBAAAjC,QAAA,cAAAvB,MAAA,CACrCzD,KAAK,OAAAyD,MAAA,CAAIJ,IAAI;gBAAA,GADZrD,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9M,OAAA;YAAKyM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB1M,OAAA;cACEyM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAE3G,YAAa;cACpB4G,QAAQ,EAAGtG,CAAC,IAAK6G,eAAe,CAAC7G,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAEjD1M,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC9M,OAAA;gBAAQoO,KAAK,EAAC,MAAM;gBAAA1B,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C9M,OAAA;gBAAQoO,KAAK,EAAC,QAAQ;gBAAA1B,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C9M,OAAA;gBAAQoO,KAAK,EAAC,SAAS;gBAAA1B,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9M,OAAA;YAAKyM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB1M,OAAA;cACEyM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAEhM,aAAc;cACrBiM,QAAQ,EAAGtG,CAAC,IAAK1F,gBAAgB,CAAC0F,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAElD1M,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC9M,OAAA;gBAAQoO,KAAK,EAAC,MAAM;gBAAA1B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC9M,OAAA;gBAAQoO,KAAK,EAAC,OAAO;gBAAA1B,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9M,OAAA;YAAKyM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB1M,OAAA;cACEyM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAE9L,WAAY;cACnB+L,QAAQ,EAAGtG,CAAC,IAAKxF,cAAc,CAACwF,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAEhD1M,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B9M,OAAA;gBAAQoO,KAAK,EAAC,IAAI;gBAAA1B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC9M,OAAA;gBAAQoO,KAAK,EAAC,IAAI;gBAAA1B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC9M,OAAA;gBAAQoO,KAAK,EAAC,IAAI;gBAAA1B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN9M,OAAA;YAAKyM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB1M,OAAA,CAAClC,gBAAgB;cACfsQ,KAAK,EAAE1L,eAAgB;cACvBmM,eAAe,EAAErM,aAAc;cAC/B6L,QAAQ,EAAE1L,kBAAmB;cAC7BmM,QAAQ,EAAEnI,iBAAkB;cAC5BoI,OAAO,EAAEhI,yBAA0B;cACnCoH,WAAW,EAAC;YAAqB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9M,OAAA;YACEmN,OAAO,EAAE9F,YAAa;YACtBoF,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3F1M,OAAA;cAAKyM,SAAS,EAAC,cAAc;cAACoB,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAf,QAAA,eACpH1M,OAAA;gBAAMgO,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACe,WAAW,EAAC,GAAG;gBAAClB,CAAC,EAAC;cAA6C;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CAAC,eAEN9M,OAAA;UAAKyM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1M,OAAA,CAAC+M,sBAAsB;YAACE,IAAI,EAAEG,OAAQ;YAACF,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAEvC;UAAU;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtF9M,OAAA,CAAC+M,sBAAsB;YAACE,IAAI,EAAEM,YAAa;YAACL,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAE9B;UAAyB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChH9M,OAAA,CAAC+M,sBAAsB;YAACE,IAAI,EAAEO,SAAU;YAACN,IAAI,EAAE,sBAAuB;YAACC,OAAO,EAAE7B;UAAoB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELpM,eAAe,CAACqI,MAAM,KAAK,CAAC,gBAC3B/I,OAAA;MAAKyM,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D1M,OAAA;QAAGyM,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,gBAEN9M,OAAA;MAAKyM,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B1M,OAAA;QAAOyM,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzE1M,OAAA;UAAOyM,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5B1M,OAAA;YAAA0M,QAAA,gBACE1M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD9M,OAAA;cAAIyM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR9M,OAAA;UAAA0M,QAAA,EACGhM,eAAe,CAACuO,GAAG,CAAC,CAACjF,OAAO,EAAEkF,KAAK;YAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClCtP,OAAA;cAEEyM,SAAS,EAAEyC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAAxC,QAAA,gBAEvD1M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAACzL,IAAI,GAAG,CAAC,IAAIG,QAAQ,GAAG8N,KAAK,GAAG;cAAC;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAAyC,aAAA,GAAAnF,OAAO,CAACuF,IAAI,cAAAJ,aAAA,uBAAZA,aAAA,CAAchI,QAAQ,IAAG,GAAG,KAAAiI,cAAA,GAAGpF,OAAO,CAACuF,IAAI,cAAAH,cAAA,uBAAZA,cAAA,CAAclI,SAAS,KAAI;cAAK;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAA2C,cAAA,GAAArF,OAAO,CAACuF,IAAI,cAAAF,cAAA,uBAAZA,cAAA,CAAcG,KAAK,KAAI;cAAK;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAA4C,cAAA,GAAAtF,OAAO,CAACuF,IAAI,cAAAD,cAAA,uBAAZA,cAAA,CAAcG,UAAU,KAAI;cAAK;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClE9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBnO,cAAc,CAACyL,OAAO,CAAC1B,cAAc;cAAC;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBnO,cAAc,CAACyL,OAAO,CAACtB,UAAU;cAAC;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE1C,OAAO,CAAC0F;cAAc;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvD9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB1C,OAAO,CAACd,WAAW,GAChB,IAAIoB,IAAI,CAACN,OAAO,CAACd,WAAW,CAAC,CAACyG,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAIpC,IAAI,CAACN,OAAO,CAACrB,OAAO,CAAC,CAACgH,kBAAkB,CAAC,OAAO;cAAC;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBF,cAAc,CAACxC,OAAO,CAACxC,MAAM,KAAK,MAAM,GAAG,MAAM,GAAGwC,OAAO,CAAC4F,SAAS,GAAG,SAAS,GAAG5F,OAAO,CAACxC,MAAM,KAAK,SAAS,GAAG,SAAS,GAAGwC,OAAO,CAACxC,MAAM;cAAC;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I,CAAC,eACL9M,OAAA;gBAAIyM,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1M,OAAA;kBAAKyM,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B1M,OAAA;oBACEmN,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAACX,OAAO,CAACnD,EAAE,EAAEmD,OAAO,CAAC9B,MAAM,EAAE8B,OAAO,CAACtC,KAAK,CAAE;oBACrE+E,SAAS,EAAC,mCAAmC;oBAC7CoD,KAAK,EAAC,mBAAc;oBAAAnD,QAAA,eAEpB1M,OAAA,CAACb,GAAG;sBAACkO,IAAI,EAAE;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACT9M,OAAA;oBACEmN,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAACG,OAAO,CAACnD,EAAE,CAAE;oBACtC4F,SAAS,EAAC,uCAAuC;oBACjDoD,KAAK,EAAC,qBAAW;oBAAAnD,QAAA,eAEjB1M,OAAA,CAAChB,IAAI;sBAACqO,IAAI,EAAE;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACT9M,OAAA;oBACEmN,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACrC,OAAO,CAACnD,EAAE,CAAE;oBACxC4F,SAAS,EAAC,iCAAiC;oBAC3CoD,KAAK,EAAC,QAAK;oBAAAnD,QAAA,eAEX1M,OAAA,CAACf,KAAK;sBAACoO,IAAI,EAAE;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArDA9C,OAAO,CAACnD,EAAE;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsDb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACA9J,QAAQ,KAAK,OAAO,iBACnBhD,OAAA;MAAKyM,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB1M,OAAA,CAAClB,UAAU;QACTgR,WAAW,EAAE7O,IAAK;QAClB8O,YAAY,EAAG9O,IAAI,IAAKT,QAAQ,CAAC5B,cAAc,CAACqC,IAAI,CAAC,CAAE;QACvD+O,UAAU,EAAE7O,KAAM;QAClB8O,KAAK,EAAE7O;MAAS;QAAAuL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAIAlL,cAAc,iBACb5B,OAAA;MAAKyM,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpF1M,OAAA;QAAKyM,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E1M,OAAA;UAAIyM,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClC5K,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,cAAc,IAAI,sBAAsB,EAC3DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAA6K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACL9M,OAAA;UACEmN,OAAO,EAAEhE,eAAgB;UACzBsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C1M,OAAA,CAACT,CAAC;YAAC8N,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9M,OAAA;QAAA0M,QAAA,GACG5K,cAAc,KAAK,KAAK,iBACvB9B,OAAA;UAAKyM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB1M,OAAA;YAAGyM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvE9M,OAAA;YAAMyM,SAAS,EAAC,WAAW;YAACyD,QAAQ,EAAEpI,sBAAuB;YAAA4E,QAAA,gBAC3D1M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxH9M,OAAA,CAACjC,eAAe;gBACdqQ,KAAK,EAAExJ,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1B2J,QAAQ,EAAExJ,oBAAqB;gBAC/BiK,QAAQ,EAAGS,IAAI,IAAK;kBAClB5K,YAAY,CAAC4K,IAAI,CAAC1I,EAAE,CAAC;kBACrBhC,oBAAoB,IAAAsG,MAAA,CAAIoE,IAAI,CAACpI,QAAQ,OAAAgE,MAAA,CAAIoE,IAAI,CAACrI,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACF6H,OAAO,EAAEA,CAAA,KAAM;kBACbpK,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACFsJ,WAAW,EAAC,mCAAsB;gBAClCgC,IAAI,EAAC;cAAS;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDxI,UAAU,CAAC4D,MAAM,iBAChBlI,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAAC4D,MAAM;cAAA;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH9M,OAAA;gBACEkO,IAAI,EAAC,OAAO;gBACZzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACoD,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3G0G,KAAK,EAAEtJ,QAAS;gBAChBuJ,QAAQ,EAAGtG,CAAC,IAAKhD,WAAW,CAACgD,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACDxI,UAAU,CAACoD,KAAK,iBACf1H,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACoD,KAAK;cAAA;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAA1M,OAAA;kBAAKyM,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC1M,OAAA;oBACEkO,IAAI,EAAC,UAAU;oBACfrH,EAAE,EAAC,sBAAsB;oBACzBuJ,OAAO,EAAEhI,oBAAqB;oBAC9BiG,QAAQ,EAAGtG,CAAC,IAAK0B,uBAAuB,CAAC1B,CAAC,CAACuG,MAAM,CAAC8B,OAAO,CAAE;oBAC3D3D,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACF9M,OAAA;oBAAOqQ,OAAO,EAAC,sBAAsB;oBAAC5D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAExE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACR9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACgE,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,kBAAA6C,MAAA,CAAe/C,oBAAoB,GAAG,aAAa,GAAG,EAAE,CAAG;gBACjK+F,WAAW,EAAC,mDAAuB;gBACnCC,KAAK,EAAEkC,0BAA2B;gBAClCjC,QAAQ,EAAGtG,CAAC,IAAK;kBACf,MAAMqG,KAAK,GAAGrG,CAAC,CAACuG,MAAM,CAACF,KAAK,CAACmC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAAC6F,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjChF,oBAAoB,CAACgF,KAAK,CAAC;oBAC3B/E,6BAA6B,CAAC+E,KAAK,GAAG5P,oBAAoB,CAACgS,QAAQ,CAACpC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACvF;gBACF,CAAE;gBACFqC,QAAQ,EAAErI;cAAqB;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACDxI,UAAU,CAACgE,cAAc,iBACxBtI,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACgE,cAAc;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACoE,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHyF,WAAW,EAAC,qDAAsB;gBAClCC,KAAK,EAAEsC,sBAAuB;gBAC9BrC,QAAQ,EAAGtG,CAAC,IAAK;kBACf,MAAMqG,KAAK,GAAGrG,CAAC,CAACuG,MAAM,CAACF,KAAK,CAACmC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAAC6F,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC9E,gBAAgB,CAAC8E,KAAK,CAAC;oBACvB7E,yBAAyB,CAAC6E,KAAK,GAAG5P,oBAAoB,CAACgS,QAAQ,CAACpC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACnF;gBACF;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDxI,UAAU,CAACoE,UAAU,iBACpB1I,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACoE,UAAU;cAAA;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,EAAC,oDAAoD;gBAC9D2B,KAAK,EAAEpJ,cAAe;gBACtBqJ,QAAQ,EAAGtG,CAAC,IAAK9C,iBAAiB,CAAC8C,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACqE,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7GyF,KAAK,EAAElJ,UAAW;gBAClBmJ,QAAQ,EAAGtG,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDxI,UAAU,CAACqE,OAAO,iBACjB3I,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACqE,OAAO;cAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1H9M,OAAA;gBACEyM,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACkD,MAAM,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC5G4G,KAAK,EAAExF,SAAU;gBACjByF,QAAQ,EAAGtG,CAAC,IAAKyB,YAAY,CAACzB,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBAAA1B,QAAA,gBAE9C1M,OAAA;kBAAQoO,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC9M,OAAA;kBAAQoO,KAAK,EAAC,MAAM;kBAAA1B,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C9M,OAAA;kBAAQoO,KAAK,EAAC,QAAQ;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C9M,OAAA;kBAAQoO,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRxI,UAAU,CAACkD,MAAM,iBAChBxH,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACkD,MAAM;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9M,OAAA;gBACEyM,SAAS,EAAC,oDAAoD;gBAC9DkE,IAAI,EAAC,GAAG;gBACRxC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAE9I,OAAQ;gBACf+I,QAAQ,EAAGtG,CAAC,IAAKxC,UAAU,CAACwC,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLxI,UAAU,CAACsF,MAAM,iBAChB5J,OAAA;cAAKyM,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjG1M,OAAA,CAACR,WAAW;gBAAC6N,IAAI,EAAE,EAAG;gBAACZ,SAAS,EAAC;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D9M,OAAA;gBAAA0M,QAAA,EAAIpI,UAAU,CAACsF;cAAM;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACD9M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,uHAAuH;cACjIgE,QAAQ,EAAEjM,YAAa;cAAAkI,QAAA,EAEtBlI,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACAhL,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKyM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB1M,OAAA;YAAGyM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElF9M,OAAA;YAAMyM,SAAS,EAAC,WAAW;YAACyD,QAAQ,EAAGnI,CAAC,IAAKmE,wBAAwB,CAACnE,CAAC,CAAE;YAAA2E,QAAA,gBACvE1M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH9M,OAAA;gBACEkO,IAAI,EAAC,OAAO;gBACZzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH0K,KAAK,EAAE1K,UAAW;gBAClB2K,QAAQ,EAAGtG,CAAC,IAAKpE,aAAa,CAACoE,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBAC/CwC,QAAQ;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDxI,UAAU,CAACZ,UAAU,iBACpB1D,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACZ,UAAU;cAAA;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,0CAC7C,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACR9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACV,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjHuK,WAAW,EAAC,2GAA4C;gBACxDC,KAAK,EAAEtK,oBAAqB;gBAC5BuK,QAAQ,EAAGtG,CAAC,IAAK;kBACf,MAAMqG,KAAK,GAAGrG,CAAC,CAACuG,MAAM,CAACF,KAAK,CAACmC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAAC6F,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjCrK,uBAAuB,CAACqK,KAAK,GAAG5P,oBAAoB,CAACgS,QAAQ,CAACpC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC/EvK,cAAc,CAACuK,KAAK,GAAGoC,QAAQ,CAACpC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;kBAClD;gBACF;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDxI,UAAU,CAACV,WAAW,iBACrB5D,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACV,WAAW;cAAA;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ,eACD9M,OAAA;gBAAGyM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnH9M,OAAA;gBACEyM,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHkK,KAAK,EAAElK,UAAW;gBAClBmK,QAAQ,EAAGtG,CAAC,IAAK5D,aAAa,CAAC4D,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBAC/CwC,QAAQ;gBAAAlE,QAAA,gBAER1M,OAAA;kBAAQoO,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9M,OAAA;kBAAQoO,KAAK,EAAC,IAAI;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9M,OAAA;kBAAQoO,KAAK,EAAC,IAAI;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9M,OAAA;kBAAQoO,KAAK,EAAC,IAAI;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACRxI,UAAU,CAACJ,UAAU,iBACpBlE,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACJ,UAAU;cAAA;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClHoK,KAAK,EAAEpK,YAAa;gBACpBqK,QAAQ,EAAGtG,CAAC,IAAK9D,eAAe,CAAC8D,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBACjDwC,QAAQ;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDxI,UAAU,CAACN,YAAY,iBACtBhE,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACN,YAAY;cAAA;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9M,OAAA;gBACEyM,SAAS,EAAC,oDAAoD;gBAC9DkE,IAAI,EAAC,GAAG;gBACRxC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEhK,SAAU;gBACjBiK,QAAQ,EAAGtG,CAAC,IAAK1D,YAAY,CAAC0D,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,uHAAuH;cACjIgE,QAAQ,EAAEjM,YAAa;cAAAkI,QAAA,EAEtBlI,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAhL,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKyM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB1M,OAAA;YAAGyM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE1E9M,OAAA;YAAMyM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzB1M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9M,OAAA;gBAAQyM,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBACpE1M,OAAA;kBAAQoO,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC9M,OAAA;kBAAQoO,KAAK,EAAC,GAAG;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC9M,OAAA;kBAAQoO,KAAK,EAAC,GAAG;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC9M,OAAA;kBAAQoO,KAAK,EAAC,GAAG;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7E9M,OAAA;gBACEkO,IAAI,EAAC,OAAO;gBACZzB,SAAS,EAAC;cAAoD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxF9M,OAAA;gBACEkO,IAAI,EAAC,QAAQ;gBACbzB,SAAS,EAAC,oDAAoD;gBAC9D0B,WAAW,EAAC;cAAc;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtF9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,EAAC;cAAoD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9M,OAAA;gBACEyM,SAAS,EAAC,oDAAoD;gBAC9DkE,IAAI,EAAC,GAAG;gBACRxC,WAAW,EAAC;cAAuB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAhL,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAKyM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB1M,OAAA;YAAGyM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/D9M,OAAA;YAAMyM,SAAS,EAAC,WAAW;YAACyD,QAAQ,EAAGnI,CAAC,IAAK4D,0BAA0B,CAAC5D,CAAC,CAAE;YAAA2E,QAAA,gBACzE1M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAA1M,OAAA;kBAAKyM,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC1M,OAAA;oBACEkO,IAAI,EAAC,UAAU;oBACfrH,EAAE,EAAC,mBAAmB;oBACtBuJ,OAAO,EAAEpE,iBAAkB;oBAC3BqC,QAAQ,EAAGtG,CAAC,IAAK2C,oBAAoB,CAAC3C,CAAC,CAACuG,MAAM,CAAC8B,OAAO,CAAE;oBACxD3D,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACF9M,OAAA;oBAAOqQ,OAAO,EAAC,mBAAmB;oBAAC5D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACR9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACmH,kBAAkB,GAAG,gBAAgB,GAAG,iBAAiB,kBAAAN,MAAA,CAAea,iBAAiB,GAAG,aAAa,GAAG,EAAE,CAAG;gBAClKmC,WAAW,EAAC,mDAAuB;gBACnCC,KAAK,EAAErC,2BAA4B;gBACnCsC,QAAQ,EAAGtG,CAAC,IAAK;kBACf,MAAMqG,KAAK,GAAGrG,CAAC,CAACuG,MAAM,CAACF,KAAK,CAACmC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAAC6F,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjCjE,8BAA8B,CAACiE,KAAK,GAAG5P,oBAAoB,CAACgS,QAAQ,CAACpC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACxF;gBACF,CAAE;gBACFqC,QAAQ,EAAEzE;cAAkB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EACDxI,UAAU,CAACmH,kBAAkB,iBAC5BzL,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACmH,kBAAkB;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACkH,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACpH2C,WAAW,EAAC,qDAAsB;gBAClCC,KAAK,EAAEvC,uBAAwB;gBAC/BwC,QAAQ,EAAGtG,CAAC,IAAK;kBACf,MAAMqG,KAAK,GAAGrG,CAAC,CAACuG,MAAM,CAACF,KAAK,CAACmC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAChI,KAAK,CAAC6F,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC/D,0BAA0B,CAAC+D,KAAK,GAAG5P,oBAAoB,CAACgS,QAAQ,CAACpC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACpF;gBACF;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDxI,UAAU,CAACkH,cAAc,iBACxBxL,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACkH,cAAc;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXzB,SAAS,EAAC,oDAAoD;gBAC9D2B,KAAK,EAAE1I,eAAgB;gBACvB2I,QAAQ,EAAGtG,CAAC,IAAKpC,kBAAkB,CAACoC,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAA1M,OAAA;kBAAMyM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1H9M,OAAA;gBACEyM,SAAS,6BAAAtB,MAAA,CAA6B7G,UAAU,CAACoH,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH0C,KAAK,EAAE1C,UAAW;gBAClB2C,QAAQ,EAAGtG,CAAC,IAAK0C,aAAa,CAAC1C,CAAC,CAACuG,MAAM,CAACF,KAAK,CAAE;gBAC/CwC,QAAQ;gBAAAlE,QAAA,gBAER1M,OAAA;kBAAQoO,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC9M,OAAA;kBAAQoO,KAAK,EAAC,MAAM;kBAAA1B,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C9M,OAAA;kBAAQoO,KAAK,EAAC,QAAQ;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C9M,OAAA;kBAAQoO,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRxI,UAAU,CAACoH,UAAU,iBACpB1L,OAAA;gBAAGyM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD1M,OAAA,CAACR,WAAW;kBAAC6N,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACoH,UAAU;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9M,OAAA;cAAA0M,QAAA,gBACE1M,OAAA;gBAAOyM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9M,OAAA;gBACEyM,SAAS,EAAC,oDAAoD;gBAC9DkE,IAAI,EAAC,GAAG;gBACRxC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEpI,QAAS;gBAChBqI,QAAQ,EAAGtG,CAAC,IAAK9B,WAAW,CAAC8B,CAAC,CAACuG,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLxI,UAAU,CAACsF,MAAM,iBAChB5J,OAAA;cAAGyM,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD1M,OAAA,CAACR,WAAW;gBAAC6N,IAAI,EAAE,EAAG;gBAACZ,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACxI,UAAU,CAACsF,MAAM;YAAA;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACD9M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,uHAAuH;cACjIgE,QAAQ,EAAEjM,YAAa;cAAAkI,QAAA,EAEtBlI,YAAY,GAAG,eAAe,GAAG;YAAc;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAhL,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAA0M,QAAA,EACGpG,WAAW,gBACVtG,OAAA;YAAKyM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD1M,OAAA,CAACnB,cAAc;cAAA8N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJjM,cAAc,gBAChBb,OAAA;YAAKyM,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B1M,OAAA;cAAK6Q,GAAG,EAAEnK,mBAAoB;cAAC+F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1E1M,OAAA;gBAAKyM,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnE1M,OAAA;kBAAKyM,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1M,OAAA;oBAAKyM,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9M,OAAA;oBAAA0M,QAAA,gBACE1M,OAAA;sBAAIyM,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClE9M,OAAA;sBAAGyM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpE9M,OAAA;sBAAGyM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAIpC,IAAI,CAAC,CAAC,CAACqF,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGN9M,OAAA;gBAAKyM,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC1M,OAAA;kBAAIyM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClE9M,OAAA;kBAAKyM,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB1M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAA3M,oBAAA,GAACU,cAAc,CAAC0O,IAAI,cAAApP,oBAAA,uBAAnBA,oBAAA,CAAqBgH,QAAQ,EAAC,GAAC,GAAA/G,qBAAA,GAACS,cAAc,CAAC0O,IAAI,cAAAnP,qBAAA,uBAAnBA,qBAAA,CAAqB8G,SAAS;kBAAA;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpH9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAzM,qBAAA,GAAAQ,cAAc,CAAC0O,IAAI,cAAAlP,qBAAA,uBAAnBA,qBAAA,CAAqByQ,KAAK,KAAI,UAAU;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrG9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAxM,qBAAA,GAAAO,cAAc,CAAC0O,IAAI,cAAAjP,qBAAA,uBAAnBA,qBAAA,CAAqBkP,KAAK,KAAI,UAAU;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3F9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAvM,qBAAA,GAAAM,cAAc,CAAC0O,IAAI,cAAAhP,qBAAA,uBAAnBA,qBAAA,CAAqBkP,UAAU,KAAI,UAAU;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9M,OAAA;gBAAA0M,QAAA,gBACE1M,OAAA;kBAAIyM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE9M,OAAA;kBAAKyM,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvE1M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjM,cAAc,CAAC6O,cAAc;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClF9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACvO,cAAc,CAACsC,cAAc,CAACyH,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClH9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACvO,cAAc,CAACsC,cAAc,CAAC6H,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7G9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACvO,cAAc,CAAC,CAACsC,cAAc,CAACyH,cAAc,IAAI,CAAC,KAAKzH,cAAc,CAAC6H,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9I9M,OAAA;oBAAA0M,QAAA,gBACE1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrD9M,OAAA;sBAAMyM,SAAS,oCAAAtB,MAAA,CAAoCtK,cAAc,CAAC2G,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjH3G,cAAc,CAAC2G,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5D3G,cAAc,CAAC2G,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAAkF,QAAA,EACF7L,cAAc,CAAC2G,MAAM,KAAK,MAAM,GAAG,eAAe,GACjD3G,cAAc,CAAC2G,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpD3G,cAAc,CAAC2G,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJ9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjM,cAAc,CAACqI,WAAW,GAAG,IAAIoB,IAAI,CAACzJ,cAAc,CAACqI,WAAW,CAAC,CAACyG,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzK9M,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjM,cAAc,CAAC8H,OAAO,GAAG,IAAI2B,IAAI,CAACzJ,cAAc,CAAC8H,OAAO,CAAC,CAACgH,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJjM,cAAc,CAACoI,IAAI,iBAAIjJ,OAAA;oBAAA0M,QAAA,gBAAG1M,OAAA;sBAAMyM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjM,cAAc,CAACoI,IAAI;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLhM,yBAAyB,IAAIA,yBAAyB,CAACiQ,aAAa,IAAIjQ,yBAAyB,CAACiQ,aAAa,CAAChI,MAAM,GAAG,CAAC,iBACzH/I,OAAA;gBAAA0M,QAAA,gBACE1M,OAAA;kBAAIyM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzE9M,OAAA;kBAAKyM,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvB5L,yBAAyB,CAACiQ,aAAa,CAAC9B,GAAG,CAAEjO,OAAO,iBACnDhB,OAAA;oBAAsByM,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9E1M,OAAA;sBAAKyM,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD1M,OAAA;wBAAIyM,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAE1L,OAAO,CAACyL;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpD9M,OAAA;wBAAMyM,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAAC1L,OAAO,CAACgQ,UAAU;sBAAA;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9M,OAAA;sBAAA0M,QAAA,gBAAG1M,OAAA;wBAAMyM,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACvO,cAAc,CAACyC,OAAO,CAACiQ,MAAM,CAAC;oBAAA;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/F9M,OAAA;sBAAA0M,QAAA,gBAAG1M,OAAA;wBAAMyM,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAIxC,IAAI,CAACtJ,OAAO,CAACkQ,QAAQ,CAAC,CAACvB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrH9L,OAAO,CAACiI,IAAI,iBAAIjJ,OAAA;sBAAA0M,QAAA,gBAAG1M,OAAA;wBAAMyM,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC9L,OAAO,CAACiI,IAAI;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtF9L,OAAO,CAAC6F,EAAE;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN9M,OAAA;kBAAKyM,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C1M,OAAA;oBAAGyM,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAACnO,cAAc,CAACuC,yBAAyB,CAACqQ,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjH9M,OAAA;oBAAGyM,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAACnO,cAAc,CAACsC,cAAc,CAACyH,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGD9M,OAAA;gBAAKyM,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjC1M,OAAA;kBAAKyM,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD1M,OAAA;oBAAA0M,QAAA,gBACE1M,OAAA;sBAAGyM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClE9M,OAAA;sBAAGyM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACN9M,OAAA;oBAAKyM,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B1M,OAAA;sBAAGyM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD9M,OAAA;sBAAKyM,SAAS,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5B9M,OAAA;sBAAGyM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD9M,OAAA;sBAAGyM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGP9M,OAAA;cAAKyM,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvC1M,OAAA;gBACEmN,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAAChJ,cAAc,CAACgG,EAAE,CAAE;gBAC7C4F,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN9M,OAAA;YAAKyM,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9M,OAAA,CAACjB,YAAY;MACXqS,MAAM,EAAE5P,gBAAiB;MACzB6P,SAAS,EAAE/E,aAAc;MACzBY,IAAI,EAAC,qGAAmD;MACxDoE,OAAO,EAAE/E;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAC5M,EAAA,CA/6CID,kBAAkB;EAAA,QACLxC,WAAW,EACXE,WAAW,EACuED,WAAW,EAEhEA,WAAW;AAAA;AAAA6T,EAAA,GALrDtR,kBAAkB;AAi7CxB,eAAeA,kBAAkB;AAAC,IAAAsR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}