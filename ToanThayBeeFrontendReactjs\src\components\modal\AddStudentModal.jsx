import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { register } from '../../features/auth/authSlice';
import { setIsAddView } from '../../features/filter/filterSlice';
import { fetchUsers } from '../../features/user/userSlice';
import Input from '../input/InputForAuthPage';
import Button from '../button/ButtonForAuthPage';
import MultiClassSelector from '../MultiClassSelector';
import { validateRegister } from '../../utils/validation';
import { processRegisterForm } from '../../utils/sanitizeInput';
import { generateUsername } from '../../utils/formatters';
import { X, Info } from 'lucide-react';
import { setErrorMessage } from '../../features/state/stateApiSlice';

const AddStudentModal = () => {
    const dispatch = useDispatch();
    const { loading } = useSelector(state => state.auth);
    const { search, pagination } = useSelector(state => state.users);
    const { page: currentPage, pageSize: limit, sortOrder } = pagination;
    const [formData, setFormData] = useState({
        lastName: '',
        firstName: '',
        gender: 1,
        phone: '',  // This will be used for parent's phone
        highSchool: '',
        class: '10', // Default to class 10
        graduationYear: '', // Năm tốt nghiệp
        userType: 'HS1'
    });

    // Student phone for username/password generation only (not sent to backend)
    const [studentPhone, setStudentPhone] = useState('');

    // Selected classes state
    const [selectedClasses, setSelectedClasses] = useState([]);

    // Auto-generated fields
    const [generatedUsername, setGeneratedUsername] = useState('');
    const [generatedPassword, setGeneratedPassword] = useState('');

    // Update generated username and password when firstName or studentPhone changes
    useEffect(() => {
        if (formData.firstName && studentPhone) {
            // Use student's phone for both username and password
            setGeneratedUsername(generateUsername(formData.firstName, studentPhone));
            setGeneratedPassword(studentPhone);
        }
    }, [formData.firstName, studentPhone]);

    const handleChange = (e) =>
        setFormData({ ...formData, [e.target.name]: e.target.value });

    // Handle student phone change
    const handleStudentPhoneChange = (e) => {
        setStudentPhone(e.target.value);
    };

    // Handle selected classes change
    const handleClassesChange = (classes) => {
        setSelectedClasses(classes);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validate student phone
        if (!studentPhone) {
            dispatch(setErrorMessage("Số điện thoại học sinh không được để trống."));
            return;
        }
        if (!/^(0|\+84)\d{9}$/.test(studentPhone)) {
            dispatch(setErrorMessage("Số điện thoại học sinh không hợp lệ"));
            return;
        }

        // Validate graduation year (required)
        if (!formData.graduationYear || formData.graduationYear === '') {
            dispatch(setErrorMessage("Năm tốt nghiệp không được để trống"));
            return;
        }

        const currentYear = new Date().getFullYear();
        const gradYear = parseInt(formData.graduationYear);

        if (isNaN(gradYear) || gradYear < 2020 || gradYear > 2030) {
            dispatch(setErrorMessage("Năm tốt nghiệp phải từ 2020 đến 2030"));
            return;
        }

        if (gradYear < currentYear) {
            dispatch(setErrorMessage("Năm tốt nghiệp không thể là năm trong quá khứ"));
            return;
        }

        // Extract class IDs from selected classes
        const classIds = selectedClasses.map(cls => cls.id);

        // Create the complete data with auto-generated username and password
        const completeData = {
            ...formData,
            username: generatedUsername,
            password: generatedPassword,
            classIds: classIds,
            // Convert to integer for graduationYear (already validated as required)
            graduationYear: parseInt(formData.graduationYear)
        };

        const processedData = processRegisterForm(completeData);
        const check = validateRegister(processedData, dispatch);
        if (!check) return;

        const resultAction = await dispatch(register(processedData));
        if (resultAction.payload && resultAction.payload.success) {
            // Refresh user list after adding a new student
            dispatch(fetchUsers({ search, currentPage, limit, sortOrder }));
            // Close the modal
            dispatch(setIsAddView(false));
        }
    };

    const handleClose = () => {
        dispatch(setIsAddView(false));
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center p-4 border-b">
                    <h2 className="text-xl font-semibold text-gray-800">Thêm học sinh mới</h2>
                    <button
                        onClick={handleClose}
                        className="text-gray-500 hover:text-gray-700"
                    >
                        <X size={20} />
                    </button>
                </div>

                <form onSubmit={handleSubmit} className="p-4 space-y-4">
                    <div className="flex gap-2">
                        <Input
                            type="text"
                            name="lastName"
                            placeholder="Họ và tên đệm"
                            value={formData.lastName}
                            onChange={handleChange}
                            required
                            className="h-10 text-sm"
                        />
                        <Input
                            type="text"
                            name="firstName"
                            placeholder="Tên"
                            value={formData.firstName}
                            onChange={handleChange}
                            required
                            className="h-10 text-sm"
                        />
                    </div>

                    <Input
                        type="text"
                        name="highSchool"
                        placeholder="Trường học"
                        value={formData.highSchool}
                        onChange={handleChange}
                        required
                        className="h-10 text-sm"
                    />

                    <div className="mb-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Lớp học <span className="text-red-500">*</span>
                        </label>
                        <MultiClassSelector
                            selectedClasses={selectedClasses}
                            onChange={handleClassesChange}
                            className="w-full"
                        />
                    </div>

                    <div className="mb-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Khối lớp <span className="text-red-500">*</span>
                        </label>
                        <div className="flex gap-4">
                            <label className="flex items-center cursor-pointer">
                                <input
                                    type="radio"
                                    name="class"
                                    value="10"
                                    checked={formData.class === '10'}
                                    onChange={handleChange}
                                    className="mr-2 cursor-pointer"
                                />
                                Khối 10
                            </label>
                            <label className="flex items-center cursor-pointer">
                                <input
                                    type="radio"
                                    name="class"
                                    value="11"
                                    checked={formData.class === '11'}
                                    onChange={handleChange}
                                    className="mr-2 cursor-pointer"
                                />
                                Khối 11
                            </label>
                            <label className="flex items-center cursor-pointer">
                                <input
                                    type="radio"
                                    name="class"
                                    value="12"
                                    checked={formData.class === '12'}
                                    onChange={handleChange}
                                    className="mr-2 cursor-pointer"
                                />
                                Khối 12
                            </label>
                        </div>
                    </div>

                    <Input
                        type="tel"
                        name="studentPhone"
                        placeholder="Số điện thoại học sinh"
                        value={studentPhone}
                        onChange={handleStudentPhoneChange}
                        required
                        className="h-10 text-sm"
                    />

                    <Input
                        type="tel"
                        name="phone"
                        placeholder="Số điện thoại phụ huynh"
                        value={formData.phone}
                        onChange={handleChange}
                        required
                        className="h-10 text-sm"
                    />

                    <div className="mb-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Năm tốt nghiệp <span className="text-red-500">*</span>
                        </label>
                        <Input
                            type="number"
                            name="graduationYear"
                            placeholder={`Lớp 12: ${new Date().getFullYear() + 1} Lớp 11: ${new Date().getFullYear() + 2} Lớp 10: ${new Date().getFullYear() + 3}`}
                            value={formData.graduationYear}
                            onChange={handleChange}
                            min="2020"
                            max="2030"
                            required
                            className="h-10 text-sm"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                            Học sinh sẽ tự động bị vô hiệu hóa vào tháng 7 năm tốt nghiệp.
                        </p>
                    </div>

                    {/* Display auto-generated credentials */}
                    {formData.lastName && studentPhone && (
                        <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                            <div className="flex items-center gap-1 mb-2">
                                <Info size={16} className="text-sky-500" />
                                <h3 className="text-sm font-medium text-gray-700">Thông tin đăng nhập tự động:</h3>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div className="text-gray-600">Tên đăng nhập:</div>
                                <div className="font-medium text-sky-600">{generatedUsername}</div>
                                <div className="text-gray-600">Mật khẩu:</div>
                                <div className="font-medium text-sky-600">{generatedPassword}</div>
                            </div>
                        </div>
                    )}

                    <div className="flex justify-end gap-2">
                        <Button
                            onClick={handleClose}
                            className="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 text-sm font-medium rounded-md"
                        >
                            Hủy
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white text-sm font-medium rounded-md"
                        >
                            {loading ? 'Đang thêm...' : 'Thêm học sinh'}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddStudentModal;
