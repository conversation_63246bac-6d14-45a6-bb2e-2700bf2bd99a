import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { getDataForLearning, markLearningItem } from "../../../features/class/classSlice";
import ViewLearning from "../../../components/ViewLearning";
import { useNavigate } from "react-router-dom";
import FullScreen from "../../../components/button/ScreenButton";
import {
    ChevronLeft,
    ChevronRight,
    BookOpen,
    Menu,
    X,
    Home,
    ChevronDown,
    ChevronUp,
    Play,
    FileText,
    PenTool,
    Moon,
    Sun,
    HelpCircle
} from 'lucide-react'
import { motion } from 'framer-motion';
import { fetchCodesByType } from "../../../features/code/codeSlice";
import UserLayout from "../../../layouts/UserLayout";


const LearningPage = () => {
    const { classCode } = useParams();
    const { classDetail } = useSelector((state) => state.classes);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [openLessons, setOpenLessons] = useState([]);
    const { exam } = useSelector((state) => state.exams);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [isDesktopSidebarOpen, setIsDesktopSidebarOpen] = useState(true);
    const { codes } = useSelector((state) => state.codes);

    // Dark mode state
    const [isDarkMode, setIsDarkMode] = useState(() => {
        const saved = localStorage.getItem('learningPageDarkMode');
        return saved ? JSON.parse(saved) : false;
    });

    // Toggle dark mode
    const toggleDarkMode = () => {
        const newMode = !isDarkMode;
        setIsDarkMode(newMode);
        localStorage.setItem('learningPageDarkMode', JSON.stringify(newMode));
    };

    // Show guide state
    const [showGuide, setShowGuide] = useState(false);

    // Toggle guide
    const toggleGuide = () => {
        setShowGuide(!showGuide);
        if (activeItem.type !== 'guide') setActiveItem({ type: 'guide', index: null, item: null });
        else setActiveItem({ type: null, index: null, item: null });
    };

    const sortLessons = (lessons) => {
        if (!Array.isArray(lessons)) return [];

        const lessonsWithChapter = lessons.filter(l => l.chapter);
        const lessonsWithoutChapter = lessons.filter(l => !l.chapter);

        // Nhóm theo chapter
        const groupedByChapter = lessonsWithChapter.reduce((acc, lesson) => {
            if (!acc[lesson.chapter]) acc[lesson.chapter] = [];
            acc[lesson.chapter].push(lesson);
            return acc;
        }, {});

        // Mỗi nhóm sort theo day tăng dần (xa nhất đến gần nhất)
        const sortedChapterGroups = Object.entries(groupedByChapter).map(([chapter, lessonList]) => {
            const sortedLessons = lessonList.sort((a, b) => new Date(a.day) - new Date(b.day));
            return {
                chapter,
                lessons: sortedLessons,
                earliestDay: new Date(sortedLessons[0].day),
            };
        });

        // Sắp xếp nhóm theo earliestDay tăng dần
        sortedChapterGroups.sort((a, b) => a.earliestDay - b.earliestDay);

        const result = [];

        // Sort lessons không chapter theo day tăng dần (xa → gần)
        const sortedNoChapter = lessonsWithoutChapter.sort((a, b) => new Date(a.day) - new Date(b.day));

        let chapterIndex = 0;

        sortedNoChapter.forEach(noChapLesson => {
            const day = new Date(noChapLesson.day);

            // Chèn lesson không chapter trước nhóm có ngày >=
            while (
                chapterIndex < sortedChapterGroups.length &&
                day > sortedChapterGroups[chapterIndex].earliestDay
            ) {
                const group = sortedChapterGroups[chapterIndex];
                result.push(...group.lessons.map(l => ({ type: 'lesson', ...l })));
                chapterIndex++;
            }

            result.push({ type: 'lesson', ...noChapLesson });
        });

        // Thêm nhóm còn lại
        for (; chapterIndex < sortedChapterGroups.length; chapterIndex++) {
            const group = sortedChapterGroups[chapterIndex];
            result.push(...group.lessons.map(l => ({ type: 'lesson', ...l })));
        }

        return result;
    };



    const sortedLessons = classDetail?.lessons ? sortLessons(classDetail.lessons) : [];


    const toggleLesson = (index) => {
        setOpenLessons((prev) =>
            prev.includes(index)
                ? prev.filter((i) => i !== index) // nếu đang mở thì đóng lại
                : [...prev, index]               // nếu đang đóng thì mở ra
        );
    };
    const [activeItem, setActiveItem] = useState({
        type: null,
        index: null,
        item: null,
    });

    const getPrevNextItem = () => {
        if (activeItem?.type !== "learningItem" || !classDetail) return {};

        const allItems = classDetail.lessons.flatMap((lesson) =>
            lesson.learningItems.map((item) => ({
                ...item,
                lessonId: lesson.id,
            }))
        );

        const currentIndex = allItems.findIndex((item) => item.id === activeItem.item.id);

        const prevItem = allItems[currentIndex - 1] || null;
        const nextItem = allItems[currentIndex + 1] || null;

        return { prevItem, nextItem };
    };

    const { prevItem, nextItem } = getPrevNextItem();

    useEffect(() => {
        if (classCode) {
            dispatch(getDataForLearning(classCode));
        }
    }, [dispatch, classCode]);

    useEffect(() => {
        dispatch(fetchCodesByType(['chapter']));
    }, [dispatch]);

    useEffect(() => {
        // console.log("activeItem", activeItem);
    }, [activeItem]);

    useEffect(() => {
        if (
            activeItem?.type === "learningItem" &&
            activeItem.item?.typeOfLearningItem === "BTVN" &&
            exam?.isDone &&
            activeItem.item?.studyStatuses?.[0]?.isDone === false
        ) {
            dispatch(markLearningItem({ learningItemId: activeItem.item.id }));
        }
    }, [activeItem, exam, dispatch]);

    useEffect(() => {
        if (classDetail) {
            const learningItem = classDetail.lessons.flatMap((lesson) =>
                lesson.learningItems.map((item) => ({
                    ...item,
                    lessonId: lesson.id,
                }))
            ).find((item) => item.id === activeItem?.index);
            if (learningItem) {
                setActiveItem((prev) => ({
                    ...prev,
                    item: learningItem,
                }));
            }
        }
    }, [classDetail]);

    useEffect(() => {
        const handleSelectItem = (e) => {
            const item = e.detail;
            setActiveItem({ type: 'learningItem', index: item.id, item });
        };

        window.addEventListener("selectLearningItem", handleSelectItem);
        return () => window.removeEventListener("selectLearningItem", handleSelectItem);
    }, []);



    return (
        <div className={`flex flex-col h-screen overflow-hidden transition-colors duration-300 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
            }`}>
            {/* Header */}
            <div className={`shadow-sm border-b flex-shrink-0 z-30 transition-colors duration-300 ${isDarkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-200'
                }`}>
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center gap-4">
                            {/* Mobile sidebar toggle */}
                            <button
                                className={`lg:hidden p-2 rounded-md transition-colors duration-200 ${isDarkMode
                                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                                    }`}
                                onClick={() => setIsSidebarOpen(prev => !prev)}
                            >
                                {isSidebarOpen ? <X size={20} /> : <Menu size={20} />}
                            </button>


                            <div className="flex items-center gap-3">
                                <BookOpen size={24} className="text-sky-600" />
                                <div>
                                    <h1 className={`text-lg font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                        }`}>
                                        {classDetail?.name || 'Đang tải...'}
                                    </h1>
                                    <p className={`text-sm sm:block hidden transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                        }`}>Học tập trực tuyến</p>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            {/* Guide Button */}

                            {/* Dark Mode Toggle */}
                            <button
                                onClick={toggleDarkMode}
                                className={`p-2 rounded-md transition-colors duration-200 ${isDarkMode
                                    ? 'text-yellow-400 hover:text-yellow-300 hover:bg-gray-700'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                                    }`}
                                title={isDarkMode ? 'Chuyển sang chế độ sáng' : 'Chuyển sang chế độ tối'}
                            >
                                {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
                            </button>

                            <FullScreen isDarkMode={isDarkMode} />

                            <button
                                onClick={toggleGuide}
                                className={`flex items-center gap-2 px-3 py-2 text-sm font-medium border rounded-md transition-colors duration-200 ${activeItem?.type === 'guide'
                                    ? 'bg-blue-600 text-white border-blue-600'
                                    : isDarkMode
                                        ? 'text-gray-300 bg-gray-800 border-gray-600 hover:bg-gray-700'
                                        : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                                    }`}
                                title="Hướng dẫn sử dụng"
                            >
                                <HelpCircle size={16} />
                                <span className="hidden sm:inline">Hướng dẫn</span>
                            </button>

                            <button
                                onClick={() => navigate(`/class/${classDetail?.class_code}`)}
                                className={`flex items-center gap-2 px-3 py-2 text-sm font-medium border rounded-md transition-colors duration-200 ${isDarkMode
                                    ? 'text-gray-300 bg-gray-800 border-gray-600 hover:bg-gray-700'
                                    : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                                    }`}
                            >
                                <Home size={16} />
                                <span className="hidden sm:inline">Về trang lớp</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="flex flex-1 overflow-hidden">
                {/* Sidebar */}



                <motion.div
                    animate={{
                        width: isDesktopSidebarOpen ? 320 : 70, // pixel tương ứng w-80 và w-20
                    }}
                    transition={{ duration: 0.3 }}
                    className={`
    h-full flex flex-col fixed z-40 border-r
    shadow-lg lg:shadow-none transition-all duration-300
    ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} // mobile only
    lg:translate-x-0 lg:relative
    ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
  `}
                >

                    {/* Sidebar Header */}
                    <div className={`flex-shrink-0 border-b p-4 transition-colors duration-300 ${isDarkMode
                        ? 'bg-gray-800 border-gray-700'
                        : 'bg-white border-gray-200'
                        }`}>
                        <div className="flex items-center justify-between">
                            <div className={`${!isDesktopSidebarOpen ? 'hidden' : 'block'}`}>
                                <h2 className={`text-lg font-semibold transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>Nội dung học tập</h2>
                                <p className={`text-sm mt-1 transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                    }`}>{classDetail?.name}</p>
                            </div>
                            <button
                                className={`lg:hidden p-2 rounded-md transition-colors duration-200 ${isDarkMode
                                    ? 'text-gray-400 hover:text-gray-200'
                                    : 'text-gray-400 hover:text-gray-600'
                                    }`}
                                onClick={() => setIsSidebarOpen(false)}
                            >
                                <X size={20} />
                            </button>
                            {/* Desktop sidebar toggle */}
                            <button
                                className={`hidden lg:block p-2 rounded-md transition-colors duration-200 ${isDarkMode
                                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                                    }`}
                                onClick={() => setIsDesktopSidebarOpen(prev => !prev)}
                                title={isDesktopSidebarOpen ? 'Ẩn sidebar' : 'Hiện sidebar'}
                            >
                                {isDesktopSidebarOpen ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
                            </button>
                        </div>
                    </div>

                    {/* Sidebar Content */}
                    <div className={`flex-1 overflow-y-auto p-4 space-y-3 lg:pb-0 pb-20 ${!isDesktopSidebarOpen ? 'hidden' : 'block'}`}>
                        {sortedLessons.map((lesson, index) => {
                            const prevLesson = sortedLessons[index - 1];
                            const nextLesson = sortedLessons[index + 1];
                            const isFirstOfGroup = lesson.chapter && lesson.chapter !== prevLesson?.chapter;
                            const isLastOfGroup = lesson.chapter && lesson.chapter !== nextLesson?.chapter;
                            const chapterDescription = lesson.chapter
                                ? codes['chapter']?.find((c) => c.code === lesson.chapter)?.description || lesson.chapter
                                : null;

                            return (
                                <div key={lesson.id} className="space-y-2">
                                    {/* Chapter Header */}
                                    {isFirstOfGroup && (
                                        <div className={`px-3 py-2 rounded-lg border-l-4 border-sky-500 transition-colors duration-200 ${isDarkMode ? 'bg-sky-900/30' : 'bg-sky-50'
                                            }`}>
                                            <h3 className={`text-sm font-semibold transition-colors duration-200 ${isDarkMode ? 'text-sky-300' : 'text-sky-700'
                                                }`}>
                                                {chapterDescription}
                                            </h3>
                                        </div>
                                    )}

                                    {/* Lesson Card */}
                                    <div className={`border rounded-lg shadow-sm hover:shadow-md transition-all duration-200 ${isDarkMode
                                        ? 'bg-gray-700 border-gray-600 hover:bg-gray-650'
                                        : 'bg-white border-gray-200'
                                        }`}>
                                        {/* Lesson Header */}
                                        <div
                                            onClick={() => {
                                                toggleLesson(index);
                                                setActiveItem({ type: 'lesson', index: lesson.id, item: lesson });
                                            }}
                                            className={`cursor-pointer p-4 flex items-center justify-between rounded-lg transition-colors duration-200 ${activeItem.type === 'lesson' && activeItem.index === lesson.id
                                                ? 'bg-sky-600 text-white'
                                                : isDarkMode
                                                    ? 'hover:bg-gray-600'
                                                    : 'hover:bg-gray-50'
                                                }`}
                                        >
                                            <div className="flex items-center gap-3 flex-1 min-w-0">
                                                <div className="flex-shrink-0">
                                                    {lesson.learningItems?.length > 0 ? (
                                                        openLessons.includes(index) ? (
                                                            <ChevronUp size={20} className={activeItem.type === 'lesson' && activeItem.index === lesson.id ? 'text-white' : 'text-gray-600'} />
                                                        ) : (
                                                            <ChevronDown size={20} className={activeItem.type === 'lesson' && activeItem.index === lesson.id ? 'text-white' : 'text-gray-600'} />
                                                        )
                                                    ) : (
                                                        <div className="w-5 h-5" />
                                                    )}
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <h4 className={`font-medium truncate transition-colors duration-200 ${activeItem.type === 'lesson' && activeItem.index === lesson.id
                                                        ? 'text-white'
                                                        : isDarkMode ? 'text-white' : 'text-gray-900'
                                                        }`}>{lesson.name}</h4>
                                                    <p className={`text-xs transition-colors duration-200 ${activeItem.type === 'lesson' && activeItem.index === lesson.id
                                                        ? 'text-sky-100'
                                                        : isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                                        }`}>
                                                        {new Date(lesson.day).toLocaleDateString('vi-VN')}
                                                    </p>
                                                </div>
                                            </div>

                                            {lesson.learningItems?.length > 0 && (
                                                <span className={`text-xs px-2 py-1 rounded-full transition-colors duration-200 ${activeItem.type === 'lesson' && activeItem.index === lesson.id
                                                    ? 'bg-sky-500 text-white'
                                                    : isDarkMode
                                                        ? 'bg-gray-600 text-gray-300'
                                                        : 'bg-gray-100 text-gray-600'
                                                    }`}>
                                                    {lesson.learningItems.length} mục
                                                </span>
                                            )}
                                        </div>

                                        {/* Learning Items */}
                                        <div
                                            className={`transition-all duration-300 ease-in-out overflow-hidden ${openLessons.includes(index) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                                                }`}
                                        >
                                            <div className={`border-t transition-colors duration-200 ${isDarkMode
                                                ? 'border-gray-600 bg-gray-750'
                                                : 'border-gray-200 bg-gray-50'
                                                }`}>
                                                {lesson.learningItems?.map((learningItem, i) => {
                                                    const isDone = learningItem.studyStatuses?.[0]?.isDone;
                                                    const getItemIcon = (type) => {
                                                        switch (type) {
                                                            case 'VID': return <Play size={16} className="text-red-500" />;
                                                            case 'DOC': return <FileText size={16} className="text-blue-500" />;
                                                            case 'BTVN': return <PenTool size={16} className="text-green-500" />;
                                                            default: return <FileText size={16} className="text-gray-500" />;
                                                        }
                                                    };

                                                    return (
                                                        <div
                                                            key={i}
                                                            onClick={() =>
                                                                setActiveItem({ type: 'learningItem', index: learningItem.id, item: learningItem })
                                                            }
                                                            className={`p-3 border-b last:border-b-0 cursor-pointer transition-colors duration-200 ${activeItem.type === 'learningItem' && activeItem.index === learningItem.id
                                                                ? 'bg-sky-100 border-sky-200'
                                                                : isDarkMode
                                                                    ? 'border-gray-600 hover:bg-gray-700'
                                                                    : 'border-gray-200 hover:bg-white'
                                                                }`}
                                                        >
                                                            <div className="flex items-center gap-3">
                                                                <div className="flex items-center gap-2">
                                                                    <div
                                                                        className={`w-2 h-2 rounded-full flex-shrink-0 ${isDone ? 'bg-green-500' : 'bg-yellow-400'
                                                                            }`}
                                                                    />
                                                                    {getItemIcon(learningItem.typeOfLearningItem)}
                                                                </div>
                                                                <div className="flex-1 min-w-0">
                                                                    <p className={`text-sm font-medium truncate transition-colors duration-200 ${activeItem.type === 'learningItem' && activeItem.index === learningItem.id
                                                                        ? 'text-gray-900'
                                                                        : isDarkMode ? 'text-white' : 'text-gray-900'
                                                                        }`}>
                                                                        {learningItem.name}
                                                                    </p>
                                                                    <p className={`text-xs transition-colors duration-200 ${activeItem.type === 'learningItem' && activeItem.index === learningItem.id
                                                                        ? 'text-gray-500'
                                                                        : isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                                                        }`}>
                                                                        {learningItem.typeOfLearningItem === 'VID' && 'Video bài giảng'}
                                                                        {learningItem.typeOfLearningItem === 'DOC' && 'Tài liệu học tập'}
                                                                        {learningItem.typeOfLearningItem === 'BTVN' && 'Bài tập về nhà'}
                                                                    </p>
                                                                </div>
                                                                {isDone && (
                                                                    <div className="flex-shrink-0">
                                                                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                                                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                                            </svg>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Chapter Divider */}
                                    {isLastOfGroup && <div className="h-4" />}
                                </div>
                            );
                        })}
                    </div>
                </motion.div>

                {/* Main Content Area */}
                <div className="flex-1 h-full overflow-y-auto pb-20">
                    <div className={`p-4 lg:p-6`}>
                        {sortedLessons.length > 0 ? (
                            <ViewLearning activeItem={activeItem} classDetail={classDetail} isDarkMode={isDarkMode} />
                        ) : (
                            <div className="flex flex-col items-center justify-center h-96 text-center">
                                <BookOpen size={48} className={`mb-4 transition-colors duration-200 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'
                                    }`} />
                                <h3 className={`text-lg font-medium mb-2 transition-colors duration-200 ${isDarkMode ? 'text-white' : 'text-gray-900'
                                    }`}>Chưa có nội dung học tập</h3>
                                <p className={`transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                    }`}>Hiện tại chưa có buổi học nào được thêm vào lớp này.</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Bottom Navigation for Learning Items */}
            {
                activeItem?.type === "learningItem" && (
                    <div className={`fixed bottom-0 left-0 right-0 border-t shadow-lg z-20 transition-colors duration-300 ${isDarkMode
                        ? 'bg-gray-800 border-gray-700'
                        : 'bg-white border-gray-200'
                        }`}>
                        <div className={`px-2 ${isDesktopSidebarOpen ? 'md:pl-[22rem]' : 'md:pl-[6rem]'} md:pr-[2rem] mx-auto`}>
                            <div className="flex items-center justify-between py-3">
                                {/* Left: Status and Mark Button */}
                                <div className="flex items-center gap-4">
                                    {activeItem?.item.typeOfLearningItem !== "BTVN" && (
                                        <button
                                            onClick={() => {
                                                dispatch(markLearningItem({
                                                    learningItemId: activeItem?.item?.id,
                                                }));
                                            }}
                                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${activeItem?.item?.studyStatuses?.[0]?.isDone
                                                ? "bg-red-50 text-red-600 hover:bg-red-100 border border-red-200"
                                                : "bg-green-600 text-white hover:bg-green-700"
                                                }`}
                                        >
                                            {activeItem?.item?.studyStatuses?.[0]?.isDone
                                                ? "Bỏ đánh dấu"
                                                : "Đánh dấu đã học"}
                                        </button>
                                    )}

                                    <div className="flex md:flex-row flex-col md:items-center items-start gap-3">
                                        <div className="flex items-center gap-2">
                                            <div
                                                className={`w-3 h-3 rounded-full ${activeItem?.item?.studyStatuses?.[0]?.isDone ? 'bg-green-500' : 'bg-yellow-400'
                                                    }`}
                                            />
                                            <span
                                                className={`text-sm font-medium ${activeItem?.item?.studyStatuses?.[0]?.isDone ? 'text-green-600' : 'text-yellow-600'
                                                    }`}
                                            >
                                                {activeItem?.item?.studyStatuses?.[0]?.isDone ? 'Đã học' : 'Chưa học'}
                                            </span>
                                        </div>

                                        {activeItem?.item?.studyStatuses?.[0]?.isDone && (
                                            <span className={`text-xs transition-colors duration-200 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                                                }`}>
                                                {new Date(activeItem?.item?.studyStatuses?.[0]?.studyTime).toLocaleDateString('vi-VN', {
                                                    day: '2-digit',
                                                    month: '2-digit',
                                                    year: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                })}
                                            </span>
                                        )}
                                    </div>
                                </div>

                                {/* Right: Navigation Buttons */}
                                <div className="flex items-center gap-2">
                                    <button
                                        disabled={!prevItem}
                                        onClick={() =>
                                            prevItem &&
                                            window.dispatchEvent(new CustomEvent("selectLearningItem", { detail: prevItem }))
                                        }
                                        className={`px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium transition-colors ${prevItem
                                            ? "bg-sky-600 hover:bg-sky-700 text-white"
                                            : isDarkMode
                                                ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                                                : "bg-gray-100 text-gray-400 cursor-not-allowed"
                                            }`}
                                    >
                                        <ChevronLeft size={16} />
                                        <span className="hidden sm:inline">Trước</span>
                                    </button>

                                    <button
                                        disabled={!nextItem}
                                        onClick={() =>
                                            nextItem &&
                                            window.dispatchEvent(new CustomEvent("selectLearningItem", { detail: nextItem }))
                                        }
                                        className={`px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium transition-colors ${nextItem
                                            ? "bg-sky-600 hover:bg-sky-700 text-white"
                                            : isDarkMode
                                                ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                                                : "bg-gray-100 text-gray-400 cursor-not-allowed"
                                            }`}
                                    >
                                        <span className="hidden sm:inline">Tiếp</span>
                                        <ChevronRight size={16} />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )
            }

            {/* Overlay for mobile sidebar */}
            {
                isSidebarOpen && (
                    <div
                        className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
                        onClick={() => setIsSidebarOpen(false)}
                    />
                )
            }
        </div >

    );
}

export default LearningPage;