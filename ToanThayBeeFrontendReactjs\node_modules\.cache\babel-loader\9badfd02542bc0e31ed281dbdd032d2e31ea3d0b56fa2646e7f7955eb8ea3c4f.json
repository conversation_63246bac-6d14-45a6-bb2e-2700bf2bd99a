{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    page,\n    totalPages,\n    total,\n    pageSize\n  } = useSelector(state => state.tuition.pagination);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho bộ lọc\n  const [filterMonth, setFilterMonth] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"\");\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\n  const [filterClass, setFilterClass] = useState(\"\");\n  const [filterClassId, setFilterClassId] = useState(\"\");\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addExpectedAmount, setAddExpectedAmount] = useState(\"\");\n  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState(\"\");\n  const [addPaidAmount, setAddPaidAmount] = useState(\"\");\n  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addStatus, setAddStatus] = useState(\"\");\n  const [addNote, setAddNote] = useState(\"\");\n  const [addCalculateExpected, setAddCalculateExpected] = useState(false);\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editExpectedAmount, setEditExpectedAmount] = useState(\"\");\n  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState(\"\");\n  const [editPaidAmount, setEditPaidAmount] = useState(\"\");\n  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState(\"\");\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editStatus, setEditStatus] = useState(\"\");\n  const [editNote, setEditNote] = useState(\"\");\n  const [calculateExpected, setCalculateExpected] = useState(false);\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n\n  // useEffect(() => {\n  //   if (!didInit) {\n  //     dispatch(resetFilters());\n  //     setDidInit(true);\n  //   }\n  // }, [dispatch, didInit]);\n\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate expected amount (required if not calculating automatically)\n    if (!addCalculateExpected && !addExpectedAmount) {\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\n      errors.expectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate paid amount (must be a positive number if provided)\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\n      errors.paidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // Validate status (required)\n    if (!addStatus) {\n      errors.status = \"Vui lòng chọn trạng thái\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        status: addStatus,\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Only include expectedAmount if not calculating automatically\n      if (!addCalculateExpected && addExpectedAmount) {\n        paymentData.expectedAmount = Number(addExpectedAmount);\n      }\n\n      // Include paidAmount if provided\n      if (addPaidAmount) {\n        paymentData.paidAmount = Number(addPaidAmount);\n      }\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddExpectedAmount(\"\");\n      setAddExpectedAmountFormatted(\"\");\n      setAddPaidAmount(\"\");\n      setAddPaidAmountFormatted(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddStatus(\"\");\n      setAddNote(\"\");\n      setAddCalculateExpected(false);\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  useEffect(() => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: page,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, page, pageSize]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditExpectedAmount(payment.expectedAmount || \"\");\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\n      setEditPaidAmount(payment.paidAmount || \"\");\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditStatus(payment.status || \"\");\n      setEditNote(payment.note || \"\");\n      setCalculateExpected(false);\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddExpectedAmount(\"\");\n    setAddExpectedAmountFormatted(\"\");\n    setAddPaidAmount(\"\");\n    setAddPaidAmountFormatted(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddStatus(\"\");\n    setAddNote(\"\");\n    setAddCalculateExpected(false);\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const handleCreateByClass = () => {\n    setRightPanelType(\"batchByClass\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditExpectedAmount(\"\");\n    setEditExpectedAmountFormatted(\"\");\n    setEditPaidAmount(\"\");\n    setEditPaidAmountFormatted(\"\");\n    setEditPaymentDate(\"\");\n    setEditStatus(\"\");\n    setEditNote(\"\");\n    setCalculateExpected(false);\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate paid amount (must be a positive number)\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate expected amount (must be a positive number if provided)\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate status (required)\n    if (!editStatus) {\n      errors.editStatus = \"Trạng thái không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\n\n    // Update the actual values with parsed values\n    if (editPaidAmountFormatted) {\n      setEditPaidAmount(parsedPaidAmount);\n    }\n    if (editExpectedAmountFormatted && !calculateExpected) {\n      setEditExpectedAmount(parsedExpectedAmount);\n    }\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\n        status: editStatus,\n        note: editNote,\n        calculateExpected: calculateExpected\n      };\n\n      // Only include expectedAmount if it's provided and not calculating automatically\n      if (parsedExpectedAmount && !calculateExpected) {\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\n      }\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        status: filterStatus,\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n  const getStatusBadge = status => {\n    switch (status) {\n      case \"PAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n          children: \"\\u0110\\xE3 thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this);\n      case \"UNPAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\",\n          children: \"Ch\\u01B0a thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this);\n      case \"OVERDUE\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\",\n          children: \"Qu\\xE1 h\\u1EA1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this);\n      case \"PARTIAL\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\",\n          children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\",\n          children: status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 657,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 663,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 669,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 675,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterStatus,\n              onChange: e => setFilterStatus(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PAID\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"UNPAID\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PARTIAL\",\n                children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconUsers,\n            text: 'Tạo học phí theo lớp',\n            onClick: handleCreateByClass\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 7\n    }, this), tuitionPayments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 807,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xF3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user, _payment$user2, _payment$user3, _payment$user4;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (page - 1) * pageSize + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.lastName) + \" \" + ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.expectedAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.paidAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleView(payment.id, payment.userId, payment.month),\n                    className: \"text-blue-500 hover:text-blue-700\",\n                    title: \"Xem chi ti\\u1EBFt\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(payment.id),\n                    className: \"text-yellow-500 hover:text-yellow-700\",\n                    title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(payment.id),\n                    className: \"text-red-500 hover:text-red-700\",\n                    title: \"X\\xF3a\",\n                    children: /*#__PURE__*/_jsxDEV(Trash, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 876,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 19\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 811,\n      columnNumber: 9\n    }, this), viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: page,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: total,\n        limit: pageSize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 915,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"addCalculateExpected\",\n                    checked: addCalculateExpected,\n                    onChange: e => setAddCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"addCalculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(addCalculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: addExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddExpectedAmount(value);\n                    setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: addCalculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 21\n              }, this), formErrors.expectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 25\n                }, this), \" \", formErrors.expectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 997,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.paidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: addPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddPaidAmount(value);\n                    setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 21\n              }, this), formErrors.paidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 25\n                }, this), \" \", formErrors.paidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1025,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.status ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addStatus,\n                onChange: e => setAddStatus(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1053,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 21\n              }, this), formErrors.status && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 25\n                }, this), \" \", formErrors.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1092,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1105,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1095,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500 text-xs font-normal\",\n                  children: \"(kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n ho\\u1EB7c \\u0111\\u1EC3 tr\\u1ED1ng \\u0111\\u1EC3 t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh\",\n                value: batchAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                    setBatchAmount(value ? parseInt(value, 10) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1114,\n                columnNumber: 21\n              }, this), formErrors.batchAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1129,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"N\\u1EBFu \\u0111\\u1EC3 tr\\u1ED1ng, h\\u1EC7 th\\u1ED1ng s\\u1EBD t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1132,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1137,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1151,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1150,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1156,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1172,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1170,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1180,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1091,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByClass\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t l\\u1EDBp h\\u1ECDc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1193,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"L\\u1EDBp h\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp h\\u1ECDc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1\",\n                  children: \"L\\u1EDBp 10A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1200,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2\",\n                  children: \"L\\u1EDBp 11A2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3\",\n                  children: \"L\\u1EDBp 12A3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1206,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1207,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1213,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"H\\u1EA1n thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1220,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1228,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1229,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n              children: \"T\\u1EA1o h\\u1ECDc ph\\xED theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1235,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1195,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1192,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1247,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"calculateExpected\",\n                    checked: calculateExpected,\n                    onChange: e => setCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1254,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"calculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh l\\u1EA1i d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1261,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1253,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1251,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(calculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: editExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: calculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1266,\n                columnNumber: 21\n              }, this), formErrors.editExpectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1281,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editExpectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1280,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1250,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1286,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: editPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 21\n              }, this), formErrors.editPaidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editPaidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1300,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1285,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1307,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1305,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1315,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1315,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editStatus ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editStatus,\n                onChange: e => setEditStatus(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1322,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1323,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1325,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1316,\n                columnNumber: 21\n              }, this), formErrors.editStatus && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1329,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editStatus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1314,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1333,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1344,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1348,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1246,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1363,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1362,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1375,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1376,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1377,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1374,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1370,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1369,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1385,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1387,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1387,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1388,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1388,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1389,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1386,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1384,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1396,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1398,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1398,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1399,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1399,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1400,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1400,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1401,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1401,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1403,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1404,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1402,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1415,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1415,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1416,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1416,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1417,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1417,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1395,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1424,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1429,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1430,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1428,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1434,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1434,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1435,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1435,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1436,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1436,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1427,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1425,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1441,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1442,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1440,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1423,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1452,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1455,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1456,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1457,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1458,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1454,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1449,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1448,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1367,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1466,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1465,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1366,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1475,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1360,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1485,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 681,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"pWQl0sfhqjikCYB0wcFEaSp1dDM=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "AdminLayout", "FunctionBarAdmin", "Chart", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "page", "totalPages", "total", "pageSize", "pagination", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "filterMonth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterStatus", "setFilterStatus", "filterOverdue", "setFilterOverdue", "filterClass", "setFilterClass", "filterClassId", "setFilterClassId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addExpectedAmount", "setAddExpectedAmount", "addExpectedAmountFormatted", "setAddExpectedAmountFormatted", "addPaidAmount", "setAddPaidAmount", "addPaidAmountFormatted", "setAddPaidAmountFormatted", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addStatus", "setAddStatus", "addNote", "setAddNote", "addCalculateExpected", "setAddCalculateExpected", "editId", "setEditId", "editExpectedAmount", "setEditExpectedAmount", "editExpectedAmountFormatted", "setEditExpectedAmountFormatted", "editPaidAmount", "setEditPaidAmount", "editPaidAmountFormatted", "setEditPaidAmountFormatted", "editPaymentDate", "setEditPaymentDate", "editStatus", "setEditStatus", "editNote", "setEditNote", "calculateExpected", "setCalculateExpected", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "sortOrder", "status", "month", "overdue", "userClass", "classId", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "expectedAmount", "isNaN", "Number", "paidAmount", "dueDate", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "error", "console", "submit", "handleEdit", "response", "unwrap", "payment", "data", "Date", "toISOString", "split", "handleView", "handleAdd", "today", "formattedDate", "year", "getFullYear", "getMonth", "formattedMonth", "concat", "handleBatchAdd", "handleCreateBatchTuition", "handleCreateByClass", "validateEditForm", "handleUpdateTuitionPayment", "parsedPaidAmount", "parsedExpectedAmount", "validateBatchTuitionForm", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "handleDelete", "confirmDelete", "cancelDelete", "getStatusBadge", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "type", "placeholder", "value", "onChange", "target", "Array", "from", "_", "i", "monthStr", "selectedClassId", "onSelect", "onClear", "strokeWidth", "map", "index", "_payment$user", "_payment$user2", "_payment$user3", "_payment$user4", "user", "class", "highSchool", "monthFormatted", "toLocaleDateString", "isOverdue", "title", "currentPage", "onPageChange", "totalItems", "limit", "onSubmit", "role", "checked", "htmlFor", "replace", "parseInt", "disabled", "rows", "required", "ref", "phone", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { page, totalPages, total, pageSize } = useSelector(\r\n    (state) => state.tuition.pagination\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho bộ lọc\r\n  const [filterMonth, setFilterMonth] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"\");\r\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  const [filterClass, setFilterClass] = useState(\"\");\r\n  const [filterClassId, setFilterClassId] = useState(\"\");\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addExpectedAmount, setAddExpectedAmount] = useState(\"\");\r\n  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState(\"\");\r\n  const [addPaidAmount, setAddPaidAmount] = useState(\"\");\r\n  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addStatus, setAddStatus] = useState(\"\");\r\n  const [addNote, setAddNote] = useState(\"\");\r\n  const [addCalculateExpected, setAddCalculateExpected] = useState(false);\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editExpectedAmount, setEditExpectedAmount] = useState(\"\");\r\n  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState(\"\");\r\n  const [editPaidAmount, setEditPaidAmount] = useState(\"\");\r\n  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState(\"\");\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editStatus, setEditStatus] = useState(\"\");\r\n  const [editNote, setEditNote] = useState(\"\");\r\n  const [calculateExpected, setCalculateExpected] = useState(false);\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  // useEffect(() => {\r\n  //   if (!didInit) {\r\n  //     dispatch(resetFilters());\r\n  //     setDidInit(true);\r\n  //   }\r\n  // }, [dispatch, didInit]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate expected amount (required if not calculating automatically)\r\n    if (!addCalculateExpected && !addExpectedAmount) {\r\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\r\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\r\n      errors.expectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate paid amount (must be a positive number if provided)\r\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\r\n      errors.paidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!addStatus) {\r\n      errors.status = \"Vui lòng chọn trạng thái\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        status: addStatus,\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Only include expectedAmount if not calculating automatically\r\n      if (!addCalculateExpected && addExpectedAmount) {\r\n        paymentData.expectedAmount = Number(addExpectedAmount);\r\n      }\r\n\r\n      // Include paidAmount if provided\r\n      if (addPaidAmount) {\r\n        paymentData.paidAmount = Number(addPaidAmount);\r\n      }\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddExpectedAmount(\"\");\r\n      setAddExpectedAmountFormatted(\"\");\r\n      setAddPaidAmount(\"\");\r\n      setAddPaidAmountFormatted(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddStatus(\"\");\r\n      setAddNote(\"\");\r\n      setAddCalculateExpected(false);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: page, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, page, pageSize]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditExpectedAmount(payment.expectedAmount || \"\");\r\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\r\n      setEditPaidAmount(payment.paidAmount || \"\");\r\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditStatus(payment.status || \"\");\r\n      setEditNote(payment.note || \"\");\r\n      setCalculateExpected(false);\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddExpectedAmount(\"\");\r\n    setAddExpectedAmountFormatted(\"\");\r\n    setAddPaidAmount(\"\");\r\n    setAddPaidAmountFormatted(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddStatus(\"\");\r\n    setAddNote(\"\");\r\n    setAddCalculateExpected(false);\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateByClass = () => {\r\n    setRightPanelType(\"batchByClass\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditExpectedAmount(\"\");\r\n    setEditExpectedAmountFormatted(\"\");\r\n    setEditPaidAmount(\"\");\r\n    setEditPaidAmountFormatted(\"\");\r\n    setEditPaymentDate(\"\");\r\n    setEditStatus(\"\");\r\n    setEditNote(\"\");\r\n    setCalculateExpected(false);\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate paid amount (must be a positive number)\r\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\r\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate expected amount (must be a positive number if provided)\r\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\r\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!editStatus) {\r\n      errors.editStatus = \"Trạng thái không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\r\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\r\n\r\n    // Update the actual values with parsed values\r\n    if (editPaidAmountFormatted) {\r\n      setEditPaidAmount(parsedPaidAmount);\r\n    }\r\n\r\n    if (editExpectedAmountFormatted && !calculateExpected) {\r\n      setEditExpectedAmount(parsedExpectedAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\r\n        status: editStatus,\r\n        note: editNote,\r\n        calculateExpected: calculateExpected\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided and not calculating automatically\r\n      if (parsedExpectedAmount && !calculateExpected) {\r\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\r\n      }\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  const getStatusBadge = (status) => {\r\n    switch (status) {\r\n      case \"PAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\">\r\n            Đã thanh toán\r\n          </span>\r\n        );\r\n      case \"UNPAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\">\r\n            Chưa thanh toán\r\n          </span>\r\n        );\r\n      case \"OVERDUE\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\">\r\n            Quá hạn\r\n          </span>\r\n        );\r\n      case \"PARTIAL\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\">\r\n            Thanh toán một phần\r\n          </span>\r\n        );\r\n      default:\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\">\r\n            {status}\r\n          </span>\r\n        );\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterStatus}\r\n                onChange={(e) => setFilterStatus(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"PAID\">Đã thanh toán</option>\r\n                <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n            <ButtonFunctionBarAdmin icon={iconUsers} text={'Tạo học phí theo lớp'} onClick={handleCreateByClass} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {tuitionPayments.length === 0 ? (\r\n        <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n          <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n            <thead className=\"bg-gray-100\">\r\n              <tr>\r\n                <th className=\"py-3 px-4 text-left\">STT</th>\r\n                <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                <th className=\"py-3 px-4 text-left\">Số tiền cần</th>\r\n                <th className=\"py-3 px-4 text-left\">Số tiền đóng</th>\r\n                <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {tuitionPayments.map((payment, index) => (\r\n                <tr\r\n                  key={payment.id}\r\n                  className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                >\r\n                  <td className=\"py-3 px-4\">\r\n                    {(page - 1) * pageSize + index + 1}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {formatCurrency(payment.expectedAmount)}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {formatCurrency(payment.paidAmount)}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.paymentDate\r\n                      ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                      : \"Chưa thanh toán\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <div className=\"flex space-x-2\">\r\n                      <button\r\n                        onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                        className=\"text-blue-500 hover:text-blue-700\"\r\n                        title=\"Xem chi tiết\"\r\n                      >\r\n                        <Eye size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleEdit(payment.id)}\r\n                        className=\"text-yellow-500 hover:text-yellow-700\"\r\n                        title=\"Chỉnh sửa\"\r\n                      >\r\n                        <Edit size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDelete(payment.id)}\r\n                        className=\"text-red-500 hover:text-red-700\"\r\n                        title=\"Xóa\"\r\n                      >\r\n                        <Trash size={16} />\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={page}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={total}\r\n            limit={pageSize}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"addCalculateExpected\"\r\n                          checked={addCalculateExpected}\r\n                          onChange={(e) => setAddCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"addCalculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${addCalculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={addExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddExpectedAmount(value);\r\n                          setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={addCalculateExpected}\r\n                    />\r\n                    {formErrors.expectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.expectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.paidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={addPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddPaidAmount(value);\r\n                          setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.paidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.paidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.status ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addStatus}\r\n                      onChange={(e) => setAddStatus(e.target.value)}\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.status && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.status}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng <span className=\"text-gray-500 text-xs font-normal\">(không bắt buộc)</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền hoặc để trống để tự động tính\"\r\n                      value={batchAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                          setBatchAmount(value ? parseInt(value, 10) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.batchAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchAmount}\r\n                      </p>\r\n                    )}\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      Nếu để trống, hệ thống sẽ tự động tính dựa trên các lớp học sinh đã tham gia\r\n                    </p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"batchByClass\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí cho tất cả học sinh trong một lớp học.</p>\r\n                {/* Form content for batch tuition by class */}\r\n                <form className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp học</label>\r\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\r\n                      <option value=\"\">Chọn lớp học</option>\r\n                      <option value=\"1\">Lớp 10A1</option>\r\n                      <option value=\"2\">Lớp 11A2</option>\r\n                      <option value=\"3\">Lớp 12A3</option>\r\n                    </select>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng</label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền cần đóng</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      placeholder=\"Nhập số tiền\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                  >\r\n                    Tạo học phí theo lớp\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"calculateExpected\"\r\n                          checked={calculateExpected}\r\n                          onChange={(e) => setCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"calculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính lại dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${calculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={editExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={calculateExpected}\r\n                    />\r\n                    {formErrors.editExpectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editExpectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={editPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.editPaidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editPaidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.editStatus ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editStatus}\r\n                      onChange={(e) => setEditStatus(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.editStatus && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editStatus}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,QACvB,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACzH,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAMgD,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE+C,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAGpD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,IAAI;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAG1D,WAAW,CACtDqD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACK,UAC3B,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC5D;EACA,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2E,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM+F,eAAe,GAAG9F,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM+F,aAAa,GAAG/F,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMgG,oBAAoB,GAAGhG,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMiG,kBAAkB,GAAGjG,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACyG,YAAY,EAAEC,eAAe,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2G,UAAU,EAAEC,aAAa,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6G,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+G,UAAU,EAAEC,aAAa,CAAC,GAAGhH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiH,YAAY,EAAEC,eAAe,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACmH,SAAS,EAAEC,YAAY,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuH,QAAQ,EAAEC,WAAW,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2H,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAChF,MAAM,CAAC6H,aAAa,EAAEC,gBAAgB,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+H,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACiI,cAAc,EAAEC,iBAAiB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmI,UAAU,EAAEC,aAAa,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqI,SAAS,EAAEC,YAAY,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuI,OAAO,EAAEC,UAAU,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC2I,MAAM,EAAEC,SAAS,CAAC,GAAG5I,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC6I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC+I,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAACiJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmJ,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAACqJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuJ,UAAU,EAAEC,aAAa,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyJ,QAAQ,EAAEC,WAAW,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5J,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC6J,WAAW,EAAEC,cAAc,CAAC,GAAG9J,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhK,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACiK,WAAW,EAAEC,cAAc,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmK,aAAa,EAAEC,gBAAgB,CAAC,GAAGpK,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMqK,mBAAmB,GAAGpK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA;EACA;EACA;EACA;EACA;;EAEAF,SAAS,CAAC,MAAM;IACdkD,QAAQ,CAAC7B,WAAW,CAAC,EAAE,CAAC,CAAC;IACzB6B,QAAQ,CAAC9B,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC8B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMqH,iBAAiB,GAAIC,SAAS,IAAK;IACvCrF,gBAAgB,CAACqF,SAAS,CAACC,EAAE,CAAC;IAC9BpF,kBAAkB,CAACmF,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCxF,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMuF,gBAAgB,GAAIC,QAAQ,IAAK;IACrCtF,iBAAiB,CAACsF,QAAQ,CAACJ,EAAE,CAAC;IAC9BhF,iBAAiB,CAACoF,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCzF,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwF,YAAY,GAAGA,CAAA,KAAM;IACzB/H,QAAQ,CACNxC,oBAAoB,CAAC;MACnBwK,MAAM,EAAElH,UAAU;MAClBL,IAAI,EAAE,CAAC;MAAE;MACTG,QAAQ;MACRqH,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAExG,YAAY;MACpByG,KAAK,EAAE3G,WAAW;MAClB4G,OAAO,EAAExG,aAAa;MACtByG,SAAS,EAAEvG,WAAW;MACtBwG,OAAO,EAAEtG;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAMuG,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACxE,SAAS,EAAE;MACdwE,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAACrE,QAAQ,EAAE;MACboE,MAAM,CAACP,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAACS,IAAI,CAACtE,QAAQ,CAAC,EAAE;MAC1CoE,MAAM,CAACP,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAAC3C,oBAAoB,IAAI,CAAChB,iBAAiB,EAAE;MAC/CkE,MAAM,CAACG,cAAc,GAAG,uDAAuD;IACjF,CAAC,MAAM,IAAIrE,iBAAiB,KAAKsE,KAAK,CAACtE,iBAAiB,CAAC,IAAIuE,MAAM,CAACvE,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3FkE,MAAM,CAACG,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAIjE,aAAa,KAAKkE,KAAK,CAAClE,aAAa,CAAC,IAAImE,MAAM,CAACnE,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MACxE8D,MAAM,CAACM,UAAU,GAAG,0BAA0B;IAChD;;IAEA;IACA,IAAI,CAAC9D,UAAU,EAAE;MACfwD,MAAM,CAACO,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAI,CAAC7D,SAAS,EAAE;MACdsD,MAAM,CAACR,MAAM,GAAG,0BAA0B;IAC5C;;IAEA;IACA,IAAIgB,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClCrF,aAAa,CAAC2E,MAAM,CAAC;MACrB;IACF;IAEAzE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMoF,WAAW,GAAG;QAClBV,MAAM,EAAEzE,SAAS;QACjBiE,KAAK,EAAE7D,QAAQ;QACf4D,MAAM,EAAE9C,SAAS;QACjBkE,IAAI,EAAEhE,OAAO;QACb2D,OAAO,EAAE/D;MACX,CAAC;;MAED;MACA,IAAI,CAACM,oBAAoB,IAAIhB,iBAAiB,EAAE;QAC9C6E,WAAW,CAACR,cAAc,GAAGE,MAAM,CAACvE,iBAAiB,CAAC;MACxD;;MAEA;MACA,IAAII,aAAa,EAAE;QACjByE,WAAW,CAACL,UAAU,GAAGD,MAAM,CAACnE,aAAa,CAAC;MAChD;;MAEA;MACA,IAAII,cAAc,EAAE;QAClBqE,WAAW,CAACE,WAAW,GAAGvE,cAAc;MAC1C;;MAEA;MACA,MAAMhF,QAAQ,CAACtC,oBAAoB,CAAC2L,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjBxJ,QAAQ,CAACxC,oBAAoB,CAAC;QAC5BiD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRoH,MAAM,EAAElH,UAAU;QAClBmH,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA9D,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,6BAA6B,CAAC,EAAE,CAAC;MACjCE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,EAAE,CAAC;MAChBE,UAAU,CAAC,EAAE,CAAC;MACdE,uBAAuB,CAAC,KAAK,CAAC;IAEhC,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD1F,aAAa,CAAC;QAAE4F,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACR1F,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDnH,SAAS,CAAC,MAAM;IACdkD,QAAQ,CACNxC,oBAAoB,CAAC;MACnBwK,MAAM,EAAElH,UAAU;MAClBL,IAAI,EAAEA,IAAI;MAAE;MACZG,QAAQ;MACRqH,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAExG,YAAY;MACpByG,KAAK,EAAE3G,WAAW;MAClB4G,OAAO,EAAExG,aAAa;MACtByG,SAAS,EAAEvG;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAAC9B,QAAQ,EAAES,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAE9B,MAAMgJ,UAAU,GAAG,MAAOrC,EAAE,IAAK;IAC/BtD,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAM4F,QAAQ,GAAG,MAAM7J,QAAQ,CAAClC,4BAA4B,CAACyJ,EAAE,CAAC,CAAC,CAACuC,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAACG,IAAI;;MAE7B;MACArE,SAAS,CAAC4B,EAAE,CAAC;MACb1B,qBAAqB,CAACkE,OAAO,CAAClB,cAAc,IAAI,EAAE,CAAC;MACnD9C,8BAA8B,CAACgE,OAAO,CAAClB,cAAc,GAAG7K,oBAAoB,CAAC+L,OAAO,CAAClB,cAAc,CAAC,GAAG,EAAE,CAAC;MAC1G5C,iBAAiB,CAAC8D,OAAO,CAACf,UAAU,IAAI,EAAE,CAAC;MAC3C7C,0BAA0B,CAAC4D,OAAO,CAACf,UAAU,GAAGhL,oBAAoB,CAAC+L,OAAO,CAACf,UAAU,CAAC,GAAG,EAAE,CAAC;MAC9F3C,kBAAkB,CAAC0D,OAAO,CAACR,WAAW,GAAG,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACW,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxG5D,aAAa,CAACwD,OAAO,CAAC7B,MAAM,IAAI,EAAE,CAAC;MACnCzB,WAAW,CAACsD,OAAO,CAACT,IAAI,IAAI,EAAE,CAAC;MAC/B3C,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACApF,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOoI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRxF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMmG,UAAU,GAAG,MAAAA,CAAO7C,EAAE,EAAEoB,MAAM,EAAER,KAAK,KAAK;IAC9ClB,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAMjH,QAAQ,CAAClC,4BAA4B,CAACyJ,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACAhG,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOoI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRxC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMoD,SAAS,GAAGA,CAAA,KAAM;IACtB;IACAlG,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,6BAA6B,CAAC,EAAE,CAAC;IACjCE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,yBAAyB,CAAC,EAAE,CAAC;IAC7BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdE,uBAAuB,CAAC,KAAK,CAAC;IAC9B1B,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAMuG,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;IACxB,MAAMM,aAAa,GAAGD,KAAK,CAACJ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvDhF,aAAa,CAACoF,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;IAChC,MAAMtC,KAAK,GAAGmC,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAC,MAAA,CAAMJ,IAAI,OAAAI,MAAA,CAAIzC,KAAK,GAAG,EAAE,OAAAyC,MAAA,CAAOzC,KAAK,IAAKA,KAAK,CAAE;IACpE5D,WAAW,CAACoG,cAAc,CAAC;;IAE3B;IACApJ,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwJ,cAAc,GAAGA,CAAA,KAAM;IAC3BtJ,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyJ,wBAAwB,GAAGA,CAAA,KAAM;IACrCvJ,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0J,mBAAmB,GAAGA,CAAA,KAAM;IAChCxJ,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMmI,eAAe,GAAGA,CAAA,KAAM;IAC5BnI,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACA4B,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChB8B,SAAS,CAAC,IAAI,CAAC;IACfE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,8BAA8B,CAAC,EAAE,CAAC;IAClCE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,0BAA0B,CAAC,EAAE,CAAC;IAC9BE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,aAAa,CAAC,EAAE,CAAC;IACjBE,WAAW,CAAC,EAAE,CAAC;IACfE,oBAAoB,CAAC,KAAK,CAAC;IAC3B;IACAE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BhD,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtBgD,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM+D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMtC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI1C,cAAc,KAAK8C,KAAK,CAAC9C,cAAc,CAAC,IAAI+C,MAAM,CAAC/C,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3E0C,MAAM,CAAC1C,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAIJ,kBAAkB,KAAKkD,KAAK,CAAClD,kBAAkB,CAAC,IAAImD,MAAM,CAACnD,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;MACvF8C,MAAM,CAAC9C,kBAAkB,GAAG,0BAA0B;IACxD;;IAEA;IACA,IAAI,CAACU,UAAU,EAAE;MACfoC,MAAM,CAACpC,UAAU,GAAG,gCAAgC;IACtD;IAEA,OAAOoC,MAAM;EACf,CAAC;;EAED;EACA,MAAMuC,0BAA0B,GAAG,MAAOzC,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMyC,gBAAgB,GAAGhF,uBAAuB,GAAGjI,kBAAkB,CAACiI,uBAAuB,CAAC,GAAGF,cAAc;IAC/G,MAAMmF,oBAAoB,GAAGrF,2BAA2B,GAAG7H,kBAAkB,CAAC6H,2BAA2B,CAAC,GAAGF,kBAAkB;;IAE/H;IACA,IAAIM,uBAAuB,EAAE;MAC3BD,iBAAiB,CAACiF,gBAAgB,CAAC;IACrC;IAEA,IAAIpF,2BAA2B,IAAI,CAACY,iBAAiB,EAAE;MACrDb,qBAAqB,CAACsF,oBAAoB,CAAC;IAC7C;;IAEA;IACA,MAAMzC,MAAM,GAAGsC,gBAAgB,CAAC,CAAC;IACjC,IAAI9B,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClCrF,aAAa,CAAC2E,MAAM,CAAC;MACrB;IACF;IAEAzE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMoF,WAAW,GAAG;QAClBL,UAAU,EAAEkC,gBAAgB,GAAGnC,MAAM,CAACmC,gBAAgB,CAAC,GAAG,CAAC;QAC3DhD,MAAM,EAAE5B,UAAU;QAClBgD,IAAI,EAAE9C,QAAQ;QACdE,iBAAiB,EAAEA;MACrB,CAAC;;MAED;MACA,IAAIyE,oBAAoB,IAAI,CAACzE,iBAAiB,EAAE;QAC9C2C,WAAW,CAACR,cAAc,GAAGE,MAAM,CAACoC,oBAAoB,CAAC;MAC3D;;MAEA;MACA,IAAI/E,eAAe,EAAE;QACnBiD,WAAW,CAACE,WAAW,GAAGnD,eAAe;MAC3C;;MAEA;MACA,MAAMpG,QAAQ,CAACnC,oBAAoB,CAAC;QAClC0J,EAAE,EAAE7B,MAAM;QACV2D;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjBxJ,QAAQ,CAACxC,oBAAoB,CAAC;QAC5BiD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRoH,MAAM,EAAElH,UAAU;QAClBmH,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAExG,YAAY;QACpByG,KAAK,EAAE3G,WAAW;QAClB4G,OAAO,EAAExG,aAAa;QACtByG,SAAS,EAAEvG;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAO2H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD1F,aAAa,CAAC;QAAE4F,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACR1F,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMmH,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAM1C,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACxF,UAAU,EAAE;MACfwF,MAAM,CAACxF,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC0F,IAAI,CAAC1F,UAAU,CAAC,EAAE;MAC5CwF,MAAM,CAACxF,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAK0F,KAAK,CAAC1F,WAAW,CAAC,IAAI2F,MAAM,CAAC3F,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEsF,MAAM,CAACtF,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACfgF,MAAM,CAAChF,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjBkF,MAAM,CAAClF,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAOkF,MAAM;EACf,CAAC;;EAED;EACA,MAAM2C,wBAAwB,GAAG,MAAO7C,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAM6C,iBAAiB,GAAGhI,oBAAoB,GAAGrF,kBAAkB,CAACqF,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACiI,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAM5C,MAAM,GAAG0C,wBAAwB,CAAC,CAAC;IACzC,IAAIlC,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClCrF,aAAa,CAAC2E,MAAM,CAAC;MACrB;IACF;IAEAzE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAMsH,SAAS,GAAG;QAChBpD,KAAK,EAAEjF,UAAU;QACjB+F,OAAO,EAAEzF,YAAY;QACrBE,UAAU;QACV4F,IAAI,EAAE1F;MACR,CAAC;;MAED;MACA,IAAI0H,iBAAiB,EAAE;QACrBC,SAAS,CAAC1C,cAAc,GAAGE,MAAM,CAACuC,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAMtL,QAAQ,CAACrC,0BAA0B,CAAC4N,SAAS,CAAC,CAAC;;MAErD;MACA/B,eAAe,CAAC,CAAC;MACjBxJ,QAAQ,CAACxC,oBAAoB,CAAC;QAC5BiD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRoH,MAAM,EAAElH,UAAU;QAClBmH,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D1F,aAAa,CAAC;QAAE4F,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACR1F,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuH,YAAY,GAAIjE,EAAE,IAAK;IAC3BpG,kBAAkB,CAACoG,EAAE,CAAC;IACtBtG,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMwK,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCzL,QAAQ,CAACvC,oBAAoB,CAACyD,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMyK,YAAY,GAAGA,CAAA,KAAM;IACzBzK,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMwK,cAAc,GAAIzD,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,oBACE1I,OAAA;UAAMoM,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE7E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,QAAQ;QACX,oBACEzM,OAAA;UAAMoM,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACEzM,OAAA;UAAMoM,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAAC;QAE/E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACEzM,OAAA;UAAMoM,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX;QACE,oBACEzM,OAAA;UAAMoM,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACvE3D;QAAM;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEb;EACF,CAAC;EAED,IAAI7L,OAAO,EAAE;IACX,oBAAOZ,OAAA,CAACnB,cAAc;MAAAyN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACE3M,OAAA;MACE8M,OAAO,EAAEA,OAAQ;MACjBV,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJO,IAAI,eACL5M,OAAA;QAAAqM,QAAA,EAAOQ;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMM,OAAO,gBACX/M,OAAA;IAAK,wBAAgB;IAACoM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCrM,OAAA,CAACd,IAAI;MAAC8N,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMQ,SAAS,gBACbjN,OAAA;IAAK,wBAAgB;IAACoM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCrM,OAAA,CAACZ,QAAQ;MAAC4N,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMS,YAAY,gBAChBlN,OAAA;IAAK,wBAAgB;IAACoM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCrM,OAAA,CAACX,QAAQ;MAAC2N,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMU,SAAS,gBACbnN,OAAA;IAAK,wBAAgB;IAACoM,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCrM,OAAA,CAACV,KAAK;MAAC0N,IAAI,EAAE;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,oBACEzM,OAAA,CAACJ,WAAW;IAAAyM,QAAA,gBACVrM,OAAA;MAAKoM,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENzM,OAAA;MAAKoM,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFrM,OAAA;QAAKoM,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBrM,OAAA;UAAKoM,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDrM,OAAA;YAAKoM,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCrM,OAAA;cACEoN,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXpB,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnErM,OAAA;gBACEyN,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzM,OAAA;cACE6N,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kEAAwC;cACpDC,KAAK,EAAEzM,UAAW;cAClB0M,QAAQ,EAAGhF,CAAC,IAAKzH,aAAa,CAACyH,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;cAC/C3B,SAAS,EAAC;YAAsI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzM,OAAA;YAAKoM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBrM,OAAA;cACEoM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAE/L,WAAY;cACnBgM,QAAQ,EAAGhF,CAAC,IAAK/G,cAAc,CAAC+G,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAEhDrM,OAAA;gBAAQ+N,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9ByB,KAAK,CAACC,IAAI,CAAC;gBAAEvE,MAAM,EAAE;cAAG,CAAC,EAAE,CAACwE,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAM1F,KAAK,GAAG0F,CAAC,GAAG,CAAC;gBACnB,MAAMrD,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;gBACrC,MAAMqD,QAAQ,GAAG3F,KAAK,GAAG,EAAE,OAAAyC,MAAA,CAAOzC,KAAK,OAAAyC,MAAA,CAAQzC,KAAK,CAAE;gBACtD,oBACE3I,OAAA;kBAAoB+N,KAAK,KAAA3C,MAAA,CAAKJ,IAAI,OAAAI,MAAA,CAAIkD,QAAQ,CAAG;kBAAAjC,QAAA,cAAAjB,MAAA,CACrCzC,KAAK,OAAAyC,MAAA,CAAIJ,IAAI;gBAAA,GADZrC,KAAK;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENzM,OAAA;YAAKoM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBrM,OAAA;cACEoM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAE7L,YAAa;cACpB8L,QAAQ,EAAGhF,CAAC,IAAK7G,eAAe,CAAC6G,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAEjDrM,OAAA;gBAAQ+N,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCzM,OAAA;gBAAQ+N,KAAK,EAAC,MAAM;gBAAA1B,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CzM,OAAA;gBAAQ+N,KAAK,EAAC,QAAQ;gBAAA1B,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CzM,OAAA;gBAAQ+N,KAAK,EAAC,SAAS;gBAAA1B,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENzM,OAAA;YAAKoM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBrM,OAAA;cACEoM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAE3L,aAAc;cACrB4L,QAAQ,EAAGhF,CAAC,IAAK3G,gBAAgB,CAAC2G,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAElDrM,OAAA;gBAAQ+N,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCzM,OAAA;gBAAQ+N,KAAK,EAAC,MAAM;gBAAA1B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCzM,OAAA;gBAAQ+N,KAAK,EAAC,OAAO;gBAAA1B,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENzM,OAAA;YAAKoM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBrM,OAAA;cACEoM,SAAS,EAAC,oDAAoD;cAC9D2B,KAAK,EAAEzL,WAAY;cACnB0L,QAAQ,EAAGhF,CAAC,IAAKzG,cAAc,CAACyG,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;cAAA1B,QAAA,gBAEhDrM,OAAA;gBAAQ+N,KAAK,EAAC,EAAE;gBAAA1B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BzM,OAAA;gBAAQ+N,KAAK,EAAC,IAAI;gBAAA1B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCzM,OAAA;gBAAQ+N,KAAK,EAAC,IAAI;gBAAA1B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCzM,OAAA;gBAAQ+N,KAAK,EAAC,IAAI;gBAAA1B,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNzM,OAAA;YAAKoM,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBrM,OAAA,CAAClC,gBAAgB;cACfiQ,KAAK,EAAErL,eAAgB;cACvB6L,eAAe,EAAE/L,aAAc;cAC/BwL,QAAQ,EAAErL,kBAAmB;cAC7B6L,QAAQ,EAAE3G,iBAAkB;cAC5B4G,OAAO,EAAExG,yBAA0B;cACnC6F,WAAW,EAAC;YAAqB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzM,OAAA;YACE8M,OAAO,EAAEvE,YAAa;YACtB6D,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3FrM,OAAA;cAAKoM,SAAS,EAAC,cAAc;cAACoB,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAf,QAAA,eACpHrM,OAAA;gBAAM2N,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACc,WAAW,EAAC,GAAG;gBAACjB,CAAC,EAAC;cAA6C;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CAAC,eAENzM,OAAA;UAAKoM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDrM,OAAA,CAAC0M,sBAAsB;YAACE,IAAI,EAAEG,OAAQ;YAACF,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAEjC;UAAU;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtFzM,OAAA,CAAC0M,sBAAsB;YAACE,IAAI,EAAEM,YAAa;YAACL,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAExB;UAAyB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChHzM,OAAA,CAAC0M,sBAAsB;YAACE,IAAI,EAAEO,SAAU;YAACN,IAAI,EAAE,sBAAuB;YAACC,OAAO,EAAEvB;UAAoB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL/L,eAAe,CAACkJ,MAAM,KAAK,CAAC,gBAC3B5J,OAAA;MAAKoM,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DrM,OAAA;QAAGoM,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,gBAENzM,OAAA;MAAKoM,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BrM,OAAA;QAAOoM,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzErM,OAAA;UAAOoM,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5BrM,OAAA;YAAAqM,QAAA,gBACErM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDzM,OAAA;cAAIoM,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRzM,OAAA;UAAAqM,QAAA,EACG3L,eAAe,CAACiO,GAAG,CAAC,CAACpE,OAAO,EAAEqE,KAAK;YAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClChP,OAAA;cAEEoM,SAAS,EAAEwC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAAvC,QAAA,gBAEvDrM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAACpL,IAAI,GAAG,CAAC,IAAIG,QAAQ,GAAGwN,KAAK,GAAG;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAAwC,aAAA,GAAAtE,OAAO,CAAC0E,IAAI,cAAAJ,aAAA,uBAAZA,aAAA,CAAcxG,QAAQ,IAAG,GAAG,KAAAyG,cAAA,GAAGvE,OAAO,CAAC0E,IAAI,cAAAH,cAAA,uBAAZA,cAAA,CAAc1G,SAAS,KAAI;cAAK;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAA0C,cAAA,GAAAxE,OAAO,CAAC0E,IAAI,cAAAF,cAAA,uBAAZA,cAAA,CAAcG,KAAK,KAAI;cAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAA2C,cAAA,GAAAzE,OAAO,CAAC0E,IAAI,cAAAD,cAAA,uBAAZA,cAAA,CAAcG,UAAU,KAAI;cAAK;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClEzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB9N,cAAc,CAACgM,OAAO,CAAClB,cAAc;cAAC;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB9N,cAAc,CAACgM,OAAO,CAACf,UAAU;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE9B,OAAO,CAAC6E;cAAc;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB9B,OAAO,CAACR,WAAW,GAChB,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACsF,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAI5B,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAAC4F,kBAAkB,CAAC,OAAO;cAAC;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBF,cAAc,CAAC5B,OAAO,CAAC7B,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG6B,OAAO,CAAC+E,SAAS,GAAG,SAAS,GAAG/E,OAAO,CAAC7B,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG6B,OAAO,CAAC7B,MAAM;cAAC;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I,CAAC,eACLzM,OAAA;gBAAIoM,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBrM,OAAA;kBAAKoM,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BrM,OAAA;oBACE8M,OAAO,EAAEA,CAAA,KAAMlC,UAAU,CAACL,OAAO,CAACxC,EAAE,EAAEwC,OAAO,CAACpB,MAAM,EAAEoB,OAAO,CAAC5B,KAAK,CAAE;oBACrEyD,SAAS,EAAC,mCAAmC;oBAC7CmD,KAAK,EAAC,mBAAc;oBAAAlD,QAAA,eAEpBrM,OAAA,CAACb,GAAG;sBAAC6N,IAAI,EAAE;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACTzM,OAAA;oBACE8M,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAACG,OAAO,CAACxC,EAAE,CAAE;oBACtCqE,SAAS,EAAC,uCAAuC;oBACjDmD,KAAK,EAAC,qBAAW;oBAAAlD,QAAA,eAEjBrM,OAAA,CAAChB,IAAI;sBAACgO,IAAI,EAAE;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACTzM,OAAA;oBACE8M,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACzB,OAAO,CAACxC,EAAE,CAAE;oBACxCqE,SAAS,EAAC,iCAAiC;oBAC3CmD,KAAK,EAAC,QAAK;oBAAAlD,QAAA,eAEXrM,OAAA,CAACf,KAAK;sBAAC+N,IAAI,EAAE;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArDAlC,OAAO,CAACxC,EAAE;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsDb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAzJ,QAAQ,KAAK,OAAO,iBACnBhD,OAAA;MAAKoM,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBrM,OAAA,CAAClB,UAAU;QACT0Q,WAAW,EAAEvO,IAAK;QAClBwO,YAAY,EAAGxO,IAAI,IAAKT,QAAQ,CAAC5B,cAAc,CAACqC,IAAI,CAAC,CAAE;QACvDyO,UAAU,EAAEvO,KAAM;QAClBwO,KAAK,EAAEvO;MAAS;QAAAkL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAIA7K,cAAc,iBACb5B,OAAA;MAAKoM,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFrM,OAAA;QAAKoM,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7ErM,OAAA;UAAIoM,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClCvK,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,cAAc,IAAI,sBAAsB,EAC3DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAAwK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACLzM,OAAA;UACE8M,OAAO,EAAE9C,eAAgB;UACzBoC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CrM,OAAA,CAACT,CAAC;YAACyN,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzM,OAAA;QAAAqM,QAAA,GACGvK,cAAc,KAAK,KAAK,iBACvB9B,OAAA;UAAKoM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBrM,OAAA;YAAGoM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvEzM,OAAA;YAAMoM,SAAS,EAAC,WAAW;YAACwD,QAAQ,EAAE7G,sBAAuB;YAAAsD,QAAA,gBAC3DrM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxHzM,OAAA,CAACjC,eAAe;gBACdgQ,KAAK,EAAEnJ,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1BsJ,QAAQ,EAAEnJ,oBAAqB;gBAC/B2J,QAAQ,EAAGS,IAAI,IAAK;kBAClBtK,YAAY,CAACsK,IAAI,CAAClH,EAAE,CAAC;kBACrBlD,oBAAoB,IAAAuG,MAAA,CAAI6D,IAAI,CAAC5G,QAAQ,OAAA+C,MAAA,CAAI6D,IAAI,CAAC7G,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACFqG,OAAO,EAAEA,CAAA,KAAM;kBACb9J,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACFiJ,WAAW,EAAC,mCAAsB;gBAClC+B,IAAI,EAAC;cAAS;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDnI,UAAU,CAAC6E,MAAM,iBAChBnJ,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAAC6E,MAAM;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrHzM,OAAA;gBACE6N,IAAI,EAAC,OAAO;gBACZzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACqE,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3GoF,KAAK,EAAEjJ,QAAS;gBAChBkJ,QAAQ,EAAGhF,CAAC,IAAKjE,WAAW,CAACiE,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACDnI,UAAU,CAACqE,KAAK,iBACf3I,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACqE,KAAK;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAArM,OAAA;kBAAKoM,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCrM,OAAA;oBACE6N,IAAI,EAAC,UAAU;oBACf9F,EAAE,EAAC,sBAAsB;oBACzB+H,OAAO,EAAE9J,oBAAqB;oBAC9BgI,QAAQ,EAAGhF,CAAC,IAAK/C,uBAAuB,CAAC+C,CAAC,CAACiF,MAAM,CAAC6B,OAAO,CAAE;oBAC3D1D,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACFzM,OAAA;oBAAO+P,OAAO,EAAC,sBAAsB;oBAAC3D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAExE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAAC+E,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,kBAAA+B,MAAA,CAAepF,oBAAoB,GAAG,aAAa,GAAG,EAAE,CAAG;gBACjK8H,WAAW,EAAC,mDAAuB;gBACnCC,KAAK,EAAE7I,0BAA2B;gBAClC8I,QAAQ,EAAGhF,CAAC,IAAK;kBACf,MAAM+E,KAAK,GAAG/E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAACiC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC1G,KAAK,CAACyE,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC9I,oBAAoB,CAAC8I,KAAK,CAAC;oBAC3B5I,6BAA6B,CAAC4I,KAAK,GAAGvP,oBAAoB,CAACyR,QAAQ,CAAClC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACvF;gBACF,CAAE;gBACFmC,QAAQ,EAAElK;cAAqB;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACDnI,UAAU,CAAC+E,cAAc,iBACxBrJ,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAAC+E,cAAc;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACkF,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHsE,WAAW,EAAC,qDAAsB;gBAClCC,KAAK,EAAEzI,sBAAuB;gBAC9B0I,QAAQ,EAAGhF,CAAC,IAAK;kBACf,MAAM+E,KAAK,GAAG/E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAACiC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC1G,KAAK,CAACyE,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC1I,gBAAgB,CAAC0I,KAAK,CAAC;oBACvBxI,yBAAyB,CAACwI,KAAK,GAAGvP,oBAAoB,CAACyR,QAAQ,CAAClC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACnF;gBACF;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDnI,UAAU,CAACkF,UAAU,iBACpBxJ,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACkF,UAAU;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,EAAC,oDAAoD;gBAC9D2B,KAAK,EAAEvI,cAAe;gBACtBwI,QAAQ,EAAGhF,CAAC,IAAKvD,iBAAiB,CAACuD,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACmF,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7GsE,KAAK,EAAErI,UAAW;gBAClBsI,QAAQ,EAAGhF,CAAC,IAAKrD,aAAa,CAACqD,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDnI,UAAU,CAACmF,OAAO,iBACjBzJ,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACmF,OAAO;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1HzM,OAAA;gBACEoM,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACoE,MAAM,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC5GqF,KAAK,EAAEnI,SAAU;gBACjBoI,QAAQ,EAAGhF,CAAC,IAAKnD,YAAY,CAACmD,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;gBAAA1B,QAAA,gBAE9CrM,OAAA;kBAAQ+N,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCzM,OAAA;kBAAQ+N,KAAK,EAAC,MAAM;kBAAA1B,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CzM,OAAA;kBAAQ+N,KAAK,EAAC,QAAQ;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CzM,OAAA;kBAAQ+N,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRnI,UAAU,CAACoE,MAAM,iBAChB1I,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACoE,MAAM;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EzM,OAAA;gBACEoM,SAAS,EAAC,oDAAoD;gBAC9D+D,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEjI,OAAQ;gBACfkI,QAAQ,EAAGhF,CAAC,IAAKjD,UAAU,CAACiD,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLnI,UAAU,CAAC6F,MAAM,iBAChBnK,OAAA;cAAKoM,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjGrM,OAAA,CAACR,WAAW;gBAACwN,IAAI,EAAE,EAAG;gBAACZ,SAAS,EAAC;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DzM,OAAA;gBAAAqM,QAAA,EAAI/H,UAAU,CAAC6F;cAAM;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACDzM,OAAA;cACE6N,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,uHAAuH;cACjI8D,QAAQ,EAAE1L,YAAa;cAAA6H,QAAA,EAEtB7H,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACA3K,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKoM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBrM,OAAA;YAAGoM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElFzM,OAAA;YAAMoM,SAAS,EAAC,WAAW;YAACwD,QAAQ,EAAG5G,CAAC,IAAK6C,wBAAwB,CAAC7C,CAAC,CAAE;YAAAqD,QAAA,gBACvErM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrHzM,OAAA;gBACE6N,IAAI,EAAC,OAAO;gBACZzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHqK,KAAK,EAAErK,UAAW;gBAClBsK,QAAQ,EAAGhF,CAAC,IAAKrF,aAAa,CAACqF,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDnI,UAAU,CAACZ,UAAU,iBACpB1D,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACZ,UAAU;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,0CAC7C,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACRzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACV,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjHkK,WAAW,EAAC,2GAA4C;gBACxDC,KAAK,EAAEjK,oBAAqB;gBAC5BkK,QAAQ,EAAGhF,CAAC,IAAK;kBACf,MAAM+E,KAAK,GAAG/E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAACiC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC1G,KAAK,CAACyE,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjChK,uBAAuB,CAACgK,KAAK,GAAGvP,oBAAoB,CAACyR,QAAQ,CAAClC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC/ElK,cAAc,CAACkK,KAAK,GAAGkC,QAAQ,CAAClC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;kBAClD;gBACF;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDnI,UAAU,CAACV,WAAW,iBACrB5D,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACV,WAAW;cAAA;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ,eACDzM,OAAA;gBAAGoM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnHzM,OAAA;gBACEoM,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH6J,KAAK,EAAE7J,UAAW;gBAClB8J,QAAQ,EAAGhF,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;gBAAA/D,QAAA,gBAERrM,OAAA;kBAAQ+N,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCzM,OAAA;kBAAQ+N,KAAK,EAAC,IAAI;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCzM,OAAA;kBAAQ+N,KAAK,EAAC,IAAI;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCzM,OAAA;kBAAQ+N,KAAK,EAAC,IAAI;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACRnI,UAAU,CAACJ,UAAU,iBACpBlE,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACJ,UAAU;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9HzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClH+J,KAAK,EAAE/J,YAAa;gBACpBgK,QAAQ,EAAGhF,CAAC,IAAK/E,eAAe,CAAC+E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;gBACjDqC,QAAQ;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDnI,UAAU,CAACN,YAAY,iBACtBhE,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACN,YAAY;cAAA;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EzM,OAAA;gBACEoM,SAAS,EAAC,oDAAoD;gBAC9D+D,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAE3J,SAAU;gBACjB4J,QAAQ,EAAGhF,CAAC,IAAK3E,YAAY,CAAC2E,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNzM,OAAA;cACE6N,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,uHAAuH;cACjI8D,QAAQ,EAAE1L,YAAa;cAAA6H,QAAA,EAEtB7H,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA3K,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKoM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBrM,OAAA;YAAGoM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE1EzM,OAAA;YAAMoM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzBrM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EzM,OAAA;gBAAQoM,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBACpErM,OAAA;kBAAQ+N,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCzM,OAAA;kBAAQ+N,KAAK,EAAC,GAAG;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCzM,OAAA;kBAAQ+N,KAAK,EAAC,GAAG;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCzM,OAAA;kBAAQ+N,KAAK,EAAC,GAAG;kBAAA1B,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7EzM,OAAA;gBACE6N,IAAI,EAAC,OAAO;gBACZzB,SAAS,EAAC;cAAoD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxFzM,OAAA;gBACE6N,IAAI,EAAC,QAAQ;gBACbzB,SAAS,EAAC,oDAAoD;gBAC9D0B,WAAW,EAAC;cAAc;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtFzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,EAAC;cAAoD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EzM,OAAA;gBACEoM,SAAS,EAAC,oDAAoD;gBAC9D+D,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC;cAAuB;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNzM,OAAA;cACE6N,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA3K,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAKoM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBrM,OAAA;YAAGoM,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/DzM,OAAA;YAAMoM,SAAS,EAAC,WAAW;YAACwD,QAAQ,EAAG5G,CAAC,IAAKyC,0BAA0B,CAACzC,CAAC,CAAE;YAAAqD,QAAA,gBACzErM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAArM,OAAA;kBAAKoM,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCrM,OAAA;oBACE6N,IAAI,EAAC,UAAU;oBACf9F,EAAE,EAAC,mBAAmB;oBACtB+H,OAAO,EAAE5I,iBAAkB;oBAC3B8G,QAAQ,EAAGhF,CAAC,IAAK7B,oBAAoB,CAAC6B,CAAC,CAACiF,MAAM,CAAC6B,OAAO,CAAE;oBACxD1D,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACFzM,OAAA;oBAAO+P,OAAO,EAAC,mBAAmB;oBAAC3D,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACRzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAAC8B,kBAAkB,GAAG,gBAAgB,GAAG,iBAAiB,kBAAAgF,MAAA,CAAelE,iBAAiB,GAAG,aAAa,GAAG,EAAE,CAAG;gBAClK4G,WAAW,EAAC,mDAAuB;gBACnCC,KAAK,EAAEzH,2BAA4B;gBACnC0H,QAAQ,EAAGhF,CAAC,IAAK;kBACf,MAAM+E,KAAK,GAAG/E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAACiC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC1G,KAAK,CAACyE,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjCxH,8BAA8B,CAACwH,KAAK,GAAGvP,oBAAoB,CAACyR,QAAQ,CAAClC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACxF;gBACF,CAAE;gBACFmC,QAAQ,EAAEhJ;cAAkB;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EACDnI,UAAU,CAAC8B,kBAAkB,iBAC5BpG,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAAC8B,kBAAkB;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACkC,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACpHsH,WAAW,EAAC,qDAAsB;gBAClCC,KAAK,EAAErH,uBAAwB;gBAC/BsH,QAAQ,EAAGhF,CAAC,IAAK;kBACf,MAAM+E,KAAK,GAAG/E,CAAC,CAACiF,MAAM,CAACF,KAAK,CAACiC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC1G,KAAK,CAACyE,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjCpH,0BAA0B,CAACoH,KAAK,GAAGvP,oBAAoB,CAACyR,QAAQ,CAAClC,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACpF;gBACF;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDnI,UAAU,CAACkC,cAAc,iBACxBxG,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACkC,cAAc;cAAA;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFzM,OAAA;gBACE6N,IAAI,EAAC,MAAM;gBACXzB,SAAS,EAAC,oDAAoD;gBAC9D2B,KAAK,EAAEnH,eAAgB;gBACvBoH,QAAQ,EAAGhF,CAAC,IAAKnC,kBAAkB,CAACmC,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAArM,OAAA;kBAAMoM,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1HzM,OAAA;gBACEoM,SAAS,6BAAAhB,MAAA,CAA6B9G,UAAU,CAACwC,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHiH,KAAK,EAAEjH,UAAW;gBAClBkH,QAAQ,EAAGhF,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACiF,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;gBAAA/D,QAAA,gBAERrM,OAAA;kBAAQ+N,KAAK,EAAC,EAAE;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCzM,OAAA;kBAAQ+N,KAAK,EAAC,MAAM;kBAAA1B,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CzM,OAAA;kBAAQ+N,KAAK,EAAC,QAAQ;kBAAA1B,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CzM,OAAA;kBAAQ+N,KAAK,EAAC,SAAS;kBAAA1B,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRnI,UAAU,CAACwC,UAAU,iBACpB9G,OAAA;gBAAGoM,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDrM,OAAA,CAACR,WAAW;kBAACwN,IAAI,EAAE,EAAG;kBAACZ,SAAS,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAACwC,UAAU;cAAA;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzM,OAAA;cAAAqM,QAAA,gBACErM,OAAA;gBAAOoM,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EzM,OAAA;gBACEoM,SAAS,EAAC,oDAAoD;gBAC9D+D,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAE/G,QAAS;gBAChBgH,QAAQ,EAAGhF,CAAC,IAAK/B,WAAW,CAAC+B,CAAC,CAACiF,MAAM,CAACF,KAAK;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLnI,UAAU,CAAC6F,MAAM,iBAChBnK,OAAA;cAAGoM,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDrM,OAAA,CAACR,WAAW;gBAACwN,IAAI,EAAE,EAAG;gBAACZ,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACnI,UAAU,CAAC6F,MAAM;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACDzM,OAAA;cACE6N,IAAI,EAAC,QAAQ;cACbzB,SAAS,EAAC,uHAAuH;cACjI8D,QAAQ,EAAE1L,YAAa;cAAA6H,QAAA,EAEtB7H,YAAY,GAAG,eAAe,GAAG;YAAc;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA3K,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAAqM,QAAA,EACG7E,WAAW,gBACVxH,OAAA;YAAKoM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDrM,OAAA,CAACnB,cAAc;cAAAyN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJ5L,cAAc,gBAChBb,OAAA;YAAKoM,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrM,OAAA;cAAKqQ,GAAG,EAAEzI,mBAAoB;cAACwE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1ErM,OAAA;gBAAKoM,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnErM,OAAA;kBAAKoM,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrM,OAAA;oBAAKoM,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNzM,OAAA;oBAAAqM,QAAA,gBACErM,OAAA;sBAAIoM,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClEzM,OAAA;sBAAGoM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEzM,OAAA;sBAAGoM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAI5B,IAAI,CAAC,CAAC,CAAC4E,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGNzM,OAAA;gBAAKoM,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCrM,OAAA;kBAAIoM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEzM,OAAA;kBAAKoM,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBrM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAAtM,oBAAA,GAACU,cAAc,CAACoO,IAAI,cAAA9O,oBAAA,uBAAnBA,oBAAA,CAAqBkI,QAAQ,EAAC,GAAC,GAAAjI,qBAAA,GAACS,cAAc,CAACoO,IAAI,cAAA7O,qBAAA,uBAAnBA,qBAAA,CAAqBgI,SAAS;kBAAA;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpHzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAApM,qBAAA,GAAAQ,cAAc,CAACoO,IAAI,cAAA5O,qBAAA,uBAAnBA,qBAAA,CAAqBiQ,KAAK,KAAI,UAAU;kBAAA;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrGzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAnM,qBAAA,GAAAO,cAAc,CAACoO,IAAI,cAAA3O,qBAAA,uBAAnBA,qBAAA,CAAqB4O,KAAK,KAAI,UAAU;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3FzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAlM,qBAAA,GAAAM,cAAc,CAACoO,IAAI,cAAA1O,qBAAA,uBAAnBA,qBAAA,CAAqB4O,UAAU,KAAI,UAAU;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzM,OAAA;gBAAAqM,QAAA,gBACErM,OAAA;kBAAIoM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEzM,OAAA;kBAAKoM,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvErM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC5L,cAAc,CAACuO,cAAc;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClFzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAClO,cAAc,CAACsC,cAAc,CAACwI,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClHzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAClO,cAAc,CAACsC,cAAc,CAAC2I,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7GzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAClO,cAAc,CAAC,CAACsC,cAAc,CAACwI,cAAc,IAAI,CAAC,KAAKxI,cAAc,CAAC2I,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9IzM,OAAA;oBAAAqM,QAAA,gBACErM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrDzM,OAAA;sBAAMoM,SAAS,oCAAAhB,MAAA,CAAoCvK,cAAc,CAAC6H,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjH7H,cAAc,CAAC6H,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5D7H,cAAc,CAAC6H,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAA2D,QAAA,EACFxL,cAAc,CAAC6H,MAAM,KAAK,MAAM,GAAG,eAAe,GACjD7H,cAAc,CAAC6H,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpD7H,cAAc,CAAC6H,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC5L,cAAc,CAACkJ,WAAW,GAAG,IAAIU,IAAI,CAAC5J,cAAc,CAACkJ,WAAW,CAAC,CAACsF,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzKzM,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC5L,cAAc,CAAC4I,OAAO,GAAG,IAAIgB,IAAI,CAAC5J,cAAc,CAAC4I,OAAO,CAAC,CAAC4F,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJ5L,cAAc,CAACiJ,IAAI,iBAAI9J,OAAA;oBAAAqM,QAAA,gBAAGrM,OAAA;sBAAMoM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC5L,cAAc,CAACiJ,IAAI;kBAAA;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL3L,yBAAyB,IAAIA,yBAAyB,CAACyP,aAAa,IAAIzP,yBAAyB,CAACyP,aAAa,CAAC3G,MAAM,GAAG,CAAC,iBACzH5J,OAAA;gBAAAqM,QAAA,gBACErM,OAAA;kBAAIoM,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEzM,OAAA;kBAAKoM,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBvL,yBAAyB,CAACyP,aAAa,CAAC5B,GAAG,CAAE3N,OAAO,iBACnDhB,OAAA;oBAAsBoM,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9ErM,OAAA;sBAAKoM,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDrM,OAAA;wBAAIoM,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAErL,OAAO,CAACoL;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpDzM,OAAA;wBAAMoM,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAACrL,OAAO,CAACwP,UAAU;sBAAA;wBAAAlE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNzM,OAAA;sBAAAqM,QAAA,gBAAGrM,OAAA;wBAAMoM,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAClO,cAAc,CAACyC,OAAO,CAACyP,MAAM,CAAC;oBAAA;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/FzM,OAAA;sBAAAqM,QAAA,gBAAGrM,OAAA;wBAAMoM,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAIhC,IAAI,CAACzJ,OAAO,CAAC0P,QAAQ,CAAC,CAACrB,kBAAkB,CAAC,CAAC;oBAAA;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrHzL,OAAO,CAAC8I,IAAI,iBAAI9J,OAAA;sBAAAqM,QAAA,gBAAGrM,OAAA;wBAAMoM,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACzL,OAAO,CAAC8I,IAAI;oBAAA;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtFzL,OAAO,CAAC+G,EAAE;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNzM,OAAA;kBAAKoM,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CrM,OAAA;oBAAGoM,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAAC9N,cAAc,CAACuC,yBAAyB,CAAC6P,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjHzM,OAAA;oBAAGoM,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAAC9N,cAAc,CAACsC,cAAc,CAACwI,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGDzM,OAAA;gBAAKoM,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCrM,OAAA;kBAAKoM,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDrM,OAAA;oBAAAqM,QAAA,gBACErM,OAAA;sBAAGoM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClEzM,OAAA;sBAAGoM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACNzM,OAAA;oBAAKoM,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BrM,OAAA;sBAAGoM,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxDzM,OAAA;sBAAKoM,SAAS,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5BzM,OAAA;sBAAGoM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxDzM,OAAA;sBAAGoM,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGPzM,OAAA;cAAKoM,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCrM,OAAA;gBACE8M,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAACvJ,cAAc,CAACkH,EAAE,CAAE;gBAC7CqE,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENzM,OAAA;YAAKoM,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDzM,OAAA,CAACjB,YAAY;MACX6R,MAAM,EAAEpP,gBAAiB;MACzBqP,SAAS,EAAE5E,aAAc;MACzBY,IAAI,EAAC,qGAAmD;MACxDiE,OAAO,EAAE5E;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAACvM,EAAA,CAx7CID,kBAAkB;EAAA,QACLxC,WAAW,EACXE,WAAW,EACuED,WAAW,EAEhEA,WAAW;AAAA;AAAAqT,EAAA,GALrD9Q,kBAAkB;AA07CxB,eAAeA,kBAAkB;AAAC,IAAA8Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}