{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search, DollarSign, TrendingUp } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from \"src/features/tuition/tuitionSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    page,\n    totalPages,\n    total,\n    pageSize\n  } = useSelector(state => state.tuition.pagination);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho thống kê\n  const [showStatistics, setShowStatistics] = useState(false);\n  const [statisticsData, setStatisticsData] = useState(null);\n  const [statisticsLoading, setStatisticsLoading] = useState(false);\n  const [statisticsStartMonth, setStatisticsStartMonth] = useState(\"\");\n  const [statisticsEndMonth, setStatisticsEndMonth] = useState(\"\");\n  const [statisticsClass, setStatisticsClass] = useState(\"\");\n\n  // State cho bộ lọc\n  // const [filterMonth, setFilterMonth] = useState(\"\");\n  // const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\n  // const [filterOverdue, setFilterOverdue] = useState(\"\");\n  // const [filterClass, setFilterClass] = useState(\"\");\n  // const [filterClassId, setFilterClassId] = useState(\"\");\n\n  const {\n    filterMonth,\n    filterIsPaid,\n    filterOverdue,\n    filterClass,\n    filterClassId\n  } = useSelector(state => state.tuition);\n  const setFilterMonth = month => {\n    dispatch(setFilterMonthSlice(month));\n  };\n  const setFilterIsPaid = isPaid => {\n    dispatch(setFilterIsPaidSlice(isPaid));\n  };\n  const setFilterOverdue = overdue => {\n    dispatch(setFilterOverdueSlice(overdue));\n  };\n  const setFilterClass = classValue => {\n    dispatch(setFilterClassSlice(classValue));\n  };\n  const setFilterClassId = classId => {\n    dispatch(setFilterClassIdSlice(classId));\n  };\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\n  const [addNote, setAddNote] = useState(\"\");\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editDueDate, setEditDueDate] = useState(\"\");\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\n  const [editNote, setEditNote] = useState(\"\");\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n\n  // useEffect(() => {\n  //   if (!didInit) {\n  //     dispatch(resetFilters());\n  //     setDidInit(true);\n  //   }\n  // }, [dispatch, didInit]);\n\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      isPaid: filterIsPaid,\n      // Thay đổi từ status thành isPaid\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        isPaid: addIsPaid,\n        // Sử dụng isPaid thay vì status\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddIsPaid(false);\n      setAddNote(\"\");\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  useEffect(() => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: page,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      isPaid: filterIsPaid,\n      // Thay đổi từ status thành isPaid\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, page, pageSize]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : \"\");\n      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status\n      setEditNote(payment.note || \"\");\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddIsPaid(false); // Reset isPaid thay vì status\n    setAddNote(\"\");\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditPaymentDate(\"\");\n    setEditDueDate(\"\");\n    setEditIsPaid(false); // Reset isPaid thay vì status\n    setEditNote(\"\");\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate due date (required)\n    if (!editDueDate) {\n      errors.editDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        isPaid: editIsPaid,\n        // Sử dụng isPaid thay vì status\n        note: editNote,\n        dueDate: editDueDate\n      };\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        isPaid: filterIsPaid,\n        // Sử dụng isPaid thay vì status\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n\n  // Hàm xử lý thao tác nhanh đánh dấu đã thanh toán\n  const handleQuickPayment = async (paymentId, currentIsPaid) => {\n    try {\n      const today = new Date().toISOString().split('T')[0]; // Ngày hiện tại\n\n      const paymentData = {\n        isPaid: !currentIsPaid,\n        // Toggle trạng thái\n        paymentDate: !currentIsPaid ? today : null // Nếu đánh dấu đã thanh toán thì set ngày hiện tại, nếu bỏ đánh dấu thì xóa ngày\n      };\n      await dispatch(updateTuitionPayment({\n        id: paymentId,\n        paymentData\n      }));\n\n      // Refresh data\n      // dispatch(fetchTuitionPayments({\n      //   page: page,\n      //   pageSize,\n      //   search: inputValue,\n      //   sortOrder: \"DESC\",\n      //   isPaid: filterIsPaid,\n      //   month: filterMonth,\n      //   overdue: filterOverdue,\n      //   userClass: filterClass\n      // }));\n    } catch (error) {\n      console.error(\"Error updating payment status:\", error);\n    }\n  };\n\n  // Hàm mở modal thống kê lần đầu\n  const handleOpenStatistics = async () => {\n    // Set default values nếu chưa có\n    let startMonth = statisticsStartMonth;\n    let endMonth = statisticsEndMonth;\n    if (!startMonth && !endMonth) {\n      const currentDate = new Date();\n      const currentMonth = currentDate.toISOString().slice(0, 7); // YYYY-MM format\n      endMonth = currentMonth;\n      setStatisticsEndMonth(currentMonth);\n\n      // Set start month là 3 tháng trước\n      const threeMonthsAgo = new Date(currentDate);\n      threeMonthsAgo.setMonth(currentDate.getMonth() - 2);\n      startMonth = threeMonthsAgo.toISOString().slice(0, 7);\n      setStatisticsStartMonth(startMonth);\n    }\n\n    // Load statistics với default values\n    setStatisticsLoading(true);\n    try {\n      const params = {\n        userClass: statisticsClass || undefined,\n        startMonth: startMonth || undefined,\n        endMonth: endMonth || undefined\n      };\n      await dispatch(fetchTuitionStatistics(params));\n\n      // Sử dụng dữ liệu từ tuitionStatistics\n      if (tuitionStatistics) {\n        const {\n          totalStatistics,\n          monthlyStatistics,\n          classStatistics\n        } = tuitionStatistics;\n        const formattedData = {\n          overview: {\n            totalStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.totalStudents) || 0,\n            paidStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.paidStudents) || 0,\n            unpaidStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.unpaidStudents) || 0,\n            paymentRate: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.paymentRate) || 0\n          },\n          monthlyStats: (monthlyStatistics === null || monthlyStatistics === void 0 ? void 0 : monthlyStatistics.map(stat => ({\n            month: stat.month,\n            monthFormatted: stat.monthFormatted,\n            total: stat.totalStudents,\n            paid: stat.paidStudents,\n            unpaid: stat.unpaidStudents,\n            paymentRate: stat.paymentRate\n          }))) || [],\n          classStats: (classStatistics === null || classStatistics === void 0 ? void 0 : classStatistics.map(stat => ({\n            class: stat.userClass,\n            total: stat.totalStudents,\n            paid: stat.paidStudents,\n            unpaid: stat.unpaidStudents,\n            paymentRate: stat.paymentRate\n          }))) || [],\n          apiData: tuitionStatistics\n        };\n        setStatisticsData(formattedData);\n      }\n    } catch (error) {\n      console.error(\"Error fetching statistics:\", error);\n    } finally {\n      setStatisticsLoading(false);\n    }\n  };\n\n  // Hàm xử lý hiển thị thống kê\n  const handleShowStatistics = async () => {\n    setStatisticsLoading(true);\n    try {\n      // Sử dụng fetchTuitionStatistics từ Redux với statistics parameters riêng\n      const params = {\n        userClass: statisticsClass || undefined,\n        startMonth: statisticsStartMonth || undefined,\n        endMonth: statisticsEndMonth || undefined\n      };\n      await dispatch(fetchTuitionStatistics(params));\n\n      // Sử dụng dữ liệu từ tuitionStatistics (đã được cập nhật trong Redux store)\n    } catch (error) {\n      console.error(\"Error fetching statistics:\", error);\n    } finally {\n      setStatisticsLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (tuitionStatistics) {\n      const {\n        totalStatistics,\n        monthlyStatistics,\n        classStatistics\n      } = tuitionStatistics;\n\n      // Format dữ liệu cho UI\n      const formattedData = {\n        overview: {\n          totalStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.totalStudents) || 0,\n          paidStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.paidStudents) || 0,\n          unpaidStudents: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.unpaidStudents) || 0,\n          paymentRate: (totalStatistics === null || totalStatistics === void 0 ? void 0 : totalStatistics.paymentRate) || 0\n        },\n        monthlyStats: (monthlyStatistics === null || monthlyStatistics === void 0 ? void 0 : monthlyStatistics.map(stat => ({\n          month: stat.month,\n          monthFormatted: stat.monthFormatted,\n          total: stat.totalStudents,\n          paid: stat.paidStudents,\n          unpaid: stat.unpaidStudents,\n          paymentRate: stat.paymentRate\n        }))) || [],\n        classStats: (classStatistics === null || classStatistics === void 0 ? void 0 : classStatistics.map(stat => ({\n          class: stat.userClass,\n          total: stat.totalStudents,\n          paid: stat.paidStudents,\n          unpaid: stat.unpaidStudents,\n          paymentRate: stat.paymentRate\n        }))) || [],\n        apiData: tuitionStatistics // Lưu toàn bộ API data\n      };\n      setStatisticsData(formattedData);\n    } else {\n      console.error(\"No tuitionStatistics data available\");\n      // Fallback: Sử dụng dữ liệu local nếu API không trả về\n      const totalStudents = tuitionPayments.length;\n      const paidStudents = tuitionPayments.filter(payment => payment.isPaid).length;\n      const unpaidStudents = totalStudents - paidStudents;\n      const paymentRate = totalStudents > 0 ? (paidStudents / totalStudents * 100).toFixed(1) : 0;\n      setStatisticsData({\n        overview: {\n          totalStudents,\n          paidStudents,\n          unpaidStudents,\n          paymentRate\n        },\n        monthlyStats: [],\n        classStats: [],\n        apiData: null\n      });\n    }\n  }, [tuitionStatistics]);\n\n  // getStatusBadge function removed - replaced with inline status display\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 743,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 750,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 749,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 755,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 761,\n    columnNumber: 5\n  }, this);\n  const iconStatistics = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(BarChart2, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 767,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterIsPaid,\n              onChange: e => setFilterIsPaid(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconStatistics,\n            text: 'Xem thống kê',\n            onClick: handleOpenStatistics\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 7\n    }, this), tuitionPayments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 898,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"(S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i c\\u1EE7a ph\\u1EE5 huynh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"(S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i c\\u1EE7a h\\u1ECDc sinh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ghi ch\\xFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user, _payment$user2, _payment$user3, _payment$user4, _payment$user5, _payment$user6;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (page - 1) * pageSize + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.lastName) + \" \" + ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4 align-top\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col gap-y-0.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_payment$user5 = payment.user) === null || _payment$user5 === void 0 ? void 0 : _payment$user5.phone) || \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_payment$user6 = payment.user) === null || _payment$user6 === void 0 ? void 0 : _payment$user6.password) || \"Không có mật khẩu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(payment.isPaid ? 'bg-green-100 text-green-800' : payment.isOverdue ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                  children: payment.isPaid ? 'Đã thanh toán' : payment.isOverdue ? 'Quá hạn' : 'Chưa thanh toán'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  title: payment.note,\n                  children: payment.note ? payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2 items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center cursor-pointer\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: payment.isPaid,\n                      onChange: () => handleQuickPayment(payment.id, payment.isPaid),\n                      className: \"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer\",\n                      title: payment.isPaid ? \"Bỏ đánh dấu đã thanh toán\" : \"Đánh dấu đã thanh toán\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 978,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleView(payment.id, payment.userId, payment.month),\n                      className: \"text-blue-500 hover:text-blue-700\",\n                      title: \"Xem chi ti\\u1EBFt\",\n                      children: /*#__PURE__*/_jsxDEV(Eye, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 994,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEdit(payment.id),\n                      className: \"text-yellow-500 hover:text-yellow-700\",\n                      title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                      children: /*#__PURE__*/_jsxDEV(Edit, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1001,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDelete(payment.id),\n                      className: \"text-red-500 hover:text-red-700\",\n                      title: \"X\\xF3a\",\n                      children: /*#__PURE__*/_jsxDEV(Trash, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1008,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 19\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 903,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 902,\n      columnNumber: 9\n    }, this), viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: page,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: total,\n        limit: pageSize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1021,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1020,\n      columnNumber: 9\n    }, this), showStatistics && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-800\",\n              children: \"Th\\u1ED1ng k\\xEA thanh to\\xE1n h\\u1ECDc ph\\xED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowStatistics(false),\n              className: \"text-gray-500 hover:text-gray-700\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"T\\u1EEB th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                value: statisticsStartMonth,\n                onChange: e => setStatisticsStartMonth(e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"\\u0110\\u1EBFn th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                value: statisticsEndMonth,\n                onChange: e => setStatisticsEndMonth(e.target.value),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"L\\u1EDBp h\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                value: statisticsClass,\n                onChange: e => setStatisticsClass(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"T\\u1EA5t c\\u1EA3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"Kh\\u1ED1i 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Kh\\u1ED1i 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Kh\\u1ED1i 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleShowStatistics,\n                disabled: statisticsLoading,\n                className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: statisticsLoading ? \"Đang tải...\" : \"Cập nhật\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1091,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1090,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 13\n        }, this), statisticsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1103,\n          columnNumber: 15\n        }, this) : statisticsData ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-8 w-8 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-blue-600\",\n                    children: \"T\\u1ED5ng h\\u1ECDc sinh\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1114,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-blue-900\",\n                    children: statisticsData.overview.totalStudents\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1115,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1113,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"h-8 w-8 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1122,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-green-600\",\n                    children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-green-900\",\n                    children: statisticsData.overview.paidStudents\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1123,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"h-8 w-8 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1132,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-red-600\",\n                    children: \"Ch\\u01B0a thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1134,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-red-900\",\n                    children: statisticsData.overview.unpaidStudents\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1133,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1130,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 p-4 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-8 w-8 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1142,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-purple-600\",\n                    children: \"T\\u1EF7 l\\u1EC7 thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-2xl font-bold text-purple-900\",\n                    children: [statisticsData.overview.paymentRate, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1145,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1109,\n            columnNumber: 17\n          }, this), statisticsData.monthlyStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4\",\n              children: \"Th\\u1ED1ng k\\xEA theo th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"Th\\xE1ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1159,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1ED5ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1160,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"\\u0110\\xE3 \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1161,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"Ch\\u01B0a \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1162,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1EF7 l\\u1EC7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1163,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1158,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  className: \"divide-y divide-gray-200\",\n                  children: statisticsData.monthlyStats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.monthFormatted\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1169,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1170,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-green-600 font-medium\",\n                      children: stat.paid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1171,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-red-600 font-medium\",\n                      children: stat.unpaid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1172,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: [stat.paymentRate, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1173,\n                      columnNumber: 31\n                    }, this)]\n                  }, stat.month, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1168,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 19\n          }, this), statisticsData.classStats.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4\",\n              children: \"Th\\u1ED1ng k\\xEA theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1187,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"L\\u1EDBp\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1192,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1ED5ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1193,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"\\u0110\\xE3 \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"Ch\\u01B0a \\u0111\\xF3ng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\",\n                      children: \"T\\u1EF7 l\\u1EC7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1196,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  className: \"divide-y divide-gray-200\",\n                  children: statisticsData.classStats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.class\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1202,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: stat.total\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1203,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-green-600 font-medium\",\n                      children: stat.paid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1204,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-red-600 font-medium\",\n                      children: stat.unpaid\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1205,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-3 text-sm text-gray-900\",\n                      children: [stat.paymentRate, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1206,\n                      columnNumber: 31\n                    }, this)]\n                  }, stat.class, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1201,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1107,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 text-center text-gray-500\",\n          children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u th\\u1ED1ng k\\xEA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1218,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1033,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1247,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1251,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1251,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1252,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1250,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1274,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1274,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1275,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1283,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1273,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1290,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1288,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1298,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1299,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1306,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"addIsPaid\",\n                  checked: addIsPaid,\n                  onChange: e => setAddIsPaid(e.target.checked),\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1314,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"addIsPaid\",\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1321,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1311,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1337,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1342,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1246,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1354,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1358,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1357,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1373,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1380,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1374,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1387,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1386,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1392,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1392,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1393,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1402,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1401,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1391,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1407,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1408,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1406,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1416,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1356,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1353,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1429,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1433,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1434,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1432,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1442,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editDueDate,\n                onChange: e => setEditDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1443,\n                columnNumber: 21\n              }, this), formErrors.editDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1456,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"editIsPaid\",\n                  checked: editIsPaid,\n                  onChange: e => setEditIsPaid(e.target.checked),\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"editIsPaid\",\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1465,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1455,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1471,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1472,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1470,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1482,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1481,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1485,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1431,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1428,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1500,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1499,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1508,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1512,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1513,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1514,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1511,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1507,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1506,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1524,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1524,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1525,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1525,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1526,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1526,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1527,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1527,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1523,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1533,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1535,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1535,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1536,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1536,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1537,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1537,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1538,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1538,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1540,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1541,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1539,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1552,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1552,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1553,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1553,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1554,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1554,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1534,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1532,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1561,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1566,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1567,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1565,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1571,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1571,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1572,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1572,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1573,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1573,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1564,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1562,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1578,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1579,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1577,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1560,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1588,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1589,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1587,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1592,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1593,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1594,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1595,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1591,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1586,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1585,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1504,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1603,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1602,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1503,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1612,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1497,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1228,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1622,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 773,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"sqwWKOm8OLSRyGUDG0moecrRsfY=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "DollarSign", "TrendingUp", "AdminLayout", "FunctionBarAdmin", "Chart", "setFilterClassIdSlice", "setFilterMonthSlice", "setFilterIsPaidSlice", "setFilterOverdueSlice", "setFilterClassSlice", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "page", "totalPages", "total", "pageSize", "pagination", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "showStatistics", "setShowStatistics", "statisticsData", "setStatisticsData", "statisticsLoading", "setStatisticsLoading", "statisticsStartMonth", "setStatisticsStartMonth", "statisticsEndMonth", "setStatisticsEndMonth", "statisticsClass", "setStatisticsClass", "filterMonth", "filterIsPaid", "filterOverdue", "filterClass", "filterClassId", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "month", "setFilterIsPaid", "isPaid", "setFilterOverdue", "overdue", "setFilterClass", "classValue", "setFilterClassId", "classId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addIsPaid", "setAddIsPaid", "addNote", "setAddNote", "editId", "setEditId", "editPaymentDate", "setEditPaymentDate", "editDueDate", "setEditDueDate", "editIsPaid", "setEditIsPaid", "editNote", "setEditNote", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "sortOrder", "userClass", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "dueDate", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "error", "console", "submit", "handleEdit", "response", "unwrap", "payment", "data", "Date", "toISOString", "split", "handleView", "handleAdd", "today", "formattedDate", "year", "getFullYear", "getMonth", "formattedMonth", "concat", "handleBatchAdd", "handleCreateBatchTuition", "validateEditForm", "handleUpdateTuitionPayment", "validateBatchTuitionForm", "isNaN", "Number", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "expectedAmount", "handleDelete", "confirmDelete", "cancelDelete", "handleQuickPayment", "paymentId", "currentIsPaid", "handleOpenStatistics", "currentDate", "currentMonth", "slice", "threeMonthsAgo", "setMonth", "params", "undefined", "totalStatistics", "monthlyStatistics", "classStatistics", "formattedData", "overview", "totalStudents", "paidStudents", "unpaidStudents", "paymentRate", "monthlyStats", "map", "stat", "monthFormatted", "paid", "unpaid", "classStats", "class", "apiData", "handleShowStatistics", "filter", "toFixed", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "className", "children", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "iconStatistics", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "type", "placeholder", "value", "onChange", "target", "Array", "from", "_", "i", "monthStr", "selectedClassId", "onSelect", "onClear", "strokeWidth", "index", "_payment$user", "_payment$user2", "_payment$user3", "_payment$user4", "_payment$user5", "_payment$user6", "user", "highSchool", "phone", "password", "toLocaleDateString", "isOverdue", "title", "substring", "checked", "currentPage", "onPageChange", "totalItems", "limit", "disabled", "onSubmit", "role", "htmlFor", "rows", "required", "ref", "paidAmount", "status", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search, DollarSign, TrendingUp } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\nimport { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from \"src/features/tuition/tuitionSlice\";\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { page, totalPages, total, pageSize } = useSelector(\r\n    (state) => state.tuition.pagination\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho thống kê\r\n  const [showStatistics, setShowStatistics] = useState(false);\r\n  const [statisticsData, setStatisticsData] = useState(null);\r\n  const [statisticsLoading, setStatisticsLoading] = useState(false);\r\n  const [statisticsStartMonth, setStatisticsStartMonth] = useState(\"\");\r\n  const [statisticsEndMonth, setStatisticsEndMonth] = useState(\"\");\r\n  const [statisticsClass, setStatisticsClass] = useState(\"\");\r\n\r\n  // State cho bộ lọc\r\n  // const [filterMonth, setFilterMonth] = useState(\"\");\r\n  // const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\r\n  // const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  // const [filterClass, setFilterClass] = useState(\"\");\r\n  // const [filterClassId, setFilterClassId] = useState(\"\");\r\n\r\n  const { filterMonth, filterIsPaid, filterOverdue, filterClass, filterClassId } = useSelector(\r\n    (state) => state.tuition\r\n  );\r\n\r\n  const setFilterMonth = (month) => {\r\n    dispatch(setFilterMonthSlice(month));\r\n  };\r\n\r\n  const setFilterIsPaid = (isPaid) => {\r\n    dispatch(setFilterIsPaidSlice(isPaid));\r\n  };\r\n\r\n  const setFilterOverdue = (overdue) => {\r\n    dispatch(setFilterOverdueSlice(overdue));\r\n  };\r\n\r\n  const setFilterClass = (classValue) => {\r\n    dispatch(setFilterClassSlice(classValue));\r\n  };\r\n\r\n  const setFilterClassId = (classId) => {\r\n    dispatch(setFilterClassIdSlice(classId));\r\n  };\r\n\r\n\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\r\n  const [addNote, setAddNote] = useState(\"\");\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editDueDate, setEditDueDate] = useState(\"\");\r\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\r\n  const [editNote, setEditNote] = useState(\"\");\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  // useEffect(() => {\r\n  //   if (!didInit) {\r\n  //     dispatch(resetFilters());\r\n  //     setDidInit(true);\r\n  //   }\r\n  // }, [dispatch, didInit]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        isPaid: addIsPaid, // Sử dụng isPaid thay vì status\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddIsPaid(false);\r\n      setAddNote(\"\");\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: page, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, page, pageSize]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : \"\");\r\n      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status\r\n      setEditNote(payment.note || \"\");\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddIsPaid(false); // Reset isPaid thay vì status\r\n    setAddNote(\"\");\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditPaymentDate(\"\");\r\n    setEditDueDate(\"\");\r\n    setEditIsPaid(false); // Reset isPaid thay vì status\r\n    setEditNote(\"\");\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate due date (required)\r\n    if (!editDueDate) {\r\n      errors.editDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        isPaid: editIsPaid, // Sử dụng isPaid thay vì status\r\n        note: editNote,\r\n        dueDate: editDueDate\r\n      };\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Sử dụng isPaid thay vì status\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  // Hàm xử lý thao tác nhanh đánh dấu đã thanh toán\r\n  const handleQuickPayment = async (paymentId, currentIsPaid) => {\r\n    try {\r\n      const today = new Date().toISOString().split('T')[0]; // Ngày hiện tại\r\n\r\n      const paymentData = {\r\n        isPaid: !currentIsPaid, // Toggle trạng thái\r\n        paymentDate: !currentIsPaid ? today : null, // Nếu đánh dấu đã thanh toán thì set ngày hiện tại, nếu bỏ đánh dấu thì xóa ngày\r\n      };\r\n\r\n      await dispatch(updateTuitionPayment({\r\n        id: paymentId,\r\n        paymentData\r\n      }));\r\n\r\n      // Refresh data\r\n      // dispatch(fetchTuitionPayments({\r\n      //   page: page,\r\n      //   pageSize,\r\n      //   search: inputValue,\r\n      //   sortOrder: \"DESC\",\r\n      //   isPaid: filterIsPaid,\r\n      //   month: filterMonth,\r\n      //   overdue: filterOverdue,\r\n      //   userClass: filterClass\r\n      // }));\r\n    } catch (error) {\r\n      console.error(\"Error updating payment status:\", error);\r\n    }\r\n  };\r\n\r\n  // Hàm mở modal thống kê lần đầu\r\n  const handleOpenStatistics = async () => {\r\n    // Set default values nếu chưa có\r\n    let startMonth = statisticsStartMonth;\r\n    let endMonth = statisticsEndMonth;\r\n\r\n    if (!startMonth && !endMonth) {\r\n      const currentDate = new Date();\r\n      const currentMonth = currentDate.toISOString().slice(0, 7); // YYYY-MM format\r\n      endMonth = currentMonth;\r\n      setStatisticsEndMonth(currentMonth);\r\n\r\n      // Set start month là 3 tháng trước\r\n      const threeMonthsAgo = new Date(currentDate);\r\n      threeMonthsAgo.setMonth(currentDate.getMonth() - 2);\r\n      startMonth = threeMonthsAgo.toISOString().slice(0, 7);\r\n      setStatisticsStartMonth(startMonth);\r\n    }\r\n\r\n    // Load statistics với default values\r\n    setStatisticsLoading(true);\r\n    try {\r\n      const params = {\r\n        userClass: statisticsClass || undefined,\r\n        startMonth: startMonth || undefined,\r\n        endMonth: endMonth || undefined\r\n      };\r\n\r\n      await dispatch(fetchTuitionStatistics(params));\r\n\r\n      // Sử dụng dữ liệu từ tuitionStatistics\r\n      if (tuitionStatistics) {\r\n        const { totalStatistics, monthlyStatistics, classStatistics } = tuitionStatistics;\r\n\r\n        const formattedData = {\r\n          overview: {\r\n            totalStudents: totalStatistics?.totalStudents || 0,\r\n            paidStudents: totalStatistics?.paidStudents || 0,\r\n            unpaidStudents: totalStatistics?.unpaidStudents || 0,\r\n            paymentRate: totalStatistics?.paymentRate || 0\r\n          },\r\n          monthlyStats: monthlyStatistics?.map(stat => ({\r\n            month: stat.month,\r\n            monthFormatted: stat.monthFormatted,\r\n            total: stat.totalStudents,\r\n            paid: stat.paidStudents,\r\n            unpaid: stat.unpaidStudents,\r\n            paymentRate: stat.paymentRate\r\n          })) || [],\r\n          classStats: classStatistics?.map(stat => ({\r\n            class: stat.userClass,\r\n            total: stat.totalStudents,\r\n            paid: stat.paidStudents,\r\n            unpaid: stat.unpaidStudents,\r\n            paymentRate: stat.paymentRate\r\n          })) || [],\r\n          apiData: tuitionStatistics\r\n        };\r\n\r\n        setStatisticsData(formattedData);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching statistics:\", error);\r\n    } finally {\r\n      setStatisticsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý hiển thị thống kê\r\n  const handleShowStatistics = async () => {\r\n    setStatisticsLoading(true);\r\n    try {\r\n      // Sử dụng fetchTuitionStatistics từ Redux với statistics parameters riêng\r\n      const params = {\r\n        userClass: statisticsClass || undefined,\r\n        startMonth: statisticsStartMonth || undefined,\r\n        endMonth: statisticsEndMonth || undefined\r\n      };\r\n\r\n      await dispatch(fetchTuitionStatistics(params));\r\n\r\n      // Sử dụng dữ liệu từ tuitionStatistics (đã được cập nhật trong Redux store)\r\n    } catch (error) {\r\n      console.error(\"Error fetching statistics:\", error);\r\n    } finally {\r\n      setStatisticsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (tuitionStatistics) {\r\n      const { totalStatistics, monthlyStatistics, classStatistics } = tuitionStatistics;\r\n\r\n      // Format dữ liệu cho UI\r\n      const formattedData = {\r\n        overview: {\r\n          totalStudents: totalStatistics?.totalStudents || 0,\r\n          paidStudents: totalStatistics?.paidStudents || 0,\r\n          unpaidStudents: totalStatistics?.unpaidStudents || 0,\r\n          paymentRate: totalStatistics?.paymentRate || 0\r\n        },\r\n        monthlyStats: monthlyStatistics?.map(stat => ({\r\n          month: stat.month,\r\n          monthFormatted: stat.monthFormatted,\r\n          total: stat.totalStudents,\r\n          paid: stat.paidStudents,\r\n          unpaid: stat.unpaidStudents,\r\n          paymentRate: stat.paymentRate\r\n        })) || [],\r\n        classStats: classStatistics?.map(stat => ({\r\n          class: stat.userClass,\r\n          total: stat.totalStudents,\r\n          paid: stat.paidStudents,\r\n          unpaid: stat.unpaidStudents,\r\n          paymentRate: stat.paymentRate\r\n        })) || [],\r\n        apiData: tuitionStatistics // Lưu toàn bộ API data\r\n      };\r\n\r\n      setStatisticsData(formattedData);\r\n    } else {\r\n      console.error(\"No tuitionStatistics data available\");\r\n      // Fallback: Sử dụng dữ liệu local nếu API không trả về\r\n      const totalStudents = tuitionPayments.length;\r\n      const paidStudents = tuitionPayments.filter(payment => payment.isPaid).length;\r\n      const unpaidStudents = totalStudents - paidStudents;\r\n      const paymentRate = totalStudents > 0 ? ((paidStudents / totalStudents) * 100).toFixed(1) : 0;\r\n\r\n      setStatisticsData({\r\n        overview: {\r\n          totalStudents,\r\n          paidStudents,\r\n          unpaidStudents,\r\n          paymentRate\r\n        },\r\n        monthlyStats: [],\r\n        classStats: [],\r\n        apiData: null\r\n      });\r\n    }\r\n  }, [tuitionStatistics]);\r\n\r\n  // getStatusBadge function removed - replaced with inline status display\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconStatistics = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <BarChart2 size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterIsPaid}\r\n                onChange={(e) => setFilterIsPaid(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"true\">Đã thanh toán</option>\r\n                <option value=\"false\">Chưa thanh toán</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n            <ButtonFunctionBarAdmin icon={iconStatistics} text={'Xem thống kê'} onClick={handleOpenStatistics} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {tuitionPayments.length === 0 ? (\r\n        <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n          <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n            <thead className=\"bg-gray-100\">\r\n              <tr>\r\n                <th className=\"py-3 px-4 text-left\">STT</th>\r\n                <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                <th className=\"py-3 px-4 text-left\">\r\n                  <div className=\"flex flex-col\">\r\n                    <span className=\"font-medium\">Số điện thoại</span>\r\n                    <span className=\"text-xs text-gray-500\">(Số điện thoại của phụ huynh)</span>\r\n                    <span className=\"text-xs text-gray-500\">(Số điện thoại của học sinh)</span>\r\n                  </div>\r\n                </th>\r\n                <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                <th className=\"py-3 px-4 text-left\">Ghi chú</th>\r\n                <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {tuitionPayments.map((payment, index) => (\r\n                <tr\r\n                  key={payment.id}\r\n                  className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                >\r\n                  <td className=\"py-3 px-4\">\r\n                    {(page - 1) * pageSize + index + 1}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4 align-top\">\r\n                    <div className=\"flex flex-col gap-y-0.5\">\r\n                      <span>{payment.user?.phone || \"N/A\"}</span>\r\n                      <span>{payment.user?.password || \"Không có mật khẩu\"}</span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.paymentDate\r\n                      ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                      : \"Chưa thanh toán\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${payment.isPaid\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : payment.isOverdue\r\n                        ? 'bg-red-100 text-red-800'\r\n                        : 'bg-yellow-100 text-yellow-800'\r\n                      }`}>\r\n                      {payment.isPaid\r\n                        ? 'Đã thanh toán'\r\n                        : payment.isOverdue\r\n                          ? 'Quá hạn'\r\n                          : 'Chưa thanh toán'\r\n                      }\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <span className=\"text-sm text-gray-600\" title={payment.note}>\r\n                      {payment.note ? (payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note) : '-'}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <div className=\"flex space-x-2 items-center\">\r\n                      {/* Quick payment checkbox */}\r\n                      <div className=\"flex items-center cursor-pointer\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={payment.isPaid}\r\n                          onChange={() => handleQuickPayment(payment.id, payment.isPaid)}\r\n                          className=\"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer\"\r\n                          title={payment.isPaid ? \"Bỏ đánh dấu đã thanh toán\" : \"Đánh dấu đã thanh toán\"}\r\n                        />\r\n                      </div>\r\n\r\n                      {/* Action buttons */}\r\n                      <div className=\"flex space-x-1\">\r\n                        <button\r\n                          onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                          className=\"text-blue-500 hover:text-blue-700\"\r\n                          title=\"Xem chi tiết\"\r\n                        >\r\n                          <Eye size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleEdit(payment.id)}\r\n                          className=\"text-yellow-500 hover:text-yellow-700\"\r\n                          title=\"Chỉnh sửa\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(payment.id)}\r\n                          className=\"text-red-500 hover:text-red-700\"\r\n                          title=\"Xóa\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={page}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={total}\r\n            limit={pageSize}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n      {/* Statistics Modal */}\r\n      {showStatistics && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6 border-b border-gray-200\">\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h2 className=\"text-xl font-semibold text-gray-800\">Thống kê thanh toán học phí</h2>\r\n                <button\r\n                  onClick={() => setShowStatistics(false)}\r\n                  className=\"text-gray-500 hover:text-gray-700\"\r\n                >\r\n                  <X size={24} />\r\n                </button>\r\n              </div>\r\n\r\n              {/* Statistics Filter Controls */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Từ tháng\r\n                  </label>\r\n                  <input\r\n                    type=\"month\"\r\n                    value={statisticsStartMonth}\r\n                    onChange={(e) => setStatisticsStartMonth(e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Đến tháng\r\n                  </label>\r\n                  <input\r\n                    type=\"month\"\r\n                    value={statisticsEndMonth}\r\n                    onChange={(e) => setStatisticsEndMonth(e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Lớp học\r\n                  </label>\r\n\r\n                  <select\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                    value={statisticsClass}\r\n                    onChange={(e) => setStatisticsClass(e.target.value)}\r\n                  >\r\n                    <option value=\"\">Tất cả</option>\r\n                    <option value=\"10\">Khối 10</option>\r\n                    <option value=\"11\">Khối 11</option>\r\n                    <option value=\"12\">Khối 12</option>\r\n                  </select>\r\n                </div>\r\n\r\n\r\n                <div className=\"flex items-end\">\r\n                  <button\r\n                    onClick={handleShowStatistics}\r\n                    disabled={statisticsLoading}\r\n                    className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    {statisticsLoading ? \"Đang tải...\" : \"Cập nhật\"}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {statisticsLoading ? (\r\n              <div className=\"flex justify-center items-center h-64\">\r\n                <LoadingSpinner />\r\n              </div>\r\n            ) : statisticsData ? (\r\n              <div className=\"p-6\">\r\n                {/* Overview Statistics */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\r\n                  <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <Users className=\"h-8 w-8 text-blue-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-blue-600\">Tổng học sinh</p>\r\n                        <p className=\"text-2xl font-bold text-blue-900\">{statisticsData.overview.totalStudents}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <DollarSign className=\"h-8 w-8 text-green-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-green-600\">Đã thanh toán</p>\r\n                        <p className=\"text-2xl font-bold text-green-900\">{statisticsData.overview.paidStudents}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-red-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <AlertCircle className=\"h-8 w-8 text-red-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-red-600\">Chưa thanh toán</p>\r\n                        <p className=\"text-2xl font-bold text-red-900\">{statisticsData.overview.unpaidStudents}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"bg-purple-50 p-4 rounded-lg\">\r\n                    <div className=\"flex items-center\">\r\n                      <TrendingUp className=\"h-8 w-8 text-purple-600\" />\r\n                      <div className=\"ml-3\">\r\n                        <p className=\"text-sm font-medium text-purple-600\">Tỷ lệ thanh toán</p>\r\n                        <p className=\"text-2xl font-bold text-purple-900\">{statisticsData.overview.paymentRate}%</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Monthly Statistics */}\r\n                {statisticsData.monthlyStats.length > 0 && (\r\n                  <div className=\"mb-8\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Thống kê theo tháng</h3>\r\n                    <div className=\"overflow-x-auto\">\r\n                      <table className=\"min-w-full bg-white border border-gray-200 rounded-lg\">\r\n                        <thead className=\"bg-gray-50\">\r\n                          <tr>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tháng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tổng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Đã đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Chưa đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tỷ lệ</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody className=\"divide-y divide-gray-200\">\r\n                          {statisticsData.monthlyStats.map((stat, index) => (\r\n                            <tr key={stat.month} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.monthFormatted}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.total}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-green-600 font-medium\">{stat.paid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-red-600 font-medium\">{stat.unpaid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">\r\n                                {stat.paymentRate}%\r\n                              </td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Class Statistics */}\r\n                {statisticsData.classStats.length > 0 && (\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Thống kê theo lớp</h3>\r\n                    <div className=\"overflow-x-auto\">\r\n                      <table className=\"min-w-full bg-white border border-gray-200 rounded-lg\">\r\n                        <thead className=\"bg-gray-50\">\r\n                          <tr>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Lớp</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tổng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Đã đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Chưa đóng</th>\r\n                            <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Tỷ lệ</th>\r\n                          </tr>\r\n                        </thead>\r\n                        <tbody className=\"divide-y divide-gray-200\">\r\n                          {statisticsData.classStats.map((stat, index) => (\r\n                            <tr key={stat.class} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.class}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">{stat.total}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-green-600 font-medium\">{stat.paid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-red-600 font-medium\">{stat.unpaid}</td>\r\n                              <td className=\"px-4 py-3 text-sm text-gray-900\">\r\n                                {stat.paymentRate}%\r\n                              </td>\r\n                            </tr>\r\n                          ))}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"p-6 text-center text-gray-500\">\r\n                Không có dữ liệu thống kê\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  {/* Removed expectedAmount and paidAmount fields - no longer needed */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái thanh toán</label>\r\n                    <div className=\"flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"addIsPaid\"\r\n                        checked={addIsPaid}\r\n                        onChange={(e) => setAddIsPaid(e.target.checked)}\r\n                        className=\"mr-2\"\r\n                      />\r\n                      <label htmlFor=\"addIsPaid\" className=\"text-sm text-gray-700\">\r\n                        Đã thanh toán\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editDueDate}\r\n                      onChange={(e) => setEditDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.editDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái thanh toán</label>\r\n                    <div className=\"flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"editIsPaid\"\r\n                        checked={editIsPaid}\r\n                        onChange={(e) => setEditIsPaid(e.target.checked)}\r\n                        className=\"mr-2\"\r\n                      />\r\n                      <label htmlFor=\"editIsPaid\" className=\"text-sm text-gray-700\">\r\n                        Đã thanh toán\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,QACvB,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,QAAQ,cAAc;AACjJ,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjK,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAGtD,WAAW,CAAC,CAAC;EAC9B,MAAMuD,QAAQ,GAAGrD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsD,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAG3D,WAAW,CAAE4D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,IAAI;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGjE,WAAW,CACtD4D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACK,UAC3B,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC5D;EACA,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACwF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM;IAAE4F,WAAW;IAAEC,YAAY;IAAEC,aAAa;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAG7F,WAAW,CACzF4D,KAAK,IAAKA,KAAK,CAACC,OACnB,CAAC;EAED,MAAMiC,cAAc,GAAIC,KAAK,IAAK;IAChC1C,QAAQ,CAACb,mBAAmB,CAACuD,KAAK,CAAC,CAAC;EACtC,CAAC;EAED,MAAMC,eAAe,GAAIC,MAAM,IAAK;IAClC5C,QAAQ,CAACZ,oBAAoB,CAACwD,MAAM,CAAC,CAAC;EACxC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC9C,QAAQ,CAACX,qBAAqB,CAACyD,OAAO,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrChD,QAAQ,CAACV,mBAAmB,CAAC0D,UAAU,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpClD,QAAQ,CAACd,qBAAqB,CAACgE,OAAO,CAAC,CAAC;EAC1C,CAAC;EAGD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC6G,cAAc,EAAEC,iBAAiB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+G,cAAc,EAAEC,iBAAiB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAACiH,QAAQ,EAAEC,WAAW,CAAC,GAAGlH,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACmH,UAAU,EAAEC,aAAa,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqH,QAAQ,EAAEC,WAAW,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMuH,eAAe,GAAGtH,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMuH,aAAa,GAAGvH,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMwH,oBAAoB,GAAGxH,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMyH,kBAAkB,GAAGzH,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAAC0H,UAAU,EAAEC,aAAa,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6H,WAAW,EAAEC,cAAc,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmI,UAAU,EAAEC,aAAa,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqI,SAAS,EAAEC,YAAY,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuI,UAAU,EAAEC,aAAa,CAAC,GAAGxI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyI,YAAY,EAAEC,eAAe,CAAC,GAAG1I,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC2I,SAAS,EAAEC,YAAY,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+I,QAAQ,EAAEC,WAAW,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmJ,UAAU,EAAEC,aAAa,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqJ,SAAS,EAAEC,YAAY,CAAC,GAAGtJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACuJ,OAAO,EAAEC,UAAU,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACyJ,MAAM,EAAEC,SAAS,CAAC,GAAG1J,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC2J,eAAe,EAAEC,kBAAkB,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6J,WAAW,EAAEC,cAAc,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+J,UAAU,EAAEC,aAAa,CAAC,GAAGhK,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACiK,QAAQ,EAAEC,WAAW,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM,CAACmK,WAAW,EAAEC,cAAc,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtK,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACuK,WAAW,EAAEC,cAAc,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyK,aAAa,EAAEC,gBAAgB,CAAC,GAAG1K,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM2K,mBAAmB,GAAG1K,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA;EACA;EACA;EACA;EACA;;EAEAF,SAAS,CAAC,MAAM;IACdyD,QAAQ,CAACpC,WAAW,CAAC,EAAE,CAAC,CAAC;IACzBoC,QAAQ,CAACrC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAACqC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoH,iBAAiB,GAAIC,SAAS,IAAK;IACvCpE,gBAAgB,CAACoE,SAAS,CAACC,EAAE,CAAC;IAC9BlE,kBAAkB,CAACiE,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCvE,gBAAgB,CAAC,EAAE,CAAC;IACpBG,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAIC,QAAQ,IAAK;IACrCpE,iBAAiB,CAACoE,QAAQ,CAACJ,EAAE,CAAC;IAC9B9D,iBAAiB,CAACkE,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCvE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzB9H,QAAQ,CACN/C,oBAAoB,CAAC;MACnB8K,MAAM,EAAEjH,UAAU;MAClBL,IAAI,EAAE,CAAC;MAAE;MACTG,QAAQ;MACRoH,SAAS,EAAE,MAAM;MACjBpF,MAAM,EAAEP,YAAY;MAAE;MACtBK,KAAK,EAAEN,WAAW;MAClBU,OAAO,EAAER,aAAa;MACtB2F,SAAS,EAAE1F,WAAW;MACtBW,OAAO,EAAEV;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAM0F,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAClD,SAAS,EAAE;MACdkD,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAAC/C,QAAQ,EAAE;MACb8C,MAAM,CAAC3F,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC6F,IAAI,CAAChD,QAAQ,CAAC,EAAE;MAC1C8C,MAAM,CAAC3F,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAACiD,UAAU,EAAE;MACf0C,MAAM,CAACG,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAIC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM0D,WAAW,GAAG;QAClBN,MAAM,EAAEnD,SAAS;QACjBzC,KAAK,EAAE6C,QAAQ;QACf3C,MAAM,EAAEiD,SAAS;QAAE;QACnBgD,IAAI,EAAE9C,OAAO;QACbyC,OAAO,EAAE7C;MACX,CAAC;;MAED;MACA,IAAIF,cAAc,EAAE;QAClBmD,WAAW,CAACE,WAAW,GAAGrD,cAAc;MAC1C;;MAEA;MACA,MAAMzF,QAAQ,CAAC7C,oBAAoB,CAACyL,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjB/I,QAAQ,CAAC/C,oBAAoB,CAAC;QAC5BwD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRmH,MAAM,EAAEjH,UAAU;QAClBkH,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA5C,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,KAAK,CAAC;MACnBE,UAAU,CAAC,EAAE,CAAC;IAEhB,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED3I,SAAS,CAAC,MAAM;IACdyD,QAAQ,CACN/C,oBAAoB,CAAC;MACnB8K,MAAM,EAAEjH,UAAU;MAClBL,IAAI,EAAEA,IAAI;MAAE;MACZG,QAAQ;MACRoH,SAAS,EAAE,MAAM;MACjBpF,MAAM,EAAEP,YAAY;MAAE;MACtBK,KAAK,EAAEN,WAAW;MAClBU,OAAO,EAAER,aAAa;MACtB2F,SAAS,EAAE1F;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAACvC,QAAQ,EAAES,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAE9B,MAAMuI,UAAU,GAAG,MAAO7B,EAAE,IAAK;IAC/BpC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMkE,QAAQ,GAAG,MAAMpJ,QAAQ,CAACzC,4BAA4B,CAAC+J,EAAE,CAAC,CAAC,CAAC+B,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAACG,IAAI;;MAE7B;MACArD,SAAS,CAACoB,EAAE,CAAC;MACblB,kBAAkB,CAACkD,OAAO,CAACR,WAAW,GAAG,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACW,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxGpD,cAAc,CAACgD,OAAO,CAACd,OAAO,GAAG,IAAIgB,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MAC5FlD,aAAa,CAAC8C,OAAO,CAAC1G,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC;MACxC8D,WAAW,CAAC4C,OAAO,CAACT,IAAI,IAAI,EAAE,CAAC;;MAE/B;MACAtH,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAO2H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACR9D,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMyE,UAAU,GAAG,MAAAA,CAAOrC,EAAE,EAAEgB,MAAM,EAAE5F,KAAK,KAAK;IAC9CsE,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAMhH,QAAQ,CAACzC,4BAA4B,CAAC+J,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACA/F,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAO2H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRhC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM4C,SAAS,GAAGA,CAAA,KAAM;IACtB;IACAxE,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IACdhB,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAM6E,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;IACxB,MAAMM,aAAa,GAAGD,KAAK,CAACJ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD9D,aAAa,CAACkE,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;IAChC,MAAMtH,KAAK,GAAGmH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAC,MAAA,CAAMJ,IAAI,OAAAI,MAAA,CAAIzH,KAAK,GAAG,EAAE,OAAAyH,MAAA,CAAOzH,KAAK,IAAKA,KAAK,CAAE;IACpE8C,WAAW,CAAC0E,cAAc,CAAC;;IAE3B;IACA3I,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+I,cAAc,GAAGA,CAAA,KAAM;IAC3B7I,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMgJ,wBAAwB,GAAGA,CAAA,KAAM;IACrC9I,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EACD,MAAM0H,eAAe,GAAGA,CAAA,KAAM;IAC5B1H,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACA6C,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBoB,SAAS,CAAC,IAAI,CAAC;IACfE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtBE,WAAW,CAAC,EAAE,CAAC;IACf;IACAE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1B9B,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtB8B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMjC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAChC,WAAW,EAAE;MAChBgC,MAAM,CAAChC,WAAW,GAAG,oCAAoC;IAC3D;IAEA,OAAOgC,MAAM;EACf,CAAC;;EAED;EACA,MAAMkC,0BAA0B,GAAG,MAAOpC,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAGiC,gBAAgB,CAAC,CAAC;IACjC,IAAI7B,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM0D,WAAW,GAAG;QAClBhG,MAAM,EAAE2D,UAAU;QAAE;QACpBsC,IAAI,EAAEpC,QAAQ;QACd+B,OAAO,EAAEnC;MACX,CAAC;;MAED;MACA,IAAIF,eAAe,EAAE;QACnByC,WAAW,CAACE,WAAW,GAAG3C,eAAe;MAC3C;;MAEA;MACA,MAAMnG,QAAQ,CAAC1C,oBAAoB,CAAC;QAClCgK,EAAE,EAAErB,MAAM;QACV2C;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjB/I,QAAQ,CAAC/C,oBAAoB,CAAC;QAC5BwD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRmH,MAAM,EAAEjH,UAAU;QAClBkH,SAAS,EAAE,MAAM;QACjBpF,MAAM,EAAEP,YAAY;QAAE;QACtBK,KAAK,EAAEN,WAAW;QAClBU,OAAO,EAAER,aAAa;QACtB2F,SAAS,EAAE1F;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOyG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMnC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAClE,UAAU,EAAE;MACfkE,MAAM,CAAClE,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAACoE,IAAI,CAACpE,UAAU,CAAC,EAAE;MAC5CkE,MAAM,CAAClE,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAKoG,KAAK,CAACpG,WAAW,CAAC,IAAIqG,MAAM,CAACrG,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEgE,MAAM,CAAChE,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACf0D,MAAM,CAAC1D,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjB4D,MAAM,CAAC5D,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAO4D,MAAM;EACf,CAAC;;EAED;EACA,MAAMsC,wBAAwB,GAAG,MAAOxC,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMwC,iBAAiB,GAAGrG,oBAAoB,GAAG7G,kBAAkB,CAAC6G,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACsG,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAMvC,MAAM,GAAGmC,wBAAwB,CAAC,CAAC;IACzC,IAAI/B,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM2F,SAAS,GAAG;QAChBnI,KAAK,EAAEyB,UAAU;QACjBqE,OAAO,EAAE/D,YAAY;QACrBE,UAAU;QACVkE,IAAI,EAAEhE;MACR,CAAC;;MAED;MACA,IAAI+F,iBAAiB,EAAE;QACrBC,SAAS,CAACC,cAAc,GAAGJ,MAAM,CAACE,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAM5K,QAAQ,CAAC5C,0BAA0B,CAACyN,SAAS,CAAC,CAAC;;MAErD;MACA9B,eAAe,CAAC,CAAC;MACjB/I,QAAQ,CAAC/C,oBAAoB,CAAC;QAC5BwD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRmH,MAAM,EAAEjH,UAAU;QAClBkH,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM6F,YAAY,GAAIzD,EAAE,IAAK;IAC3BnG,kBAAkB,CAACmG,EAAE,CAAC;IACtBrG,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM+J,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChChL,QAAQ,CAAC9C,oBAAoB,CAACgE,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMgK,YAAY,GAAGA,CAAA,KAAM;IACzBhK,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM+J,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,aAAa,KAAK;IAC7D,IAAI;MACF,MAAMvB,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtD,MAAMd,WAAW,GAAG;QAClBhG,MAAM,EAAE,CAACwI,aAAa;QAAE;QACxBtC,WAAW,EAAE,CAACsC,aAAa,GAAGvB,KAAK,GAAG,IAAI,CAAE;MAC9C,CAAC;MAED,MAAM7J,QAAQ,CAAC1C,oBAAoB,CAAC;QAClCgK,EAAE,EAAE6D,SAAS;QACbvC;MACF,CAAC,CAAC,CAAC;;MAEH;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI1H,UAAU,GAAG7B,oBAAoB;IACrC,IAAI+B,QAAQ,GAAG7B,kBAAkB;IAEjC,IAAI,CAAC2B,UAAU,IAAI,CAACE,QAAQ,EAAE;MAC5B,MAAMyH,WAAW,GAAG,IAAI9B,IAAI,CAAC,CAAC;MAC9B,MAAM+B,YAAY,GAAGD,WAAW,CAAC7B,WAAW,CAAC,CAAC,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5D3H,QAAQ,GAAG0H,YAAY;MACvBtJ,qBAAqB,CAACsJ,YAAY,CAAC;;MAEnC;MACA,MAAME,cAAc,GAAG,IAAIjC,IAAI,CAAC8B,WAAW,CAAC;MAC5CG,cAAc,CAACC,QAAQ,CAACJ,WAAW,CAACrB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACnDtG,UAAU,GAAG8H,cAAc,CAAChC,WAAW,CAAC,CAAC,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACrDzJ,uBAAuB,CAAC4B,UAAU,CAAC;IACrC;;IAEA;IACA9B,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAM8J,MAAM,GAAG;QACb1D,SAAS,EAAE/F,eAAe,IAAI0J,SAAS;QACvCjI,UAAU,EAAEA,UAAU,IAAIiI,SAAS;QACnC/H,QAAQ,EAAEA,QAAQ,IAAI+H;MACxB,CAAC;MAED,MAAM5L,QAAQ,CAAC3C,sBAAsB,CAACsO,MAAM,CAAC,CAAC;;MAE9C;MACA,IAAIxL,iBAAiB,EAAE;QACrB,MAAM;UAAE0L,eAAe;UAAEC,iBAAiB;UAAEC;QAAgB,CAAC,GAAG5L,iBAAiB;QAEjF,MAAM6L,aAAa,GAAG;UACpBC,QAAQ,EAAE;YACRC,aAAa,EAAE,CAAAL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,aAAa,KAAI,CAAC;YAClDC,YAAY,EAAE,CAAAN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,YAAY,KAAI,CAAC;YAChDC,cAAc,EAAE,CAAAP,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,cAAc,KAAI,CAAC;YACpDC,WAAW,EAAE,CAAAR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEQ,WAAW,KAAI;UAC/C,CAAC;UACDC,YAAY,EAAE,CAAAR,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAES,GAAG,CAACC,IAAI,KAAK;YAC5C9J,KAAK,EAAE8J,IAAI,CAAC9J,KAAK;YACjB+J,cAAc,EAAED,IAAI,CAACC,cAAc;YACnC9L,KAAK,EAAE6L,IAAI,CAACN,aAAa;YACzBQ,IAAI,EAAEF,IAAI,CAACL,YAAY;YACvBQ,MAAM,EAAEH,IAAI,CAACJ,cAAc;YAC3BC,WAAW,EAAEG,IAAI,CAACH;UACpB,CAAC,CAAC,CAAC,KAAI,EAAE;UACTO,UAAU,EAAE,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEQ,GAAG,CAACC,IAAI,KAAK;YACxCK,KAAK,EAAEL,IAAI,CAACvE,SAAS;YACrBtH,KAAK,EAAE6L,IAAI,CAACN,aAAa;YACzBQ,IAAI,EAAEF,IAAI,CAACL,YAAY;YACvBQ,MAAM,EAAEH,IAAI,CAACJ,cAAc;YAC3BC,WAAW,EAAEG,IAAI,CAACH;UACpB,CAAC,CAAC,CAAC,KAAI,EAAE;UACTS,OAAO,EAAE3M;QACX,CAAC;QAEDwB,iBAAiB,CAACqK,aAAa,CAAC;MAClC;IACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRnH,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMkL,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvClL,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF;MACA,MAAM8J,MAAM,GAAG;QACb1D,SAAS,EAAE/F,eAAe,IAAI0J,SAAS;QACvCjI,UAAU,EAAE7B,oBAAoB,IAAI8J,SAAS;QAC7C/H,QAAQ,EAAE7B,kBAAkB,IAAI4J;MAClC,CAAC;MAED,MAAM5L,QAAQ,CAAC3C,sBAAsB,CAACsO,MAAM,CAAC,CAAC;;MAE9C;IACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRnH,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAEDtF,SAAS,CAAC,MAAM;IACd,IAAI4D,iBAAiB,EAAE;MACrB,MAAM;QAAE0L,eAAe;QAAEC,iBAAiB;QAAEC;MAAgB,CAAC,GAAG5L,iBAAiB;;MAEjF;MACA,MAAM6L,aAAa,GAAG;QACpBC,QAAQ,EAAE;UACRC,aAAa,EAAE,CAAAL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,aAAa,KAAI,CAAC;UAClDC,YAAY,EAAE,CAAAN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,YAAY,KAAI,CAAC;UAChDC,cAAc,EAAE,CAAAP,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,cAAc,KAAI,CAAC;UACpDC,WAAW,EAAE,CAAAR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEQ,WAAW,KAAI;QAC/C,CAAC;QACDC,YAAY,EAAE,CAAAR,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAES,GAAG,CAACC,IAAI,KAAK;UAC5C9J,KAAK,EAAE8J,IAAI,CAAC9J,KAAK;UACjB+J,cAAc,EAAED,IAAI,CAACC,cAAc;UACnC9L,KAAK,EAAE6L,IAAI,CAACN,aAAa;UACzBQ,IAAI,EAAEF,IAAI,CAACL,YAAY;UACvBQ,MAAM,EAAEH,IAAI,CAACJ,cAAc;UAC3BC,WAAW,EAAEG,IAAI,CAACH;QACpB,CAAC,CAAC,CAAC,KAAI,EAAE;QACTO,UAAU,EAAE,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEQ,GAAG,CAACC,IAAI,KAAK;UACxCK,KAAK,EAAEL,IAAI,CAACvE,SAAS;UACrBtH,KAAK,EAAE6L,IAAI,CAACN,aAAa;UACzBQ,IAAI,EAAEF,IAAI,CAACL,YAAY;UACvBQ,MAAM,EAAEH,IAAI,CAACJ,cAAc;UAC3BC,WAAW,EAAEG,IAAI,CAACH;QACpB,CAAC,CAAC,CAAC,KAAI,EAAE;QACTS,OAAO,EAAE3M,iBAAiB,CAAC;MAC7B,CAAC;MAEDwB,iBAAiB,CAACqK,aAAa,CAAC;IAClC,CAAC,MAAM;MACL/C,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAC;MACpD;MACA,MAAMkD,aAAa,GAAGhM,eAAe,CAACyI,MAAM;MAC5C,MAAMwD,YAAY,GAAGjM,eAAe,CAAC8M,MAAM,CAAC1D,OAAO,IAAIA,OAAO,CAAC1G,MAAM,CAAC,CAAC+F,MAAM;MAC7E,MAAMyD,cAAc,GAAGF,aAAa,GAAGC,YAAY;MACnD,MAAME,WAAW,GAAGH,aAAa,GAAG,CAAC,GAAG,CAAEC,YAAY,GAAGD,aAAa,GAAI,GAAG,EAAEe,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;MAE7FtL,iBAAiB,CAAC;QAChBsK,QAAQ,EAAE;UACRC,aAAa;UACbC,YAAY;UACZC,cAAc;UACdC;QACF,CAAC;QACDC,YAAY,EAAE,EAAE;QAChBM,UAAU,EAAE,EAAE;QACdE,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3M,iBAAiB,CAAC,CAAC;;EAEvB;;EAEA,IAAIC,OAAO,EAAE;IACX,oBAAOZ,OAAA,CAAC1B,cAAc;MAAAoP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACE/N,OAAA;MACEkO,OAAO,EAAEA,OAAQ;MACjBC,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJJ,IAAI,eACLhO,OAAA;QAAAoO,QAAA,EAAOH;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMQ,OAAO,gBACXrO,OAAA;IAAK,wBAAgB;IAACmO,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCpO,OAAA,CAACrB,IAAI;MAAC2P,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMU,SAAS,gBACbvO,OAAA;IAAK,wBAAgB;IAACmO,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCpO,OAAA,CAACnB,QAAQ;MAACyP,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMW,YAAY,gBAChBxO,OAAA;IAAK,wBAAgB;IAACmO,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCpO,OAAA,CAAClB,QAAQ;MAACwP,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMY,SAAS,gBACbzO,OAAA;IAAK,wBAAgB;IAACmO,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCpO,OAAA,CAACjB,KAAK;MAACuP,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,MAAMa,cAAc,gBAClB1O,OAAA;IAAK,wBAAgB;IAACmO,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCpO,OAAA,CAACd,SAAS;MAACoP,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CACN;EAED,oBACE7N,OAAA,CAACT,WAAW;IAAA6O,QAAA,gBACVpO,OAAA;MAAKmO,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAEN7N,OAAA;MAAKmO,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFpO,OAAA;QAAKmO,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBpO,OAAA;UAAKmO,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDpO,OAAA;YAAKmO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCpO,OAAA;cACE2O,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXZ,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnEpO,OAAA;gBACEgP,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7N,OAAA;cACEoP,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kEAAwC;cACpDC,KAAK,EAAEhO,UAAW;cAClBiO,QAAQ,EAAG5G,CAAC,IAAKpH,aAAa,CAACoH,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;cAC/CnB,SAAS,EAAC;YAAsI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN7N,OAAA;YAAKmO,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpO,OAAA;cACEmO,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAE1M,WAAY;cACnB2M,QAAQ,EAAG5G,CAAC,IAAK1F,cAAc,CAAC0F,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAEhDpO,OAAA;gBAAQsP,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9B4B,KAAK,CAACC,IAAI,CAAC;gBAAEvG,MAAM,EAAE;cAAG,CAAC,EAAE,CAACwG,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAM1M,KAAK,GAAG0M,CAAC,GAAG,CAAC;gBACnB,MAAMrF,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;gBACrC,MAAMqF,QAAQ,GAAG3M,KAAK,GAAG,EAAE,OAAAyH,MAAA,CAAOzH,KAAK,OAAAyH,MAAA,CAAQzH,KAAK,CAAE;gBACtD,oBACElD,OAAA;kBAAoBsP,KAAK,KAAA3E,MAAA,CAAKJ,IAAI,OAAAI,MAAA,CAAIkF,QAAQ,CAAG;kBAAAzB,QAAA,cAAAzD,MAAA,CACrCzH,KAAK,OAAAyH,MAAA,CAAIJ,IAAI;gBAAA,GADZrH,KAAK;kBAAAwK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7N,OAAA;YAAKmO,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpO,OAAA;cACEmO,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAEzM,YAAa;cACpB0M,QAAQ,EAAG5G,CAAC,IAAKxF,eAAe,CAACwF,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAEjDpO,OAAA;gBAAQsP,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC7N,OAAA;gBAAQsP,KAAK,EAAC,MAAM;gBAAAlB,QAAA,EAAC;cAAa;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C7N,OAAA;gBAAQsP,KAAK,EAAC,OAAO;gBAAAlB,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7N,OAAA;YAAKmO,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpO,OAAA;cACEmO,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAExM,aAAc;cACrByM,QAAQ,EAAG5G,CAAC,IAAKtF,gBAAgB,CAACsF,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAElDpO,OAAA;gBAAQsP,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC7N,OAAA;gBAAQsP,KAAK,EAAC,MAAM;gBAAAlB,QAAA,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC7N,OAAA;gBAAQsP,KAAK,EAAC,OAAO;gBAAAlB,QAAA,EAAC;cAAY;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7N,OAAA;YAAKmO,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpO,OAAA;cACEmO,SAAS,EAAC,oDAAoD;cAC9DmB,KAAK,EAAEvM,WAAY;cACnBwM,QAAQ,EAAG5G,CAAC,IAAKpF,cAAc,CAACoF,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;cAAAlB,QAAA,gBAEhDpO,OAAA;gBAAQsP,KAAK,EAAC,EAAE;gBAAAlB,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B7N,OAAA;gBAAQsP,KAAK,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC7N,OAAA;gBAAQsP,KAAK,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC7N,OAAA;gBAAQsP,KAAK,EAAC,IAAI;gBAAAlB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7N,OAAA;YAAKmO,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpO,OAAA,CAACzC,gBAAgB;cACf+R,KAAK,EAAE3L,eAAgB;cACvBmM,eAAe,EAAE9M,aAAc;cAC/BuM,QAAQ,EAAE3L,kBAAmB;cAC7BmM,QAAQ,EAAEnI,iBAAkB;cAC5BoI,OAAO,EAAEhI,yBAA0B;cACnCqH,WAAW,EAAC;YAAqB;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7N,OAAA;YACEkO,OAAO,EAAE5F,YAAa;YACtB6F,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3FpO,OAAA;cAAKmO,SAAS,EAAC,cAAc;cAACY,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAP,QAAA,eACpHpO,OAAA;gBAAMkP,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACc,WAAW,EAAC,GAAG;gBAACjB,CAAC,EAAC;cAA6C;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CAAC,eAEN7N,OAAA;UAAKmO,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpO,OAAA,CAAC8N,sBAAsB;YAACE,IAAI,EAAEK,OAAQ;YAACJ,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAE9D;UAAU;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtF7N,OAAA,CAAC8N,sBAAsB;YAACE,IAAI,EAAEQ,YAAa;YAACP,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAErD;UAAyB;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChH7N,OAAA,CAAC8N,sBAAsB;YAACE,IAAI,EAAEU,cAAe;YAACT,IAAI,EAAE,cAAe;YAACC,OAAO,EAAErC;UAAqB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELnN,eAAe,CAACyI,MAAM,KAAK,CAAC,gBAC3BnJ,OAAA;MAAKmO,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DpO,OAAA;QAAGmO,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,gBAEN7N,OAAA;MAAKmO,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BpO,OAAA;QAAOmO,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzEpO,OAAA;UAAOmO,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5BpO,OAAA;YAAAoO,QAAA,gBACEpO,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eACjCpO,OAAA;gBAAKmO,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BpO,OAAA;kBAAMmO,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClD7N,OAAA;kBAAMmO,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA6B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5E7N,OAAA;kBAAMmO,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD7N,OAAA;cAAImO,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR7N,OAAA;UAAAoO,QAAA,EACG1N,eAAe,CAACqM,GAAG,CAAC,CAACjD,OAAO,EAAEoG,KAAK;YAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClCxQ,OAAA;cAEEmO,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAA9B,QAAA,gBAEvDpO,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAACnN,IAAI,GAAG,CAAC,IAAIG,QAAQ,GAAG8O,KAAK,GAAG;cAAC;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAA+B,aAAA,GAAArG,OAAO,CAAC2G,IAAI,cAAAN,aAAA,uBAAZA,aAAA,CAAc/H,QAAQ,IAAG,GAAG,KAAAgI,cAAA,GAAGtG,OAAO,CAAC2G,IAAI,cAAAL,cAAA,uBAAZA,cAAA,CAAcjI,SAAS,KAAI;cAAK;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAiC,cAAA,GAAAvG,OAAO,CAAC2G,IAAI,cAAAJ,cAAA,uBAAZA,cAAA,CAAchD,KAAK,KAAI;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAkC,cAAA,GAAAxG,OAAO,CAAC2G,IAAI,cAAAH,cAAA,uBAAZA,cAAA,CAAcI,UAAU,KAAI;cAAK;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClE7N,OAAA;gBAAImO,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eACjCpO,OAAA;kBAAKmO,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCpO,OAAA;oBAAAoO,QAAA,EAAO,EAAAmC,cAAA,GAAAzG,OAAO,CAAC2G,IAAI,cAAAF,cAAA,uBAAZA,cAAA,CAAcI,KAAK,KAAI;kBAAK;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C7N,OAAA;oBAAAoO,QAAA,EAAO,EAAAoC,cAAA,GAAA1G,OAAO,CAAC2G,IAAI,cAAAD,cAAA,uBAAZA,cAAA,CAAcI,QAAQ,KAAI;kBAAmB;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEtE,OAAO,CAACmD;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvD7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBtE,OAAO,CAACR,WAAW,GAChB,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACuH,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAIpE,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAAC6H,kBAAkB,CAAC,OAAO;cAAC;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBpO,OAAA;kBAAMmO,SAAS,gDAAAxD,MAAA,CAAgDb,OAAO,CAAC1G,MAAM,GACzE,6BAA6B,GAC7B0G,OAAO,CAACgH,SAAS,GACf,yBAAyB,GACzB,+BAA+B,CAChC;kBAAA1C,QAAA,EACFtE,OAAO,CAAC1G,MAAM,GACX,eAAe,GACf0G,OAAO,CAACgH,SAAS,GACf,SAAS,GACT;gBAAiB;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBpO,OAAA;kBAAMmO,SAAS,EAAC,uBAAuB;kBAAC4C,KAAK,EAAEjH,OAAO,CAACT,IAAK;kBAAA+E,QAAA,EACzDtE,OAAO,CAACT,IAAI,GAAIS,OAAO,CAACT,IAAI,CAACF,MAAM,GAAG,EAAE,GAAGW,OAAO,CAACT,IAAI,CAAC2H,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGlH,OAAO,CAACT,IAAI,GAAI;gBAAG;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7N,OAAA;gBAAImO,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBpO,OAAA;kBAAKmO,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAE1CpO,OAAA;oBAAKmO,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,eAC/CpO,OAAA;sBACEoP,IAAI,EAAC,UAAU;sBACf6B,OAAO,EAAEnH,OAAO,CAAC1G,MAAO;sBACxBmM,QAAQ,EAAEA,CAAA,KAAM7D,kBAAkB,CAAC5B,OAAO,CAAChC,EAAE,EAAEgC,OAAO,CAAC1G,MAAM,CAAE;sBAC/D+K,SAAS,EAAC,6GAA6G;sBACvH4C,KAAK,EAAEjH,OAAO,CAAC1G,MAAM,GAAG,2BAA2B,GAAG;oBAAyB;sBAAAsK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAGN7N,OAAA;oBAAKmO,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BpO,OAAA;sBACEkO,OAAO,EAAEA,CAAA,KAAM/D,UAAU,CAACL,OAAO,CAAChC,EAAE,EAAEgC,OAAO,CAAChB,MAAM,EAAEgB,OAAO,CAAC5G,KAAK,CAAE;sBACrEiL,SAAS,EAAC,mCAAmC;sBAC7C4C,KAAK,EAAC,mBAAc;sBAAA3C,QAAA,eAEpBpO,OAAA,CAACpB,GAAG;wBAAC0P,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACT7N,OAAA;sBACEkO,OAAO,EAAEA,CAAA,KAAMvE,UAAU,CAACG,OAAO,CAAChC,EAAE,CAAE;sBACtCqG,SAAS,EAAC,uCAAuC;sBACjD4C,KAAK,EAAC,qBAAW;sBAAA3C,QAAA,eAEjBpO,OAAA,CAACvB,IAAI;wBAAC6P,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACT7N,OAAA;sBACEkO,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAACzB,OAAO,CAAChC,EAAE,CAAE;sBACxCqG,SAAS,EAAC,iCAAiC;sBAC3C4C,KAAK,EAAC,QAAK;sBAAA3C,QAAA,eAEXpO,OAAA,CAACtB,KAAK;wBAAC4P,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GApFA/D,OAAO,CAAChC,EAAE;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqFb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACA5J,QAAQ,KAAK,OAAO,iBACnBjE,OAAA;MAAKmO,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBpO,OAAA,CAACzB,UAAU;QACT2S,WAAW,EAAEjQ,IAAK;QAClBkQ,YAAY,EAAGlQ,IAAI,IAAKT,QAAQ,CAACnC,cAAc,CAAC4C,IAAI,CAAC,CAAE;QACvDmQ,UAAU,EAAEjQ,KAAM;QAClBkQ,KAAK,EAAEjQ;MAAS;QAAAsM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAGA7L,cAAc,iBACbhC,OAAA;MAAKmO,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFpO,OAAA;QAAKmO,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAC/FpO,OAAA;UAAKmO,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CpO,OAAA;YAAKmO,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDpO,OAAA;cAAImO,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAA2B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF7N,OAAA;cACEkO,OAAO,EAAEA,CAAA,KAAMjM,iBAAiB,CAAC,KAAK,CAAE;cACxCkM,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAE7CpO,OAAA,CAAChB,CAAC;gBAACsP,IAAI,EAAE;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7N,OAAA;YAAKmO,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDpO,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7N,OAAA;gBACEoP,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAEhN,oBAAqB;gBAC5BiN,QAAQ,EAAG5G,CAAC,IAAKpG,uBAAuB,CAACoG,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;gBACzDnB,SAAS,EAAC;cAAwG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7N,OAAA;gBACEoP,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAE9M,kBAAmB;gBAC1B+M,QAAQ,EAAG5G,CAAC,IAAKlG,qBAAqB,CAACkG,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;gBACvDnB,SAAS,EAAC;cAAwG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAER7N,OAAA;gBACEmO,SAAS,EAAC,wGAAwG;gBAClHmB,KAAK,EAAE5M,eAAgB;gBACvB6M,QAAQ,EAAG5G,CAAC,IAAKhG,kBAAkB,CAACgG,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;gBAAAlB,QAAA,gBAEpDpO,OAAA;kBAAQsP,KAAK,EAAC,EAAE;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChC7N,OAAA;kBAAQsP,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC7N,OAAA;kBAAQsP,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC7N,OAAA;kBAAQsP,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN7N,OAAA;cAAKmO,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BpO,OAAA;gBACEkO,OAAO,EAAEX,oBAAqB;gBAC9B+D,QAAQ,EAAElP,iBAAkB;gBAC5B+L,SAAS,EAAC,0KAA0K;gBAAAC,QAAA,EAEnLhM,iBAAiB,GAAG,aAAa,GAAG;cAAU;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELzL,iBAAiB,gBAChBpC,OAAA;UAAKmO,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDpO,OAAA,CAAC1B,cAAc;YAAAoP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,GACJ3L,cAAc,gBAChBlC,OAAA;UAAKmO,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAElBpO,OAAA;YAAKmO,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDpO,OAAA;cAAKmO,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxCpO,OAAA;gBAAKmO,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpO,OAAA,CAACjB,KAAK;kBAACoP,SAAS,EAAC;gBAAuB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3C7N,OAAA;kBAAKmO,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpO,OAAA;oBAAGmO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAClE7N,OAAA;oBAAGmO,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAElM,cAAc,CAACuK,QAAQ,CAACC;kBAAa;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7N,OAAA;cAAKmO,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCpO,OAAA;gBAAKmO,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpO,OAAA,CAACX,UAAU;kBAAC8O,SAAS,EAAC;gBAAwB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjD7N,OAAA;kBAAKmO,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpO,OAAA;oBAAGmO,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnE7N,OAAA;oBAAGmO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAElM,cAAc,CAACuK,QAAQ,CAACE;kBAAY;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7N,OAAA;cAAKmO,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCpO,OAAA;gBAAKmO,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpO,OAAA,CAACf,WAAW;kBAACkP,SAAS,EAAC;gBAAsB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChD7N,OAAA;kBAAKmO,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpO,OAAA;oBAAGmO,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnE7N,OAAA;oBAAGmO,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAElM,cAAc,CAACuK,QAAQ,CAACG;kBAAc;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7N,OAAA;cAAKmO,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eAC1CpO,OAAA;gBAAKmO,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpO,OAAA,CAACV,UAAU;kBAAC6O,SAAS,EAAC;gBAAyB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClD7N,OAAA;kBAAKmO,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpO,OAAA;oBAAGmO,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACvE7N,OAAA;oBAAGmO,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAElM,cAAc,CAACuK,QAAQ,CAACI,WAAW,EAAC,GAAC;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL3L,cAAc,CAAC4K,YAAY,CAAC3D,MAAM,GAAG,CAAC,iBACrCnJ,OAAA;YAAKmO,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBpO,OAAA;cAAImO,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAmB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF7N,OAAA;cAAKmO,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpO,OAAA;gBAAOmO,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACtEpO,OAAA;kBAAOmO,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAC3BpO,OAAA;oBAAAoO,QAAA,gBACEpO,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1F7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzF7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5F7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAS;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9F7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR7N,OAAA;kBAAOmO,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACxClM,cAAc,CAAC4K,YAAY,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEkD,KAAK,kBAC3ClQ,OAAA;oBAAqBmO,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;oBAAA9B,QAAA,gBAC1EpO,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEpB,IAAI,CAACC;oBAAc;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1E7N,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEpB,IAAI,CAAC7L;oBAAK;sBAAAuM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjE7N,OAAA;sBAAImO,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAEpB,IAAI,CAACE;oBAAI;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7E7N,OAAA;sBAAImO,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAEpB,IAAI,CAACG;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7E7N,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAC5CpB,IAAI,CAACH,WAAW,EAAC,GACpB;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GAPEb,IAAI,CAAC9J,KAAK;oBAAAwK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA3L,cAAc,CAACkL,UAAU,CAACjE,MAAM,GAAG,CAAC,iBACnCnJ,OAAA;YAAAoO,QAAA,gBACEpO,OAAA;cAAImO,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E7N,OAAA;cAAKmO,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpO,OAAA;gBAAOmO,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,gBACtEpO,OAAA;kBAAOmO,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAC3BpO,OAAA;oBAAAoO,QAAA,gBACEpO,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxF7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzF7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5F7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAS;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9F7N,OAAA;sBAAImO,SAAS,EAAC,iEAAiE;sBAAAC,QAAA,EAAC;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR7N,OAAA;kBAAOmO,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACxClM,cAAc,CAACkL,UAAU,CAACL,GAAG,CAAC,CAACC,IAAI,EAAEkD,KAAK,kBACzClQ,OAAA;oBAAqBmO,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;oBAAA9B,QAAA,gBAC1EpO,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEpB,IAAI,CAACK;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjE7N,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEpB,IAAI,CAAC7L;oBAAK;sBAAAuM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjE7N,OAAA;sBAAImO,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAEpB,IAAI,CAACE;oBAAI;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7E7N,OAAA;sBAAImO,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAEpB,IAAI,CAACG;oBAAM;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7E7N,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAC5CpB,IAAI,CAACH,WAAW,EAAC,GACpB;oBAAA;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GAPEb,IAAI,CAACK,KAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN7N,OAAA;UAAKmO,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE/C;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAjM,cAAc,iBACb5B,OAAA;MAAKmO,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFpO,OAAA;QAAKmO,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EpO,OAAA;UAAImO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClCtM,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAA4L,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACL7N,OAAA;UACEkO,OAAO,EAAE3E,eAAgB;UACzB4E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CpO,OAAA,CAAChB,CAAC;YAACsP,IAAI,EAAE;UAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7N,OAAA;QAAAoO,QAAA,GACGtM,cAAc,KAAK,KAAK,iBACvB9B,OAAA;UAAKmO,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBpO,OAAA;YAAGmO,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvE7N,OAAA;YAAMmO,SAAS,EAAC,WAAW;YAACoD,QAAQ,EAAE7I,sBAAuB;YAAA0F,QAAA,gBAC3DpO,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxH7N,OAAA,CAACxC,eAAe;gBACd8R,KAAK,EAAEzJ,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1B4J,QAAQ,EAAEzJ,oBAAqB;gBAC/BiK,QAAQ,EAAGU,IAAI,IAAK;kBAClB7K,YAAY,CAAC6K,IAAI,CAAC3I,EAAE,CAAC;kBACrBhC,oBAAoB,IAAA6E,MAAA,CAAI8F,IAAI,CAACrI,QAAQ,OAAAuC,MAAA,CAAI8F,IAAI,CAACtI,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACF6H,OAAO,EAAEA,CAAA,KAAM;kBACbpK,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACFuJ,WAAW,EAAC,mCAAsB;gBAClCmC,IAAI,EAAC;cAAS;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDtI,UAAU,CAACuD,MAAM,iBAChB9I,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACuD,MAAM;cAAA;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH7N,OAAA;gBACEoP,IAAI,EAAC,OAAO;gBACZjB,SAAS,6BAAAxD,MAAA,CAA6BpF,UAAU,CAACrC,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3GoM,KAAK,EAAEvJ,QAAS;gBAChBwJ,QAAQ,EAAG5G,CAAC,IAAK3C,WAAW,CAAC2C,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACDtI,UAAU,CAACrC,KAAK,iBACflD,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACrC,KAAK;cAAA;gBAAAwK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF7N,OAAA;gBACEoP,IAAI,EAAC,MAAM;gBACXjB,SAAS,EAAC,oDAAoD;gBAC9DmB,KAAK,EAAErJ,cAAe;gBACtBsJ,QAAQ,EAAG5G,CAAC,IAAKzC,iBAAiB,CAACyC,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H7N,OAAA;gBACEoP,IAAI,EAAC,MAAM;gBACXjB,SAAS,6BAAAxD,MAAA,CAA6BpF,UAAU,CAACyD,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7GsG,KAAK,EAAEnJ,UAAW;gBAClBoJ,QAAQ,EAAG5G,CAAC,IAAKvC,aAAa,CAACuC,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDtI,UAAU,CAACyD,OAAO,iBACjBhJ,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACyD,OAAO;cAAA;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7F7N,OAAA;gBAAKmO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CpO,OAAA;kBACEoP,IAAI,EAAC,UAAU;kBACftH,EAAE,EAAC,WAAW;kBACdmJ,OAAO,EAAE5K,SAAU;kBACnBkJ,QAAQ,EAAG5G,CAAC,IAAKrC,YAAY,CAACqC,CAAC,CAAC6G,MAAM,CAACyB,OAAO,CAAE;kBAChD9C,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACF7N,OAAA;kBAAOyR,OAAO,EAAC,WAAW;kBAACtD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E7N,OAAA;gBACEmO,SAAS,EAAC,oDAAoD;gBAC9DuD,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAE/I,OAAQ;gBACfgJ,QAAQ,EAAG5G,CAAC,IAAKnC,UAAU,CAACmC,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLtI,UAAU,CAACmE,MAAM,iBAChB1J,OAAA;cAAKmO,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjGpO,OAAA,CAACf,WAAW;gBAACqP,IAAI,EAAE,EAAG;gBAACH,SAAS,EAAC;cAA2B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D7N,OAAA;gBAAAoO,QAAA,EAAI7I,UAAU,CAACmE;cAAM;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACD7N,OAAA;cACEoP,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,uHAAuH;cACjImD,QAAQ,EAAE7L,YAAa;cAAA2I,QAAA,EAEtB3I,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACA/L,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKmO,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBpO,OAAA;YAAGmO,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElF7N,OAAA;YAAMmO,SAAS,EAAC,WAAW;YAACoD,QAAQ,EAAG5I,CAAC,IAAKwC,wBAAwB,CAACxC,CAAC,CAAE;YAAAyF,QAAA,gBACvEpO,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH7N,OAAA;gBACEoP,IAAI,EAAC,OAAO;gBACZjB,SAAS,6BAAAxD,MAAA,CAA6BpF,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH2K,KAAK,EAAE3K,UAAW;gBAClB4K,QAAQ,EAAG5G,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDtI,UAAU,CAACZ,UAAU,iBACpB3E,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACZ,UAAU;cAAA;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnH7N,OAAA;gBACEmO,SAAS,6BAAAxD,MAAA,CAA6BpF,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHmK,KAAK,EAAEnK,UAAW;gBAClBoK,QAAQ,EAAG5G,CAAC,IAAKvD,aAAa,CAACuD,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;gBAAAvD,QAAA,gBAERpO,OAAA;kBAAQsP,KAAK,EAAC,EAAE;kBAAAlB,QAAA,EAAC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC7N,OAAA;kBAAQsP,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC7N,OAAA;kBAAQsP,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC7N,OAAA;kBAAQsP,KAAK,EAAC,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACRtI,UAAU,CAACJ,UAAU,iBACpBnF,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACJ,UAAU;cAAA;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H7N,OAAA;gBACEoP,IAAI,EAAC,MAAM;gBACXjB,SAAS,6BAAAxD,MAAA,CAA6BpF,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClHqK,KAAK,EAAErK,YAAa;gBACpBsK,QAAQ,EAAG5G,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAAC6G,MAAM,CAACF,KAAK,CAAE;gBACjDqC,QAAQ;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDtI,UAAU,CAACN,YAAY,iBACtBjF,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACN,YAAY;cAAA;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E7N,OAAA;gBACEmO,SAAS,EAAC,oDAAoD;gBAC9DuD,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEjK,SAAU;gBACjBkK,QAAQ,EAAG5G,CAAC,IAAKrD,YAAY,CAACqD,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7N,OAAA;cACEoP,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,uHAAuH;cACjImD,QAAQ,EAAE7L,YAAa;cAAA2I,QAAA,EAEtB3I,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA/L,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAKmO,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBpO,OAAA;YAAGmO,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/D7N,OAAA;YAAMmO,SAAS,EAAC,WAAW;YAACoD,QAAQ,EAAG5I,CAAC,IAAKoC,0BAA0B,CAACpC,CAAC,CAAE;YAAAyF,QAAA,gBACzEpO,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF7N,OAAA;gBACEoP,IAAI,EAAC,MAAM;gBACXjB,SAAS,EAAC,oDAAoD;gBAC9DmB,KAAK,EAAE3I,eAAgB;gBACvB4I,QAAQ,EAAG5G,CAAC,IAAK/B,kBAAkB,CAAC+B,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAApO,OAAA;kBAAMmO,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H7N,OAAA;gBACEoP,IAAI,EAAC,MAAM;gBACXjB,SAAS,6BAAAxD,MAAA,CAA6BpF,UAAU,CAACsB,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjHyI,KAAK,EAAEzI,WAAY;gBACnB0I,QAAQ,EAAG5G,CAAC,IAAK7B,cAAc,CAAC6B,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,EACDtI,UAAU,CAACsB,WAAW,iBACrB7G,OAAA;gBAAGmO,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDpO,OAAA,CAACf,WAAW;kBAACqP,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACsB,WAAW;cAAA;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7F7N,OAAA;gBAAKmO,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CpO,OAAA;kBACEoP,IAAI,EAAC,UAAU;kBACftH,EAAE,EAAC,YAAY;kBACfmJ,OAAO,EAAElK,UAAW;kBACpBwI,QAAQ,EAAG5G,CAAC,IAAK3B,aAAa,CAAC2B,CAAC,CAAC6G,MAAM,CAACyB,OAAO,CAAE;kBACjD9C,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACF7N,OAAA;kBAAOyR,OAAO,EAAC,YAAY;kBAACtD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7N,OAAA;cAAAoO,QAAA,gBACEpO,OAAA;gBAAOmO,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E7N,OAAA;gBACEmO,SAAS,EAAC,oDAAoD;gBAC9DuD,IAAI,EAAC,GAAG;gBACRrC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAErI,QAAS;gBAChBsI,QAAQ,EAAG5G,CAAC,IAAKzB,WAAW,CAACyB,CAAC,CAAC6G,MAAM,CAACF,KAAK;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLtI,UAAU,CAACmE,MAAM,iBAChB1J,OAAA;cAAGmO,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDpO,OAAA,CAACf,WAAW;gBAACqP,IAAI,EAAE,EAAG;gBAACH,SAAS,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACtI,UAAU,CAACmE,MAAM;YAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACD7N,OAAA;cACEoP,IAAI,EAAC,QAAQ;cACbjB,SAAS,EAAC,uHAAuH;cACjImD,QAAQ,EAAE7L,YAAa;cAAA2I,QAAA,EAEtB3I,YAAY,GAAG,eAAe,GAAG;YAAc;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA/L,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAAoO,QAAA,EACG7G,WAAW,gBACVvH,OAAA;YAAKmO,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDpO,OAAA,CAAC1B,cAAc;cAAAoP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJhN,cAAc,gBAChBb,OAAA;YAAKmO,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpO,OAAA;cAAK4R,GAAG,EAAEjK,mBAAoB;cAACwG,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1EpO,OAAA;gBAAKmO,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnEpO,OAAA;kBAAKmO,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpO,OAAA;oBAAKmO,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7N,OAAA;oBAAAoO,QAAA,gBACEpO,OAAA;sBAAImO,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClE7N,OAAA;sBAAGmO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpE7N,OAAA;sBAAGmO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAIpE,IAAI,CAAC,CAAC,CAAC6G,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGN7N,OAAA;gBAAKmO,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCpO,OAAA;kBAAImO,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClE7N,OAAA;kBAAKmO,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBpO,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAA1N,oBAAA,GAACU,cAAc,CAAC4P,IAAI,cAAAtQ,oBAAA,uBAAnBA,oBAAA,CAAqBiI,QAAQ,EAAC,GAAC,GAAAhI,qBAAA,GAACS,cAAc,CAAC4P,IAAI,cAAArQ,qBAAA,uBAAnBA,qBAAA,CAAqB+H,SAAS;kBAAA;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpH7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAxN,qBAAA,GAAAQ,cAAc,CAAC4P,IAAI,cAAApQ,qBAAA,uBAAnBA,qBAAA,CAAqBsQ,KAAK,KAAI,UAAU;kBAAA;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrG7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAvN,qBAAA,GAAAO,cAAc,CAAC4P,IAAI,cAAAnQ,qBAAA,uBAAnBA,qBAAA,CAAqB+M,KAAK,KAAI,UAAU;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3F7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAtN,qBAAA,GAAAM,cAAc,CAAC4P,IAAI,cAAAlQ,qBAAA,uBAAnBA,qBAAA,CAAqBmQ,UAAU,KAAI,UAAU;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN7N,OAAA;gBAAAoO,QAAA,gBACEpO,OAAA;kBAAImO,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE7N,OAAA;kBAAKmO,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvEpO,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAChN,cAAc,CAACoM,cAAc;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClF7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC7P,cAAc,CAAC6C,cAAc,CAACyK,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClH7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC7P,cAAc,CAAC6C,cAAc,CAACgR,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7G7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC7P,cAAc,CAAC,CAAC6C,cAAc,CAACyK,cAAc,IAAI,CAAC,KAAKzK,cAAc,CAACgR,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9I7N,OAAA;oBAAAoO,QAAA,gBACEpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrD7N,OAAA;sBAAMmO,SAAS,oCAAAxD,MAAA,CAAoC9J,cAAc,CAACiR,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjHjR,cAAc,CAACiR,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5DjR,cAAc,CAACiR,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAA1D,QAAA,EACFvN,cAAc,CAACiR,MAAM,KAAK,MAAM,GAAG,eAAe,GACjDjR,cAAc,CAACiR,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpDjR,cAAc,CAACiR,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJ7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAChN,cAAc,CAACyI,WAAW,GAAG,IAAIU,IAAI,CAACnJ,cAAc,CAACyI,WAAW,CAAC,CAACuH,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzK7N,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAChN,cAAc,CAACmI,OAAO,GAAG,IAAIgB,IAAI,CAACnJ,cAAc,CAACmI,OAAO,CAAC,CAAC6H,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJhN,cAAc,CAACwI,IAAI,iBAAIrJ,OAAA;oBAAAoO,QAAA,gBAAGpO,OAAA;sBAAMmO,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAChN,cAAc,CAACwI,IAAI;kBAAA;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL/M,yBAAyB,IAAIA,yBAAyB,CAACiR,aAAa,IAAIjR,yBAAyB,CAACiR,aAAa,CAAC5I,MAAM,GAAG,CAAC,iBACzHnJ,OAAA;gBAAAoO,QAAA,gBACEpO,OAAA;kBAAImO,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzE7N,OAAA;kBAAKmO,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBtN,yBAAyB,CAACiR,aAAa,CAAChF,GAAG,CAAE/L,OAAO,iBACnDhB,OAAA;oBAAsBmO,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9EpO,OAAA;sBAAKmO,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDpO,OAAA;wBAAImO,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEpN,OAAO,CAACmN;sBAAS;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpD7N,OAAA;wBAAMmO,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAACpN,OAAO,CAACgR,UAAU;sBAAA;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN7N,OAAA;sBAAAoO,QAAA,gBAAGpO,OAAA;wBAAMmO,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC7P,cAAc,CAACgD,OAAO,CAACiR,MAAM,CAAC;oBAAA;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/F7N,OAAA;sBAAAoO,QAAA,gBAAGpO,OAAA;wBAAMmO,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAI7D,IAAI,CAAChJ,OAAO,CAACkR,QAAQ,CAAC,CAACrB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrH7M,OAAO,CAACqI,IAAI,iBAAIrJ,OAAA;sBAAAoO,QAAA,gBAAGpO,OAAA;wBAAMmO,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC7M,OAAO,CAACqI,IAAI;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtF7M,OAAO,CAAC8G,EAAE;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7N,OAAA;kBAAKmO,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CpO,OAAA;oBAAGmO,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAACpQ,cAAc,CAAC8C,yBAAyB,CAACqR,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjH7N,OAAA;oBAAGmO,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAACpQ,cAAc,CAAC6C,cAAc,CAACyK,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGD7N,OAAA;gBAAKmO,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCpO,OAAA;kBAAKmO,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDpO,OAAA;oBAAAoO,QAAA,gBACEpO,OAAA;sBAAGmO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClE7N,OAAA;sBAAGmO,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACN7N,OAAA;oBAAKmO,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BpO,OAAA;sBAAGmO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD7N,OAAA;sBAAKmO,SAAS,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5B7N,OAAA;sBAAGmO,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD7N,OAAA;sBAAGmO,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGP7N,OAAA;cAAKmO,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCpO,OAAA;gBACEkO,OAAO,EAAEA,CAAA,KAAMvE,UAAU,CAAC9I,cAAc,CAACiH,EAAE,CAAE;gBAC7CqG,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN7N,OAAA;YAAKmO,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED7N,OAAA,CAACxB,YAAY;MACX4T,MAAM,EAAE5Q,gBAAiB;MACzB6Q,SAAS,EAAE7G,aAAc;MACzByC,IAAI,EAAC,qGAAmD;MACxDqE,OAAO,EAAE7G;IAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAC3N,EAAA,CAhkDID,kBAAkB;EAAA,QACL/C,WAAW,EACXE,WAAW,EACuED,WAAW,EAEhEA,WAAW,EAyBwBA,WAAW;AAAA;AAAAoV,EAAA,GA9BxFtS,kBAAkB;AAkkDxB,eAAeA,kBAAkB;AAAC,IAAAsS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}