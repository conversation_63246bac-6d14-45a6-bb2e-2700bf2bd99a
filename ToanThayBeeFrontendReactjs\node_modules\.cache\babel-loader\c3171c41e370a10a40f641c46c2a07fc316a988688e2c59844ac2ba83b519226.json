{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\tuition\\\\UserTuitionPaymentDetail.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { fetchUserTuitionPaymentById, clearTuitionPayment } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency } from \"src/utils/formatters\";\nimport UserLayout from \"src/layouts/UserLayout\";\nimport PaymentModal from \"src/components/PaymentModal\";\nimport { CreditCard, ArrowLeft, FileText, Calendar, DollarSign, AlertCircle, CheckCircle, Clock, Receipt, Loader, ChevronRight } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserTuitionPaymentDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    tuitionPayment,\n    loading,\n    studentClassTuitions\n  } = useSelector(state => state.tuition);\n  // paymentProgress removed - không còn sử dụng với schema mới\n  const [classTuitionsLoading, setClassTuitionsLoading] = useState(false);\n\n  // State cho modal thanh toán\n  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);\n  const [paymentInfo, setPaymentInfo] = useState(null);\n  useEffect(() => {\n    dispatch(fetchUserTuitionPaymentById(id));\n    return () => {\n      dispatch(clearTuitionPayment());\n    };\n  }, [dispatch, id]);\n\n  // useEffect(() => {\n  //   if (tuitionPayment) {\n  //     setClassTuitionsLoading(true);\n  //     dispatch(fetchStudentClassTuitionsByMonth(tuitionPayment.month))\n  //       .unwrap()\n  //       .then(() => {\n  //         setClassTuitionsLoading(false);\n  //       })\n  //       .catch((error) => {\n  //         console.error(\"Error fetching class tuitions:\", error);\n  //         setClassTuitionsLoading(false);\n  //       });\n  //   }\n  // }, [dispatch, tuitionPayment]);\n\n  // useEffect for paymentProgress removed - không còn sử dụng với schema mới\n\n  const handleOpenPaymentModal = () => {\n    if (!tuitionPayment) return;\n    setPaymentInfo({\n      id: tuitionPayment.id,\n      month: tuitionPayment.monthFormatted,\n      amount: \"Liên hệ admin để biết số tiền\",\n      // Không còn expectedAmount/paidAmount\n      description: \"\".concat(user.firstName, \" \").concat(user.lastName, \" \").concat(user.highSchool, \" HP_\").concat(tuitionPayment.monthFormatted.replace(' ', '_'), \"_\").concat(tuitionPayment.id)\n    });\n    setIsPaymentModalOpen(true);\n  };\n  const handleClosePaymentModal = () => {\n    setIsPaymentModalOpen(false);\n    setPaymentInfo(null);\n  };\n  const getStatusBadge = (status, isOverdue) => {\n    if (status === \"PAID\") {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), \"\\u0110\\xE3 thanh to\\xE1n\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this);\n    } else if (isOverdue) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-3 py-1 rounded-full text-sm bg-red-100 text-red-800 flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), \"Qu\\xE1 h\\u1EA1n\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this);\n    } else if (status === \"PARTIAL\") {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), \"Ch\\u01B0a thanh to\\xE1n\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  const getPaymentStatusIcon = (status, isOverdue) => {\n    if (status === \"PAID\") {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 bg-green-100 rounded-full\",\n        children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-8 h-8 text-green-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this);\n    } else if (isOverdue) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 bg-red-100 rounded-full\",\n        children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-8 h-8 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this);\n    } else if (status === \"PARTIAL\") {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 bg-blue-100 rounded-full\",\n        children: /*#__PURE__*/_jsxDEV(DollarSign, {\n          className: \"w-8 h-8 text-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 bg-yellow-100 rounded-full\",\n        children: /*#__PURE__*/_jsxDEV(CreditCard, {\n          className: \"w-8 h-8 text-yellow-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(UserLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-8 max-w-4xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 text-center text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(Loader, {\n            size: 40,\n            className: \"mx-auto mb-4 text-gray-300 animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0110ang t\\u1EA3i th\\xF4ng tin h\\u1ECDc ph\\xED...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this);\n  }\n  if (!tuitionPayment) {\n    return /*#__PURE__*/_jsxDEV(UserLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-8 max-w-4xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 text-center text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n            size: 40,\n            className: \"mx-auto mb-4 text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate(\"/tuition-payments\"),\n            className: \"mt-4 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors\",\n            children: \"Quay l\\u1EA1i danh s\\xE1ch h\\u1ECDc ph\\xED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(UserLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8 max-w-4xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-6 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(\"/tuition-payments\"),\n          className: \"text-gray-500 hover:text-sky-600 flex items-center gap-1\",\n          children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChevronRight, {\n          size: 16,\n          className: \"mx-2 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: \"Chi ti\\u1EBFt h\\u1ECDc ph\\xED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-800\",\n          children: [\"Chi ti\\u1EBFt h\\u1ECDc ph\\xED \", tuitionPayment.monthFormatted]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), getStatusBadge(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 flex gap-6\",\n          children: [getPaymentStatusIcon(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mb-1\",\n                  children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-semibold\",\n                  children: tuitionPayment.isPaid ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-600\",\n                    children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 23\n                  }, this) : tuitionPayment.isOverdue ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-600\",\n                    children: \"Qu\\xE1 h\\u1EA1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-yellow-600\",\n                    children: \"Ch\\u01B0a thanh to\\xE1n\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mb-1\",\n                  children: \"Ghi ch\\xFA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xl font-semibold text-gray-600\",\n                  children: tuitionPayment.note || \"Không có ghi chú\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mb-1\",\n                  children: \"H\\u1EA1n thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-base flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    size: 16,\n                    className: \"text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), tuitionPayment.dueDateFormatted || \"Chưa có hạn thanh toán\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mb-1\",\n                  children: \"Ng\\xE0y thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-base flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    size: 16,\n                    className: \"text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this), tuitionPayment.paymentDateFormatted || \"Chưa thanh toán\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 p-4 bg-gray-50 rounded-md\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-base\",\n                children: tuitionPayment.note\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold mb-4 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              size: 20,\n              className: \"text-sky-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), \"Chi ti\\u1EBFt h\\u1ECDc ph\\xED theo l\\u1EDBp\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), classTuitionsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Loader, {\n              size: 30,\n              className: \"mx-auto mb-4 text-sky-500 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"\\u0110ang t\\u1EA3i danh s\\xE1ch h\\u1ECDc ph\\xED...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this) : !studentClassTuitions || !studentClassTuitions.classTuitions || studentClassTuitions.classTuitions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 30,\n              className: \"mx-auto mb-4 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u h\\u1ECDc ph\\xED cho th\\xE1ng n\\xE0y\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: studentClassTuitions.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-medium text-gray-800\",\n                    children: tuition.className\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded-full\",\n                    children: [\"L\\u1EDBp \", tuition.classGrade]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500\",\n                      children: \"H\\u1ECDc ph\\xED:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: formatCurrency(tuition.amount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500\",\n                      children: \"Ng\\xE0y tham gia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800 flex items-center gap-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                        size: 14,\n                        className: \"text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 286,\n                        columnNumber: 29\n                      }, this), new Date(tuition.joinDate).toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 25\n                  }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: tuition.note\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this)]\n              }, tuition.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 p-4 bg-sky-50 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-semibold text-sky-700\",\n                    children: formatCurrency(studentClassTuitions.totalAmount || 0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 md:mt-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Th\\xE1ng:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-semibold text-sky-700\",\n                    children: studentClassTuitions.monthFormatted\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(\"/tuition-payments\"),\n          className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), \"Quay l\\u1EA1i\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), !tuitionPayment.isPaid && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleOpenPaymentModal,\n          className: \"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Receipt, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), \"Thanh to\\xE1n\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PaymentModal, {\n      isOpen: isPaymentModalOpen,\n      onClose: handleClosePaymentModal,\n      paymentInfo: paymentInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n};\n_s(UserTuitionPaymentDetail, \"XKuNJGE49cZgNzPTsD5S7GdVxOc=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = UserTuitionPaymentDetail;\nexport default UserTuitionPaymentDetail;\nvar _c;\n$RefreshReg$(_c, \"UserTuitionPaymentDetail\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "fetchUserTuitionPaymentById", "clearTuitionPayment", "formatCurrency", "UserLayout", "PaymentModal", "CreditCard", "ArrowLeft", "FileText", "Calendar", "DollarSign", "AlertCircle", "CheckCircle", "Clock", "Receipt", "Loader", "ChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserTuitionPaymentDetail", "_s", "id", "dispatch", "navigate", "user", "state", "auth", "tuitionPayment", "loading", "studentClassTuitions", "tuition", "classTuitionsLoading", "setClassTuitionsLoading", "isPaymentModalOpen", "setIsPaymentModalOpen", "paymentInfo", "setPaymentInfo", "useEffect", "handleOpenPaymentModal", "month", "monthFormatted", "amount", "description", "concat", "firstName", "lastName", "highSchool", "replace", "handleClosePaymentModal", "getStatusBadge", "status", "isOverdue", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPaymentStatusIcon", "onClick", "isPaid", "note", "dueDateFormatted", "paymentDateFormatted", "classTuitions", "length", "map", "classGrade", "Date", "joinDate", "toLocaleDateString", "totalAmount", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/tuition/UserTuitionPaymentDetail.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport {\n  fetchUserTuitionPaymentById,\n  clearTuitionPayment,\n} from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency } from \"src/utils/formatters\";\nimport UserLayout from \"src/layouts/UserLayout\";\nimport PaymentModal from \"src/components/PaymentModal\";\nimport {\n  CreditCard,\n  ArrowLeft,\n  FileText,\n  Calendar,\n  DollarSign,\n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Receipt,\n  Loader,\n  ChevronRight\n} from \"lucide-react\";\n\nconst UserTuitionPaymentDetail = () => {\n  const { id } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.auth);\n  const { tuitionPayment, loading, studentClassTuitions } = useSelector((state) => state.tuition);\n  // paymentProgress removed - không còn sử dụng với schema mới\n  const [classTuitionsLoading, setClassTuitionsLoading] = useState(false);\n\n  // State cho modal thanh toán\n  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);\n  const [paymentInfo, setPaymentInfo] = useState(null);\n\n  useEffect(() => {\n    dispatch(fetchUserTuitionPaymentById(id));\n\n    return () => {\n      dispatch(clearTuitionPayment());\n    };\n  }, [dispatch, id]);\n\n  // useEffect(() => {\n  //   if (tuitionPayment) {\n  //     setClassTuitionsLoading(true);\n  //     dispatch(fetchStudentClassTuitionsByMonth(tuitionPayment.month))\n  //       .unwrap()\n  //       .then(() => {\n  //         setClassTuitionsLoading(false);\n  //       })\n  //       .catch((error) => {\n  //         console.error(\"Error fetching class tuitions:\", error);\n  //         setClassTuitionsLoading(false);\n  //       });\n  //   }\n  // }, [dispatch, tuitionPayment]);\n\n  // useEffect for paymentProgress removed - không còn sử dụng với schema mới\n\n  const handleOpenPaymentModal = () => {\n    if (!tuitionPayment) return;\n\n    setPaymentInfo({\n      id: tuitionPayment.id,\n      month: tuitionPayment.monthFormatted,\n      amount: \"Liên hệ admin để biết số tiền\", // Không còn expectedAmount/paidAmount\n      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${tuitionPayment.monthFormatted.replace(' ', '_')}_${tuitionPayment.id}`\n    });\n    setIsPaymentModalOpen(true);\n  };\n\n  const handleClosePaymentModal = () => {\n    setIsPaymentModalOpen(false);\n    setPaymentInfo(null);\n  };\n\n  const getStatusBadge = (status, isOverdue) => {\n    if (status === \"PAID\") {\n      return (\n        <span className=\"px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 flex items-center gap-1\">\n          <CheckCircle size={16} />\n          Đã thanh toán\n        </span>\n      );\n    } else if (isOverdue) {\n      return (\n        <span className=\"px-3 py-1 rounded-full text-sm bg-red-100 text-red-800 flex items-center gap-1\">\n          <AlertCircle size={16} />\n          Quá hạn\n        </span>\n      );\n    } else if (status === \"PARTIAL\") {\n      return (\n        <span className=\"px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 flex items-center gap-1\">\n          <DollarSign size={16} />\n          Thanh toán một phần\n        </span>\n      );\n    } else {\n      return (\n        <span className=\"px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 flex items-center gap-1\">\n          <CreditCard size={16} />\n          Chưa thanh toán\n        </span>\n      );\n    }\n  };\n\n  const getPaymentStatusIcon = (status, isOverdue) => {\n    if (status === \"PAID\") {\n      return (\n        <div className=\"p-4 bg-green-100 rounded-full\">\n          <CheckCircle className=\"w-8 h-8 text-green-600\" />\n        </div>\n      );\n    } else if (isOverdue) {\n      return (\n        <div className=\"p-4 bg-red-100 rounded-full\">\n          <AlertCircle className=\"w-8 h-8 text-red-600\" />\n        </div>\n      );\n    } else if (status === \"PARTIAL\") {\n      return (\n        <div className=\"p-4 bg-blue-100 rounded-full\">\n          <DollarSign className=\"w-8 h-8 text-blue-600\" />\n        </div>\n      );\n    } else {\n      return (\n        <div className=\"p-4 bg-yellow-100 rounded-full\">\n          <CreditCard className=\"w-8 h-8 text-yellow-600\" />\n        </div>\n      );\n    }\n  };\n\n  if (loading) {\n    return (\n      <UserLayout>\n        <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n          <div className=\"p-8 text-center text-gray-500\">\n            <Loader size={40} className=\"mx-auto mb-4 text-gray-300 animate-spin\" />\n            <p>Đang tải thông tin học phí...</p>\n          </div>\n        </div>\n      </UserLayout>\n    );\n  }\n\n  if (!tuitionPayment) {\n    return (\n      <UserLayout>\n        <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n          <div className=\"p-8 text-center text-gray-500\">\n            <AlertCircle size={40} className=\"mx-auto mb-4 text-gray-300\" />\n            <p>Không tìm thấy thông tin học phí.</p>\n            <button\n              onClick={() => navigate(\"/tuition-payments\")}\n              className=\"mt-4 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors\"\n            >\n              Quay lại danh sách học phí\n            </button>\n          </div>\n        </div>\n      </UserLayout>\n    );\n  }\n\n  return (\n    <UserLayout>\n      <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n        {/* Breadcrumb */}\n        <div className=\"flex items-center mb-6 text-sm\">\n          <button\n            onClick={() => navigate(\"/tuition-payments\")}\n            className=\"text-gray-500 hover:text-sky-600 flex items-center gap-1\"\n          >\n            Danh sách học phí\n          </button>\n          <ChevronRight size={16} className=\"mx-2 text-gray-400\" />\n          <span className=\"text-gray-700\">Chi tiết học phí</span>\n        </div>\n\n        {/* Tiêu đề */}\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            Chi tiết học phí {tuitionPayment.monthFormatted}\n          </h1>\n          {getStatusBadge(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)}\n        </div>\n\n        {/* Thông tin chính */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 mb-6\">\n          <div className=\"p-6 flex gap-6\">\n            {getPaymentStatusIcon(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)}\n            <div className=\"flex-1\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <p className=\"text-sm text-gray-500 mb-1\">Trạng thái thanh toán</p>\n                  <p className=\"text-xl font-semibold\">\n                    {tuitionPayment.isPaid ? (\n                      <span className=\"text-green-600\">Đã thanh toán</span>\n                    ) : tuitionPayment.isOverdue ? (\n                      <span className=\"text-red-600\">Quá hạn</span>\n                    ) : (\n                      <span className=\"text-yellow-600\">Chưa thanh toán</span>\n                    )}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-500 mb-1\">Ghi chú</p>\n                  <p className=\"text-xl font-semibold text-gray-600\">\n                    {tuitionPayment.note || \"Không có ghi chú\"}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <p className=\"text-sm text-gray-500 mb-1\">Hạn thanh toán</p>\n                  <p className=\"text-base flex items-center gap-1\">\n                    <Calendar size={16} className=\"text-gray-400\" />\n                    {tuitionPayment.dueDateFormatted || \"Chưa có hạn thanh toán\"}\n                  </p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-500 mb-1\">Ngày thanh toán</p>\n                  <p className=\"text-base flex items-center gap-1\">\n                    <Calendar size={16} className=\"text-gray-400\" />\n                    {tuitionPayment.paymentDateFormatted || \"Chưa thanh toán\"}\n                  </p>\n                </div>\n              </div>\n\n              {tuitionPayment.note && (\n                <div className=\"mt-6 p-4 bg-gray-50 rounded-md\">\n                  <p className=\"text-sm text-gray-500 mb-1\">Ghi chú</p>\n                  <p className=\"text-base\">{tuitionPayment.note}</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Danh sách học phí theo lớp */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 mb-6\">\n          <div className=\"p-6\">\n            <h2 className=\"text-lg font-semibold mb-4 flex items-center gap-2\">\n              <FileText size={20} className=\"text-sky-600\" />\n              Chi tiết học phí theo lớp\n            </h2>\n\n            {classTuitionsLoading ? (\n              <div className=\"py-8 text-center\">\n                <Loader size={30} className=\"mx-auto mb-4 text-sky-500 animate-spin\" />\n                <p className=\"text-gray-500\">Đang tải danh sách học phí...</p>\n              </div>\n            ) : !studentClassTuitions || !studentClassTuitions.classTuitions || studentClassTuitions.classTuitions.length === 0 ? (\n              <div className=\"py-8 text-center\">\n                <AlertCircle size={30} className=\"mx-auto mb-4 text-gray-400\" />\n                <p className=\"text-gray-500\">Không có dữ liệu học phí cho tháng này</p>\n              </div>\n            ) : (\n              <>\n                <div className=\"space-y-4\">\n                  {studentClassTuitions.classTuitions.map((tuition) => (\n                    <div key={tuition.id} className=\"border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow\">\n                      <div className=\"flex justify-between items-center mb-3\">\n                        <h3 className=\"font-medium text-gray-800\">{tuition.className}</h3>\n                        <span className=\"bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded-full\">\n                          Lớp {tuition.classGrade}\n                        </span>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                        <div>\n                          <p className=\"text-gray-500\">Học phí:</p>\n                          <p className=\"font-medium text-gray-800\">{formatCurrency(tuition.amount)}</p>\n                        </div>\n                        <div>\n                          <p className=\"text-gray-500\">Ngày tham gia:</p>\n                          <p className=\"font-medium text-gray-800 flex items-center gap-1\">\n                            <Calendar size={14} className=\"text-gray-400\" />\n                            {new Date(tuition.joinDate).toLocaleDateString('vi-VN')}\n                          </p>\n                        </div>\n                        {tuition.note && (\n                          <div>\n                            <p className=\"text-gray-500\">Ghi chú:</p>\n                            <p className=\"font-medium text-gray-800\">{tuition.note}</p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"mt-6 p-4 bg-sky-50 rounded-lg\">\n                  <div className=\"flex flex-col md:flex-row justify-between\">\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Tổng học phí các lớp:</p>\n                      <p className=\"text-lg font-semibold text-sky-700\">{formatCurrency(studentClassTuitions.totalAmount || 0)}</p>\n                    </div>\n                    <div className=\"mt-3 md:mt-0\">\n                      <p className=\"text-sm text-gray-500\">Tháng:</p>\n                      <p className=\"text-lg font-semibold text-sky-700\">{studentClassTuitions.monthFormatted}</p>\n                    </div>\n                  </div>\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Các tùy chọn */}\n        <div className=\"flex justify-end gap-3\">\n          <button\n            onClick={() => navigate(\"/tuition-payments\")}\n            className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-2\"\n          >\n            <ArrowLeft size={16} />\n            Quay lại\n          </button>\n\n          {!tuitionPayment.isPaid && (\n            <button\n              onClick={handleOpenPaymentModal}\n              className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2\"\n            >\n              <Receipt size={16} />\n              Thanh toán\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Payment Modal */}\n      <PaymentModal\n        isOpen={isPaymentModalOpen}\n        onClose={handleClosePaymentModal}\n        paymentInfo={paymentInfo}\n      />\n    </UserLayout>\n  );\n};\n\nexport default UserTuitionPaymentDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SACEC,2BAA2B,EAC3BC,mBAAmB,QACd,mCAAmC;AAC1C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,6BAA6B;AACtD,SACEC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,YAAY,QACP,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC1B,MAAMwB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAK,CAAC,GAAG5B,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EACnD,MAAM;IAAEC,cAAc;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAGjC,WAAW,CAAE6B,KAAK,IAAKA,KAAK,CAACK,OAAO,CAAC;EAC/F;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAEpD2C,SAAS,CAAC,MAAM;IACdf,QAAQ,CAACvB,2BAA2B,CAACsB,EAAE,CAAC,CAAC;IAEzC,OAAO,MAAM;MACXC,QAAQ,CAACtB,mBAAmB,CAAC,CAAC,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,CAACsB,QAAQ,EAAED,EAAE,CAAC,CAAC;;EAElB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA,MAAMiB,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACX,cAAc,EAAE;IAErBS,cAAc,CAAC;MACbf,EAAE,EAAEM,cAAc,CAACN,EAAE;MACrBkB,KAAK,EAAEZ,cAAc,CAACa,cAAc;MACpCC,MAAM,EAAE,+BAA+B;MAAE;MACzCC,WAAW,KAAAC,MAAA,CAAKnB,IAAI,CAACoB,SAAS,OAAAD,MAAA,CAAInB,IAAI,CAACqB,QAAQ,OAAAF,MAAA,CAAInB,IAAI,CAACsB,UAAU,UAAAH,MAAA,CAAOhB,cAAc,CAACa,cAAc,CAACO,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,OAAAJ,MAAA,CAAIhB,cAAc,CAACN,EAAE;IAC/I,CAAC,CAAC;IACFa,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMc,uBAAuB,GAAGA,CAAA,KAAM;IACpCd,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMa,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;IAC5C,IAAID,MAAM,KAAK,MAAM,EAAE;MACrB,oBACElC,OAAA;QAAMoC,SAAS,EAAC,oFAAoF;QAAAC,QAAA,gBAClGrC,OAAA,CAACN,WAAW;UAAC4C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAEX,CAAC,MAAM,IAAIP,SAAS,EAAE;MACpB,oBACEnC,OAAA;QAAMoC,SAAS,EAAC,gFAAgF;QAAAC,QAAA,gBAC9FrC,OAAA,CAACP,WAAW;UAAC6C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAEX,CAAC,MAAM,IAAIR,MAAM,KAAK,SAAS,EAAE;MAC/B,oBACElC,OAAA;QAAMoC,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAChGrC,OAAA,CAACR,UAAU;UAAC8C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oCAE1B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAEX,CAAC,MAAM;MACL,oBACE1C,OAAA;QAAMoC,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACpGrC,OAAA,CAACZ,UAAU;UAACkD,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,2BAE1B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAEX;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAACT,MAAM,EAAEC,SAAS,KAAK;IAClD,IAAID,MAAM,KAAK,MAAM,EAAE;MACrB,oBACElC,OAAA;QAAKoC,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5CrC,OAAA,CAACN,WAAW;UAAC0C,SAAS,EAAC;QAAwB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAEV,CAAC,MAAM,IAAIP,SAAS,EAAE;MACpB,oBACEnC,OAAA;QAAKoC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CrC,OAAA,CAACP,WAAW;UAAC2C,SAAS,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAEV,CAAC,MAAM,IAAIR,MAAM,KAAK,SAAS,EAAE;MAC/B,oBACElC,OAAA;QAAKoC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CrC,OAAA,CAACR,UAAU;UAAC4C,SAAS,EAAC;QAAuB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAEV,CAAC,MAAM;MACL,oBACE1C,OAAA;QAAKoC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7CrC,OAAA,CAACZ,UAAU;UAACgD,SAAS,EAAC;QAAyB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAEV;EACF,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACEZ,OAAA,CAACd,UAAU;MAAAmD,QAAA,eACTrC,OAAA;QAAKoC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDrC,OAAA;UAAKoC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CrC,OAAA,CAACH,MAAM;YAACyC,IAAI,EAAE,EAAG;YAACF,SAAS,EAAC;UAAyC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxE1C,OAAA;YAAAqC,QAAA,EAAG;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEjB;EAEA,IAAI,CAAC/B,cAAc,EAAE;IACnB,oBACEX,OAAA,CAACd,UAAU;MAAAmD,QAAA,eACTrC,OAAA;QAAKoC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDrC,OAAA;UAAKoC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CrC,OAAA,CAACP,WAAW;YAAC6C,IAAI,EAAE,EAAG;YAACF,SAAS,EAAC;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE1C,OAAA;YAAAqC,QAAA,EAAG;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxC1C,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,mBAAmB,CAAE;YAC7C6B,SAAS,EAAC,oFAAoF;YAAAC,QAAA,EAC/F;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEjB;EAEA,oBACE1C,OAAA,CAACd,UAAU;IAAAmD,QAAA,gBACTrC,OAAA;MAAKoC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrC,OAAA;QAAKoC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7CrC,OAAA;UACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,mBAAmB,CAAE;UAC7C6B,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACrE;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA,CAACF,YAAY;UAACwC,IAAI,EAAE,EAAG;UAACF,SAAS,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzD1C,OAAA;UAAMoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAGN1C,OAAA;QAAKoC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDrC,OAAA;UAAIoC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAAC,gCAC9B,EAAC1B,cAAc,CAACa,cAAc;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,EACJT,cAAc,CAACtB,cAAc,CAACkC,MAAM,GAAG,MAAM,GAAGlC,cAAc,CAACwB,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAExB,cAAc,CAACwB,SAAS,CAAC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxH,CAAC,eAGN1C,OAAA;QAAKoC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxErC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC5BM,oBAAoB,CAAChC,cAAc,CAACkC,MAAM,GAAG,MAAM,GAAGlC,cAAc,CAACwB,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAExB,cAAc,CAACwB,SAAS,CAAC,eACjInC,OAAA;YAAKoC,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBrC,OAAA;cAAKoC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDrC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAGoC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnE1C,OAAA;kBAAGoC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACjC1B,cAAc,CAACkC,MAAM,gBACpB7C,OAAA;oBAAMoC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,GACnD/B,cAAc,CAACwB,SAAS,gBAC1BnC,OAAA;oBAAMoC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAE7C1C,OAAA;oBAAMoC,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACxD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN1C,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAGoC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACrD1C,OAAA;kBAAGoC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAC/C1B,cAAc,CAACmC,IAAI,IAAI;gBAAkB;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA;cAAKoC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDrC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAGoC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5D1C,OAAA;kBAAGoC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAC9CrC,OAAA,CAACT,QAAQ;oBAAC+C,IAAI,EAAE,EAAG;oBAACF,SAAS,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC/C/B,cAAc,CAACoC,gBAAgB,IAAI,wBAAwB;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN1C,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAGoC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC7D1C,OAAA;kBAAGoC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAC9CrC,OAAA,CAACT,QAAQ;oBAAC+C,IAAI,EAAE,EAAG;oBAACF,SAAS,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC/C/B,cAAc,CAACqC,oBAAoB,IAAI,iBAAiB;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL/B,cAAc,CAACmC,IAAI,iBAClB9C,OAAA;cAAKoC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CrC,OAAA;gBAAGoC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrD1C,OAAA;gBAAGoC,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE1B,cAAc,CAACmC;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKoC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxErC,OAAA;UAAKoC,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBrC,OAAA;YAAIoC,SAAS,EAAC,oDAAoD;YAAAC,QAAA,gBAChErC,OAAA,CAACV,QAAQ;cAACgD,IAAI,EAAE,EAAG;cAACF,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,+CAEjD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJ3B,oBAAoB,gBACnBf,OAAA;YAAKoC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrC,OAAA,CAACH,MAAM;cAACyC,IAAI,EAAE,EAAG;cAACF,SAAS,EAAC;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvE1C,OAAA;cAAGoC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,GACJ,CAAC7B,oBAAoB,IAAI,CAACA,oBAAoB,CAACoC,aAAa,IAAIpC,oBAAoB,CAACoC,aAAa,CAACC,MAAM,KAAK,CAAC,gBACjHlD,OAAA;YAAKoC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrC,OAAA,CAACP,WAAW;cAAC6C,IAAI,EAAE,EAAG;cAACF,SAAS,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChE1C,OAAA;cAAGoC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,gBAEN1C,OAAA,CAAAE,SAAA;YAAAmC,QAAA,gBACErC,OAAA;cAAKoC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBxB,oBAAoB,CAACoC,aAAa,CAACE,GAAG,CAAErC,OAAO,iBAC9Cd,OAAA;gBAAsBoC,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACvGrC,OAAA;kBAAKoC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDrC,OAAA;oBAAIoC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAEvB,OAAO,CAACsB;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClE1C,OAAA;oBAAMoC,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,GAAC,WACnE,EAACvB,OAAO,CAACsC,UAAU;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAEN1C,OAAA;kBAAKoC,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DrC,OAAA;oBAAAqC,QAAA,gBACErC,OAAA;sBAAGoC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzC1C,OAAA;sBAAGoC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEpD,cAAc,CAAC6B,OAAO,CAACW,MAAM;oBAAC;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACN1C,OAAA;oBAAAqC,QAAA,gBACErC,OAAA;sBAAGoC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC/C1C,OAAA;sBAAGoC,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAC9DrC,OAAA,CAACT,QAAQ;wBAAC+C,IAAI,EAAE,EAAG;wBAACF,SAAS,EAAC;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EAC/C,IAAIW,IAAI,CAACvC,OAAO,CAACwC,QAAQ,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,EACL5B,OAAO,CAACgC,IAAI,iBACX9C,OAAA;oBAAAqC,QAAA,gBACErC,OAAA;sBAAGoC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACzC1C,OAAA;sBAAGoC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEvB,OAAO,CAACgC;oBAAI;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA1BE5B,OAAO,CAACT,EAAE;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2Bf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAKoC,SAAS,EAAC,+BAA+B;cAAAC,QAAA,eAC5CrC,OAAA;gBAAKoC,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACxDrC,OAAA;kBAAAqC,QAAA,gBACErC,OAAA;oBAAGoC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC9D1C,OAAA;oBAAGoC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAEpD,cAAc,CAAC4B,oBAAoB,CAAC2C,WAAW,IAAI,CAAC;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC,eACN1C,OAAA;kBAAKoC,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BrC,OAAA;oBAAGoC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/C1C,OAAA;oBAAGoC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAExB,oBAAoB,CAACW;kBAAc;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKoC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCrC,OAAA;UACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,mBAAmB,CAAE;UAC7C6B,SAAS,EAAC,sHAAsH;UAAAC,QAAA,gBAEhIrC,OAAA,CAACX,SAAS;YAACiD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER,CAAC/B,cAAc,CAACkC,MAAM,iBACrB7C,OAAA;UACE4C,OAAO,EAAEtB,sBAAuB;UAChCc,SAAS,EAAC,2GAA2G;UAAAC,QAAA,gBAErHrC,OAAA,CAACJ,OAAO;YAAC0C,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA,CAACb,YAAY;MACXsE,MAAM,EAAExC,kBAAmB;MAC3ByC,OAAO,EAAE1B,uBAAwB;MACjCb,WAAW,EAAEA;IAAY;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEjB,CAAC;AAACtC,EAAA,CAnUID,wBAAwB;EAAA,QACbrB,SAAS,EACPH,WAAW,EACXE,WAAW,EACXD,WAAW,EAC8BA,WAAW;AAAA;AAAA+E,EAAA,GALjExD,wBAAwB;AAqU9B,eAAeA,wBAAwB;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}