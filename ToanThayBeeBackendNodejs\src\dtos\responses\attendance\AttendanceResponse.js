// dtos/responses/attendance/AttendanceResponse.js

export default class responseAttendance {
    constructor({ id, userId, lessonId, status, attendanceTime, note, user, createdAt, updatedAt }) {
        this.id = id;
        this.userId = userId;
        this.lessonId = lessonId;
        this.status = status;
        this.attendanceTime = attendanceTime;
        this.note = note;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.user = user
            ? {
                id: user.id,
                fullName: `${user.lastName} ${user.firstName}`,
                phone: user.phone,
                highSchool: user.highSchool,
                class: user.class,
                username: user.username,
                password: user.password
            }
            : null;
    }
}
