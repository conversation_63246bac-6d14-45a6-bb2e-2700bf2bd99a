import db from "../models/index.js"
import { sendClassNotification } from "../utils/notificationUtils.js"
import * as notificationService from "../services/notification.service.js"

export const getLessonById = async (req, res) => {
    const { id } = req.params
    const lesson = await db.Lesson.findOne({ where: { id } })
    if (!lesson) {
        return res.status(404).json({
            message: `<PERSON>h<PERSON>ng tìm thấy buổi học với ID: ${id}!`
        })
    }
    return res.status(200).json({
        message: 'Lấy thông tin buổi học thành công!',
        data: lesson
    })
}

export const getLessonByClassId = async (req, res) => {
    const { classId } = req.params

    const lessons = await db.Lesson.findAll({
        where: { classId },
        include: [
            {
                model: db.Class,
                as: 'class',
            },
        ],
        order: [['createdAt', 'DESC']],
    })

    return res.status(200).json({
        message: '<PERSON><PERSON><PERSON> danh sách buổi học thành công!',
        data: lessons,
    })
}

// API lấy 20 buổi học gần nhất từ tất cả các lớp
export const getUserAttendedLessons = async (req, res) => {
    try {
        // Lấy 20 buổi học gần nhất từ tất cả các lớp đang hoạt động
        const lessons = await db.Lesson.findAll({
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    where: {
                        status: 'LHD' // Chỉ lấy các lớp đang hoạt động
                    },
                    attributes: ['id', 'name', 'description', 'class_code', 'academicYear', 'status']
                }
            ],
            order: [['day', 'DESC']], // Sắp xếp theo ngày học mới nhất
            limit: 20 // Giới hạn 20 buổi học gần nhất
        });

        if (lessons.length === 0) {
            return res.status(200).json({
                message: 'Không có buổi học nào!',
                data: []
            });
        }

        // Format dữ liệu trả về
        const formattedData = lessons.map(lesson => ({
            id: lesson.id,
            name: lesson.name,
            description: lesson.description,
            day: lesson.day,
            chapter: lesson.chapter,
            learningItemCount: lesson.learningItemCount,
            createdAt: lesson.createdAt,
            class: {
                id: lesson.class.id,
                name: lesson.class.name,
                description: lesson.class.description,
                class_code: lesson.class.class_code,
                academicYear: lesson.class.academicYear,
                status: lesson.class.status
            }
        }));

        return res.status(200).json({
            message: 'Lấy danh sách 20 buổi học gần nhất thành công!',
            data: formattedData,
            totalItems: formattedData.length
        });

    } catch (error) {
        console.error('Lỗi khi lấy danh sách 20 buổi học gần nhất:', error);
        return res.status(500).json({
            message: 'Lỗi server khi lấy danh sách 20 buổi học gần nhất',
            error: error.message
        });
    }
};

export const insertLesson = async (req, res) => {
    const t = await db.sequelize.transaction();

    try {
        // Thêm classId vào body của request trước khi tạo buổi học
        const lessonData = { ...req.body };

        const newLesson = await db.Lesson.create(lessonData, { transaction: t });

        // Lấy thông tin lớp học để gửi thông báo
        const classInfo = await db.Class.findByPk(lessonData.classId);

        const students = await db.StudentClassStatus.findAll({
            where: {
                classId: newLesson.classId,
                status: 'JS',
            },
            attributes: ['studentId'],
            transaction: t,
        });

        await t.commit();

        // Gửi thông báo cho tất cả học sinh trong lớp
        if (classInfo) {
            try {
                // Lấy Socket.IO instance từ app
                // const io = req.app.get('io');

                // Tạo URL để học sinh có thể truy cập vào buổi học
                const actionUrl = `/class/${classInfo.class_code}/learning`;

                // Gửi thông báo
                await notificationService.createNotificationsForUsers(
                    students.map(s => s.studentId),
                    {
                        title: "Buổi học mới đã được thêm",
                        content: `Buổi học "${newLesson.name}" đã được thêm vào lớp "${classInfo.name}"`,
                        type: 'LESSON',
                        relatedId: lessonData.id,
                        relatedType: 'LESSON',
                        actionUrl: actionUrl,
                        isRead: false
                    }
                );
                // await sendClassNotification(
                //     io,
                //     lessonData.classId,
                //     "Buổi học mới đã được thêm",
                //     `Buổi học "${newLesson.name}" đã được thêm vào lớp "${classInfo.name}"`,
                //     'LESSON',
                //     actionUrl
                // );
            } catch (notificationError) {
                console.error('Lỗi khi gửi thông báo:', notificationError);
                // Không ảnh hưởng đến kết quả trả về nếu gửi thông báo thất bại
            }
        }

        return res.status(201).json({
            message: "Tạo buổi học mới thành công!",
            data: newLesson,
        });
    } catch (error) {
        await t.rollback();

        return res.status(500).json({
            message: "Lỗi khi tạo buổi học",
            error: error.message,
        });
    }
};


export const changeLesson = async (req, res) => {
    const id = req.params.id
    const lesson = await db.Lesson.findOne({ where: { id } })
    if (!lesson) {
        return res.status(404).json({
            message: `Không tìm thấy buổi học với ID: ${id}!`
        })
    }
    await lesson.update(req.body)
    return res.status(200).json({
        message: 'Cập nhật thông tin buổi học thành công!',
    })
}

export const deleteLesson = async (req, res) => {
    const t = await db.sequelize.transaction();

    try {
        // Lấy ID của lesson cần xóa
        const { lessonId } = req.params;

        // Tìm buổi học cần xóa
        const lessonToDelete = await db.Lesson.findOne({
            where: { id: lessonId },
            transaction: t,
        });

        if (!lessonToDelete) {
            throw new Error("Không tìm thấy buổi học để xóa.");
        }

        // Xóa buổi học
        await lessonToDelete.destroy({ transaction: t });

        // Commit transaction
        await t.commit();

        return res.status(200).json({
            message: "Xóa buổi học thành công và cập nhật!",
        });
    } catch (error) {
        // Rollback transaction nếu có lỗi
        await t.rollback();

        return res.status(500).json({
            message: "Lỗi khi xóa buổi học hoặc cập nhật.",
            error: error.message,
        });
    }
};

