{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@reduxjs/toolkit": "^2.6.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@uiw/react-markdown-preview": "^5.1.3", "@uiw/react-md-editor": "^4.0.5", "axios": "^1.7.9", "chart.js": "^4.4.9", "core-js": "^3.41.0", "date-fns": "^4.1.0", "framer-motion": "^12.6.2", "html2canvas": "^1.4.1", "katex": "^0.16.21", "lucide-react": "^0.476.0", "pdfjs-dist": "^3.4.120", "react": "^18.3.1", "react-dom": "^18.3.1", "react-internet-meter": "^1.1.1", "react-katex": "^3.0.1", "react-qr-code": "^2.0.15", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "react-to-pdf": "^1.0.1", "react-to-print": "^3.0.5", "recharts": "^2.15.1", "regenerator-runtime": "^0.14.1", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "safari >= 12", "ios >= 12", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "safari >= 12", "ios >= 12"]}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-named-capturing-groups-regex": "^7.25.9", "@babel/preset-env": "^7.26.9", "autoprefixer": "^10.4.20", "babel-loader": "^10.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}