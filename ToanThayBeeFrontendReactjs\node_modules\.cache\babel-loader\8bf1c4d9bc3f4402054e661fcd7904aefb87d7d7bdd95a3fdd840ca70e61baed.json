{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from \"src/features/tuition/tuitionSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    page,\n    totalPages,\n    total,\n    pageSize\n  } = useSelector(state => state.tuition.pagination);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho thống kê\n  const [showStatistics, setShowStatistics] = useState(false);\n  const [statisticsData, setStatisticsData] = useState(null);\n  const [statisticsLoading, setStatisticsLoading] = useState(false);\n\n  // State cho bộ lọc\n  // const [filterMonth, setFilterMonth] = useState(\"\");\n  // const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\n  // const [filterOverdue, setFilterOverdue] = useState(\"\");\n  // const [filterClass, setFilterClass] = useState(\"\");\n  // const [filterClassId, setFilterClassId] = useState(\"\");\n\n  const {\n    filterMonth,\n    filterIsPaid,\n    filterOverdue,\n    filterClass,\n    filterClassId\n  } = useSelector(state => state.tuition);\n  const setFilterMonth = month => {\n    dispatch(setFilterMonthSlice(month));\n  };\n  const setFilterIsPaid = isPaid => {\n    dispatch(setFilterIsPaidSlice(isPaid));\n  };\n  const setFilterOverdue = overdue => {\n    dispatch(setFilterOverdueSlice(overdue));\n  };\n  const setFilterClass = classValue => {\n    dispatch(setFilterClassSlice(classValue));\n  };\n  const setFilterClassId = classId => {\n    dispatch(setFilterClassIdSlice(classId));\n  };\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\n  const [addNote, setAddNote] = useState(\"\");\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editDueDate, setEditDueDate] = useState(\"\");\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\n  const [editNote, setEditNote] = useState(\"\");\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n\n  // useEffect(() => {\n  //   if (!didInit) {\n  //     dispatch(resetFilters());\n  //     setDidInit(true);\n  //   }\n  // }, [dispatch, didInit]);\n\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      isPaid: filterIsPaid,\n      // Thay đổi từ status thành isPaid\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        isPaid: addIsPaid,\n        // Sử dụng isPaid thay vì status\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddIsPaid(false);\n      setAddNote(\"\");\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  useEffect(() => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: page,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      isPaid: filterIsPaid,\n      // Thay đổi từ status thành isPaid\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, page, pageSize]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : \"\");\n      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status\n      setEditNote(payment.note || \"\");\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddIsPaid(false); // Reset isPaid thay vì status\n    setAddNote(\"\");\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditPaymentDate(\"\");\n    setEditDueDate(\"\");\n    setEditIsPaid(false); // Reset isPaid thay vì status\n    setEditNote(\"\");\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate due date (required)\n    if (!editDueDate) {\n      errors.editDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        isPaid: editIsPaid,\n        // Sử dụng isPaid thay vì status\n        note: editNote,\n        dueDate: editDueDate\n      };\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        isPaid: filterIsPaid,\n        // Sử dụng isPaid thay vì status\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n\n  // Hàm xử lý thao tác nhanh đánh dấu đã thanh toán\n  const handleQuickPayment = async (paymentId, currentIsPaid) => {\n    try {\n      const today = new Date().toISOString().split('T')[0]; // Ngày hiện tại\n\n      const paymentData = {\n        isPaid: !currentIsPaid,\n        // Toggle trạng thái\n        paymentDate: !currentIsPaid ? today : null // Nếu đánh dấu đã thanh toán thì set ngày hiện tại, nếu bỏ đánh dấu thì xóa ngày\n      };\n      await dispatch(updateTuitionPayment({\n        id: paymentId,\n        paymentData\n      }));\n\n      // Refresh data\n      // dispatch(fetchTuitionPayments({\n      //   page: page,\n      //   pageSize,\n      //   search: inputValue,\n      //   sortOrder: \"DESC\",\n      //   isPaid: filterIsPaid,\n      //   month: filterMonth,\n      //   overdue: filterOverdue,\n      //   userClass: filterClass\n      // }));\n    } catch (error) {\n      console.error(\"Error updating payment status:\", error);\n    }\n  };\n\n  // Hàm xử lý hiển thị thống kê\n  const handleShowStatistics = async () => {\n    setStatisticsLoading(true);\n    try {\n      // Tính toán thống kê từ dữ liệu hiện tại\n      const totalStudents = tuitionPayments.length;\n      const paidStudents = tuitionPayments.filter(payment => payment.isPaid).length;\n      const unpaidStudents = totalStudents - paidStudents;\n      const paymentRate = totalStudents > 0 ? (paidStudents / totalStudents * 100).toFixed(1) : 0;\n\n      // Thống kê theo tháng\n      const monthlyStats = {};\n      tuitionPayments.forEach(payment => {\n        const month = payment.month;\n        if (!monthlyStats[month]) {\n          monthlyStats[month] = {\n            month: month,\n            monthFormatted: payment.monthFormatted,\n            total: 0,\n            paid: 0,\n            unpaid: 0\n          };\n        }\n        monthlyStats[month].total++;\n        if (payment.isPaid) {\n          monthlyStats[month].paid++;\n        } else {\n          monthlyStats[month].unpaid++;\n        }\n      });\n\n      // Thống kê theo lớp\n      const classStats = {};\n      tuitionPayments.forEach(payment => {\n        var _payment$user;\n        const userClass = ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.class) || 'Không xác định';\n        if (!classStats[userClass]) {\n          classStats[userClass] = {\n            class: userClass,\n            total: 0,\n            paid: 0,\n            unpaid: 0\n          };\n        }\n        classStats[userClass].total++;\n        if (payment.isPaid) {\n          classStats[userClass].paid++;\n        } else {\n          classStats[userClass].unpaid++;\n        }\n      });\n      setStatisticsData({\n        overview: {\n          totalStudents,\n          paidStudents,\n          unpaidStudents,\n          paymentRate\n        },\n        monthlyStats: Object.values(monthlyStats),\n        classStats: Object.values(classStats)\n      });\n      setShowStatistics(true);\n    } catch (error) {\n      console.error(\"Error calculating statistics:\", error);\n    } finally {\n      setStatisticsLoading(false);\n    }\n  };\n\n  // getStatusBadge function removed - replaced with inline status display\n\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 668,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 674,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 680,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 686,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterIsPaid,\n              onChange: e => setFilterIsPaid(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 7\n    }, this), tuitionPayments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 816,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"(S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i c\\u1EE7a ph\\u1EE5 huynh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"(S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i c\\u1EE7a h\\u1ECDc sinh)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ghi ch\\xFA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user2, _payment$user3, _payment$user4, _payment$user5, _payment$user6, _payment$user7;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (page - 1) * pageSize + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.lastName) + \" \" + ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user5 = payment.user) === null || _payment$user5 === void 0 ? void 0 : _payment$user5.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4 align-top\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col gap-y-0.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_payment$user6 = payment.user) === null || _payment$user6 === void 0 ? void 0 : _payment$user6.phone) || \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_payment$user7 = payment.user) === null || _payment$user7 === void 0 ? void 0 : _payment$user7.password) || \"Không có mật khẩu\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(payment.isPaid ? 'bg-green-100 text-green-800' : payment.isOverdue ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                  children: payment.isPaid ? 'Đã thanh toán' : payment.isOverdue ? 'Quá hạn' : 'Chưa thanh toán'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  title: payment.note,\n                  children: payment.note ? payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2 items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center cursor-pointer\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: payment.isPaid,\n                      onChange: () => handleQuickPayment(payment.id, payment.isPaid),\n                      className: \"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer\",\n                      title: payment.isPaid ? \"Bỏ đánh dấu đã thanh toán\" : \"Đánh dấu đã thanh toán\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 896,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleView(payment.id, payment.userId, payment.month),\n                      className: \"text-blue-500 hover:text-blue-700\",\n                      title: \"Xem chi ti\\u1EBFt\",\n                      children: /*#__PURE__*/_jsxDEV(Eye, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 912,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 907,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEdit(payment.id),\n                      className: \"text-yellow-500 hover:text-yellow-700\",\n                      title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                      children: /*#__PURE__*/_jsxDEV(Edit, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 919,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDelete(payment.id),\n                      className: \"text-red-500 hover:text-red-700\",\n                      title: \"X\\xF3a\",\n                      children: /*#__PURE__*/_jsxDEV(Trash, {\n                        size: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 926,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 19\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 820,\n      columnNumber: 9\n    }, this), viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: page,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: total,\n        limit: pageSize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 939,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 960,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1022,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"addIsPaid\",\n                  checked: addIsPaid,\n                  onChange: e => setAddIsPaid(e.target.checked),\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"addIsPaid\",\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1061,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 973,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1092,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1091,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1097,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1104,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1111,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1096,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1117,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1126,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1115,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1132,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1130,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1080,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1166,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editDueDate,\n                onChange: e => setEditDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1167,\n                columnNumber: 21\n              }, this), formErrors.editDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1175,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1174,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Tr\\u1EA1ng th\\xE1i thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1180,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"editIsPaid\",\n                  checked: editIsPaid,\n                  onChange: e => setEditIsPaid(e.target.checked),\n                  className: \"mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1182,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"editIsPaid\",\n                  className: \"text-sm text-gray-700\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1181,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1195,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1196,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1194,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1206,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1209,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1155,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1152,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1223,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1236,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1237,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1238,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1235,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1230,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1246,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1248,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1248,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1249,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1249,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1250,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1250,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1251,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1251,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1247,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1245,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1257,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1259,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1259,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1260,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1261,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1261,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1262,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1262,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1264,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1265,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1263,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1276,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1276,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1277,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1277,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1278,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1278,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1258,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1256,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1290,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1291,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1289,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1295,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1295,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1296,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1296,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1297,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1297,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1286,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1303,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1284,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1312,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1313,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1311,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1316,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1317,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1318,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1319,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1315,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1310,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1309,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1228,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1326,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1336,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1221,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 968,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 952,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1346,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 692,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"V7Isoyd76mT6tv3+TzHLWIIauKg=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "AdminLayout", "FunctionBarAdmin", "Chart", "setFilterClassIdSlice", "setFilterMonthSlice", "setFilterIsPaidSlice", "setFilterOverdueSlice", "setFilterClassSlice", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "page", "totalPages", "total", "pageSize", "pagination", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "showStatistics", "setShowStatistics", "statisticsData", "setStatisticsData", "statisticsLoading", "setStatisticsLoading", "filterMonth", "filterIsPaid", "filterOverdue", "filterClass", "filterClassId", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "month", "setFilterIsPaid", "isPaid", "setFilterOverdue", "overdue", "setFilterClass", "classValue", "setFilterClassId", "classId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addIsPaid", "setAddIsPaid", "addNote", "setAddNote", "editId", "setEditId", "editPaymentDate", "setEditPaymentDate", "editDueDate", "setEditDueDate", "editIsPaid", "setEditIsPaid", "editNote", "setEditNote", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "sortOrder", "userClass", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "dueDate", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "error", "console", "submit", "handleEdit", "response", "unwrap", "payment", "data", "Date", "toISOString", "split", "handleView", "handleAdd", "today", "formattedDate", "year", "getFullYear", "getMonth", "formattedMonth", "concat", "handleBatchAdd", "handleCreateBatchTuition", "validateEditForm", "handleUpdateTuitionPayment", "validateBatchTuitionForm", "isNaN", "Number", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "expectedAmount", "handleDelete", "confirmDelete", "cancelDelete", "handleQuickPayment", "paymentId", "currentIsPaid", "handleShowStatistics", "totalStudents", "paidStudents", "filter", "unpaidStudents", "paymentRate", "toFixed", "monthlyStats", "for<PERSON>ach", "monthFormatted", "paid", "unpaid", "classStats", "_payment$user", "user", "class", "overview", "values", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "className", "children", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "type", "placeholder", "value", "onChange", "target", "Array", "from", "_", "i", "monthStr", "selectedClassId", "onSelect", "onClear", "strokeWidth", "map", "index", "_payment$user2", "_payment$user3", "_payment$user4", "_payment$user5", "_payment$user6", "_payment$user7", "highSchool", "phone", "password", "toLocaleDateString", "isOverdue", "title", "substring", "checked", "currentPage", "onPageChange", "totalItems", "limit", "onSubmit", "role", "htmlFor", "rows", "disabled", "required", "ref", "paidAmount", "status", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage } from \"src/features/tuition/tuitionSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\nimport { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from \"src/features/tuition/tuitionSlice\";\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { page, totalPages, total, pageSize } = useSelector(\r\n    (state) => state.tuition.pagination\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho thống kê\r\n  const [showStatistics, setShowStatistics] = useState(false);\r\n  const [statisticsData, setStatisticsData] = useState(null);\r\n  const [statisticsLoading, setStatisticsLoading] = useState(false);\r\n\r\n  // State cho bộ lọc\r\n  // const [filterMonth, setFilterMonth] = useState(\"\");\r\n  // const [filterIsPaid, setFilterIsPaid] = useState(\"\"); // Thay đổi từ filterStatus thành filterIsPaid\r\n  // const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  // const [filterClass, setFilterClass] = useState(\"\");\r\n  // const [filterClassId, setFilterClassId] = useState(\"\");\r\n\r\n  const { filterMonth, filterIsPaid, filterOverdue, filterClass, filterClassId } = useSelector(\r\n    (state) => state.tuition\r\n  );\r\n\r\n  const setFilterMonth = (month) => {\r\n    dispatch(setFilterMonthSlice(month));\r\n  };\r\n\r\n  const setFilterIsPaid = (isPaid) => {\r\n    dispatch(setFilterIsPaidSlice(isPaid));\r\n  };\r\n\r\n  const setFilterOverdue = (overdue) => {\r\n    dispatch(setFilterOverdueSlice(overdue));\r\n  };\r\n\r\n  const setFilterClass = (classValue) => {\r\n    dispatch(setFilterClassSlice(classValue));\r\n  };\r\n\r\n  const setFilterClassId = (classId) => {\r\n    dispatch(setFilterClassIdSlice(classId));\r\n  };\r\n\r\n\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid\r\n  const [addNote, setAddNote] = useState(\"\");\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editDueDate, setEditDueDate] = useState(\"\");\r\n  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid\r\n  const [editNote, setEditNote] = useState(\"\");\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  // useEffect(() => {\r\n  //   if (!didInit) {\r\n  //     dispatch(resetFilters());\r\n  //     setDidInit(true);\r\n  //   }\r\n  // }, [dispatch, didInit]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        isPaid: addIsPaid, // Sử dụng isPaid thay vì status\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddIsPaid(false);\r\n      setAddNote(\"\");\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: page, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, page, pageSize]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : \"\");\r\n      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status\r\n      setEditNote(payment.note || \"\");\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddIsPaid(false); // Reset isPaid thay vì status\r\n    setAddNote(\"\");\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditPaymentDate(\"\");\r\n    setEditDueDate(\"\");\r\n    setEditIsPaid(false); // Reset isPaid thay vì status\r\n    setEditNote(\"\");\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate due date (required)\r\n    if (!editDueDate) {\r\n      errors.editDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        isPaid: editIsPaid, // Sử dụng isPaid thay vì status\r\n        note: editNote,\r\n        dueDate: editDueDate\r\n      };\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        isPaid: filterIsPaid, // Sử dụng isPaid thay vì status\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  // Hàm xử lý thao tác nhanh đánh dấu đã thanh toán\r\n  const handleQuickPayment = async (paymentId, currentIsPaid) => {\r\n    try {\r\n      const today = new Date().toISOString().split('T')[0]; // Ngày hiện tại\r\n\r\n      const paymentData = {\r\n        isPaid: !currentIsPaid, // Toggle trạng thái\r\n        paymentDate: !currentIsPaid ? today : null, // Nếu đánh dấu đã thanh toán thì set ngày hiện tại, nếu bỏ đánh dấu thì xóa ngày\r\n      };\r\n\r\n      await dispatch(updateTuitionPayment({\r\n        id: paymentId,\r\n        paymentData\r\n      }));\r\n\r\n      // Refresh data\r\n      // dispatch(fetchTuitionPayments({\r\n      //   page: page,\r\n      //   pageSize,\r\n      //   search: inputValue,\r\n      //   sortOrder: \"DESC\",\r\n      //   isPaid: filterIsPaid,\r\n      //   month: filterMonth,\r\n      //   overdue: filterOverdue,\r\n      //   userClass: filterClass\r\n      // }));\r\n    } catch (error) {\r\n      console.error(\"Error updating payment status:\", error);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý hiển thị thống kê\r\n  const handleShowStatistics = async () => {\r\n    setStatisticsLoading(true);\r\n    try {\r\n      // Tính toán thống kê từ dữ liệu hiện tại\r\n      const totalStudents = tuitionPayments.length;\r\n      const paidStudents = tuitionPayments.filter(payment => payment.isPaid).length;\r\n      const unpaidStudents = totalStudents - paidStudents;\r\n      const paymentRate = totalStudents > 0 ? ((paidStudents / totalStudents) * 100).toFixed(1) : 0;\r\n\r\n      // Thống kê theo tháng\r\n      const monthlyStats = {};\r\n      tuitionPayments.forEach(payment => {\r\n        const month = payment.month;\r\n        if (!monthlyStats[month]) {\r\n          monthlyStats[month] = {\r\n            month: month,\r\n            monthFormatted: payment.monthFormatted,\r\n            total: 0,\r\n            paid: 0,\r\n            unpaid: 0\r\n          };\r\n        }\r\n        monthlyStats[month].total++;\r\n        if (payment.isPaid) {\r\n          monthlyStats[month].paid++;\r\n        } else {\r\n          monthlyStats[month].unpaid++;\r\n        }\r\n      });\r\n\r\n      // Thống kê theo lớp\r\n      const classStats = {};\r\n      tuitionPayments.forEach(payment => {\r\n        const userClass = payment.user?.class || 'Không xác định';\r\n        if (!classStats[userClass]) {\r\n          classStats[userClass] = {\r\n            class: userClass,\r\n            total: 0,\r\n            paid: 0,\r\n            unpaid: 0\r\n          };\r\n        }\r\n        classStats[userClass].total++;\r\n        if (payment.isPaid) {\r\n          classStats[userClass].paid++;\r\n        } else {\r\n          classStats[userClass].unpaid++;\r\n        }\r\n      });\r\n\r\n      setStatisticsData({\r\n        overview: {\r\n          totalStudents,\r\n          paidStudents,\r\n          unpaidStudents,\r\n          paymentRate\r\n        },\r\n        monthlyStats: Object.values(monthlyStats),\r\n        classStats: Object.values(classStats)\r\n      });\r\n\r\n      setShowStatistics(true);\r\n    } catch (error) {\r\n      console.error(\"Error calculating statistics:\", error);\r\n    } finally {\r\n      setStatisticsLoading(false);\r\n    }\r\n  };\r\n\r\n  // getStatusBadge function removed - replaced with inline status display\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterIsPaid}\r\n                onChange={(e) => setFilterIsPaid(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"true\">Đã thanh toán</option>\r\n                <option value=\"false\">Chưa thanh toán</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {tuitionPayments.length === 0 ? (\r\n        <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n          <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n            <thead className=\"bg-gray-100\">\r\n              <tr>\r\n                <th className=\"py-3 px-4 text-left\">STT</th>\r\n                <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                <th className=\"py-3 px-4 text-left\">\r\n                  <div className=\"flex flex-col\">\r\n                    <span className=\"font-medium\">Số điện thoại</span>\r\n                    <span className=\"text-xs text-gray-500\">(Số điện thoại của phụ huynh)</span>\r\n                    <span className=\"text-xs text-gray-500\">(Số điện thoại của học sinh)</span>\r\n                  </div>\r\n                </th>\r\n                <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                <th className=\"py-3 px-4 text-left\">Ghi chú</th>\r\n                <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {tuitionPayments.map((payment, index) => (\r\n                <tr\r\n                  key={payment.id}\r\n                  className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                >\r\n                  <td className=\"py-3 px-4\">\r\n                    {(page - 1) * pageSize + index + 1}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                  <td className=\"py-3 px-4 align-top\">\r\n                    <div className=\"flex flex-col gap-y-0.5\">\r\n                      <span>{payment.user?.phone || \"N/A\"}</span>\r\n                      <span>{payment.user?.password || \"Không có mật khẩu\"}</span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {payment.paymentDate\r\n                      ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                      : \"Chưa thanh toán\"}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${payment.isPaid\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : payment.isOverdue\r\n                        ? 'bg-red-100 text-red-800'\r\n                        : 'bg-yellow-100 text-yellow-800'\r\n                      }`}>\r\n                      {payment.isPaid\r\n                        ? 'Đã thanh toán'\r\n                        : payment.isOverdue\r\n                          ? 'Quá hạn'\r\n                          : 'Chưa thanh toán'\r\n                      }\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <span className=\"text-sm text-gray-600\" title={payment.note}>\r\n                      {payment.note ? (payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note) : '-'}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"py-3 px-4\">\r\n                    <div className=\"flex space-x-2 items-center\">\r\n                      {/* Quick payment checkbox */}\r\n                      <div className=\"flex items-center cursor-pointer\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={payment.isPaid}\r\n                          onChange={() => handleQuickPayment(payment.id, payment.isPaid)}\r\n                          className=\"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer\"\r\n                          title={payment.isPaid ? \"Bỏ đánh dấu đã thanh toán\" : \"Đánh dấu đã thanh toán\"}\r\n                        />\r\n                      </div>\r\n\r\n                      {/* Action buttons */}\r\n                      <div className=\"flex space-x-1\">\r\n                        <button\r\n                          onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                          className=\"text-blue-500 hover:text-blue-700\"\r\n                          title=\"Xem chi tiết\"\r\n                        >\r\n                          <Eye size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleEdit(payment.id)}\r\n                          className=\"text-yellow-500 hover:text-yellow-700\"\r\n                          title=\"Chỉnh sửa\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(payment.id)}\r\n                          className=\"text-red-500 hover:text-red-700\"\r\n                          title=\"Xóa\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={page}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={total}\r\n            limit={pageSize}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  {/* Removed expectedAmount and paidAmount fields - no longer needed */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái thanh toán</label>\r\n                    <div className=\"flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"addIsPaid\"\r\n                        checked={addIsPaid}\r\n                        onChange={(e) => setAddIsPaid(e.target.checked)}\r\n                        className=\"mr-2\"\r\n                      />\r\n                      <label htmlFor=\"addIsPaid\" className=\"text-sm text-gray-700\">\r\n                        Đã thanh toán\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editDueDate}\r\n                      onChange={(e) => setEditDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.editDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái thanh toán</label>\r\n                    <div className=\"flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"editIsPaid\"\r\n                        checked={editIsPaid}\r\n                        onChange={(e) => setEditIsPaid(e.target.checked)}\r\n                        className=\"mr-2\"\r\n                      />\r\n                      <label htmlFor=\"editIsPaid\" className=\"text-sm text-gray-700\">\r\n                        Đã thanh toán\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,QACvB,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACzH,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AACjC,SAASC,qBAAqB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjK,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAGpD,WAAW,CAAC,CAAC;EAC9B,MAAMqD,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoD,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAGzD,WAAW,CAAE0D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,IAAI;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAG/D,WAAW,CACtD0D,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACK,UAC3B,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC5D;EACA,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM;IAAEoF,WAAW;IAAEC,YAAY;IAAEC,aAAa;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGrF,WAAW,CACzF0D,KAAK,IAAKA,KAAK,CAACC,OACnB,CAAC;EAED,MAAM2B,cAAc,GAAIC,KAAK,IAAK;IAChCpC,QAAQ,CAACb,mBAAmB,CAACiD,KAAK,CAAC,CAAC;EACtC,CAAC;EAED,MAAMC,eAAe,GAAIC,MAAM,IAAK;IAClCtC,QAAQ,CAACZ,oBAAoB,CAACkD,MAAM,CAAC,CAAC;EACxC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpCxC,QAAQ,CAACX,qBAAqB,CAACmD,OAAO,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrC1C,QAAQ,CAACV,mBAAmB,CAACoD,UAAU,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC5C,QAAQ,CAACd,qBAAqB,CAAC0D,OAAO,CAAC,CAAC;EAC1C,CAAC;EAGD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACqG,cAAc,EAAEC,iBAAiB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuG,cAAc,EAAEC,iBAAiB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAACyG,QAAQ,EAAEC,WAAW,CAAC,GAAG1G,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC2G,UAAU,EAAEC,aAAa,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6G,QAAQ,EAAEC,WAAW,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM+G,eAAe,GAAG9G,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM+G,aAAa,GAAG/G,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMgH,oBAAoB,GAAGhH,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMiH,kBAAkB,GAAGjH,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACkH,UAAU,EAAEC,aAAa,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqH,WAAW,EAAEC,cAAc,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACyH,YAAY,EAAEC,eAAe,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2H,UAAU,EAAEC,aAAa,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6H,SAAS,EAAEC,YAAY,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+H,UAAU,EAAEC,aAAa,CAAC,GAAGhI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGlI,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACmI,SAAS,EAAEC,YAAY,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACuI,QAAQ,EAAEC,WAAW,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyI,cAAc,EAAEC,iBAAiB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2I,UAAU,EAAEC,aAAa,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6I,SAAS,EAAEC,YAAY,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC+I,OAAO,EAAEC,UAAU,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACiJ,MAAM,EAAEC,SAAS,CAAC,GAAGlJ,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACmJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqJ,WAAW,EAAEC,cAAc,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuJ,UAAU,EAAEC,aAAa,CAAC,GAAGxJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAACyJ,QAAQ,EAAEC,WAAW,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM,CAAC2J,WAAW,EAAEC,cAAc,CAAC,GAAG5J,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9J,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC+J,WAAW,EAAEC,cAAc,CAAC,GAAGhK,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiK,aAAa,EAAEC,gBAAgB,CAAC,GAAGlK,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMmK,mBAAmB,GAAGlK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA;EACA;EACA;EACA;EACA;;EAEAF,SAAS,CAAC,MAAM;IACduD,QAAQ,CAAClC,WAAW,CAAC,EAAE,CAAC,CAAC;IACzBkC,QAAQ,CAACnC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAACmC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM8G,iBAAiB,GAAIC,SAAS,IAAK;IACvCpE,gBAAgB,CAACoE,SAAS,CAACC,EAAE,CAAC;IAC9BlE,kBAAkB,CAACiE,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCvE,gBAAgB,CAAC,EAAE,CAAC;IACpBG,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAIC,QAAQ,IAAK;IACrCpE,iBAAiB,CAACoE,QAAQ,CAACJ,EAAE,CAAC;IAC9B9D,iBAAiB,CAACkE,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCvE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzBxH,QAAQ,CACN7C,oBAAoB,CAAC;MACnBsK,MAAM,EAAE3G,UAAU;MAClBL,IAAI,EAAE,CAAC;MAAE;MACTG,QAAQ;MACR8G,SAAS,EAAE,MAAM;MACjBpF,MAAM,EAAEP,YAAY;MAAE;MACtBK,KAAK,EAAEN,WAAW;MAClBU,OAAO,EAAER,aAAa;MACtB2F,SAAS,EAAE1F,WAAW;MACtBW,OAAO,EAAEV;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAM0F,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAClD,SAAS,EAAE;MACdkD,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAAC/C,QAAQ,EAAE;MACb8C,MAAM,CAAC3F,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC6F,IAAI,CAAChD,QAAQ,CAAC,EAAE;MAC1C8C,MAAM,CAAC3F,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAACiD,UAAU,EAAE;MACf0C,MAAM,CAACG,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAIC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM0D,WAAW,GAAG;QAClBN,MAAM,EAAEnD,SAAS;QACjBzC,KAAK,EAAE6C,QAAQ;QACf3C,MAAM,EAAEiD,SAAS;QAAE;QACnBgD,IAAI,EAAE9C,OAAO;QACbyC,OAAO,EAAE7C;MACX,CAAC;;MAED;MACA,IAAIF,cAAc,EAAE;QAClBmD,WAAW,CAACE,WAAW,GAAGrD,cAAc;MAC1C;;MAEA;MACA,MAAMnF,QAAQ,CAAC3C,oBAAoB,CAACiL,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjBzI,QAAQ,CAAC7C,oBAAoB,CAAC;QAC5BsD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACR6G,MAAM,EAAE3G,UAAU;QAClB4G,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA5C,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,KAAK,CAAC;MACnBE,UAAU,CAAC,EAAE,CAAC;IAEhB,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDnI,SAAS,CAAC,MAAM;IACduD,QAAQ,CACN7C,oBAAoB,CAAC;MACnBsK,MAAM,EAAE3G,UAAU;MAClBL,IAAI,EAAEA,IAAI;MAAE;MACZG,QAAQ;MACR8G,SAAS,EAAE,MAAM;MACjBpF,MAAM,EAAEP,YAAY;MAAE;MACtBK,KAAK,EAAEN,WAAW;MAClBU,OAAO,EAAER,aAAa;MACtB2F,SAAS,EAAE1F;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAACjC,QAAQ,EAAES,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAE9B,MAAMiI,UAAU,GAAG,MAAO7B,EAAE,IAAK;IAC/BpC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMkE,QAAQ,GAAG,MAAM9I,QAAQ,CAACvC,4BAA4B,CAACuJ,EAAE,CAAC,CAAC,CAAC+B,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAACG,IAAI;;MAE7B;MACArD,SAAS,CAACoB,EAAE,CAAC;MACblB,kBAAkB,CAACkD,OAAO,CAACR,WAAW,GAAG,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAACW,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxGpD,cAAc,CAACgD,OAAO,CAACd,OAAO,GAAG,IAAIgB,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAACiB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MAC5FlD,aAAa,CAAC8C,OAAO,CAAC1G,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC;MACxC8D,WAAW,CAAC4C,OAAO,CAACT,IAAI,IAAI,EAAE,CAAC;;MAE/B;MACAhH,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOqH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACR9D,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMyE,UAAU,GAAG,MAAAA,CAAOrC,EAAE,EAAEgB,MAAM,EAAE5F,KAAK,KAAK;IAC9CsE,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM1G,QAAQ,CAACvC,4BAA4B,CAACuJ,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACAzF,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOqH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRhC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM4C,SAAS,GAAGA,CAAA,KAAM;IACtB;IACAxE,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACrBE,UAAU,CAAC,EAAE,CAAC;IACdhB,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAM6E,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC;IACxB,MAAMM,aAAa,GAAGD,KAAK,CAACJ,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD9D,aAAa,CAACkE,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;IAChC,MAAMtH,KAAK,GAAGmH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAC,MAAA,CAAMJ,IAAI,OAAAI,MAAA,CAAIzH,KAAK,GAAG,EAAE,OAAAyH,MAAA,CAAOzH,KAAK,IAAKA,KAAK,CAAE;IACpE8C,WAAW,CAAC0E,cAAc,CAAC;;IAE3B;IACArI,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyI,cAAc,GAAGA,CAAA,KAAM;IAC3BvI,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0I,wBAAwB,GAAGA,CAAA,KAAM;IACrCxI,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EACD,MAAMoH,eAAe,GAAGA,CAAA,KAAM;IAC5BpH,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACAuC,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBoB,SAAS,CAAC,IAAI,CAAC;IACfE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACtBE,WAAW,CAAC,EAAE,CAAC;IACf;IACAE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1B9B,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtB8B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMjC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAChC,WAAW,EAAE;MAChBgC,MAAM,CAAChC,WAAW,GAAG,oCAAoC;IAC3D;IAEA,OAAOgC,MAAM;EACf,CAAC;;EAED;EACA,MAAMkC,0BAA0B,GAAG,MAAOpC,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAGiC,gBAAgB,CAAC,CAAC;IACjC,IAAI7B,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM0D,WAAW,GAAG;QAClBhG,MAAM,EAAE2D,UAAU;QAAE;QACpBsC,IAAI,EAAEpC,QAAQ;QACd+B,OAAO,EAAEnC;MACX,CAAC;;MAED;MACA,IAAIF,eAAe,EAAE;QACnByC,WAAW,CAACE,WAAW,GAAG3C,eAAe;MAC3C;;MAEA;MACA,MAAM7F,QAAQ,CAACxC,oBAAoB,CAAC;QAClCwJ,EAAE,EAAErB,MAAM;QACV2C;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjBzI,QAAQ,CAAC7C,oBAAoB,CAAC;QAC5BsD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACR6G,MAAM,EAAE3G,UAAU;QAClB4G,SAAS,EAAE,MAAM;QACjBpF,MAAM,EAAEP,YAAY;QAAE;QACtBK,KAAK,EAAEN,WAAW;QAClBU,OAAO,EAAER,aAAa;QACtB2F,SAAS,EAAE1F;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOyG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsF,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMnC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAClE,UAAU,EAAE;MACfkE,MAAM,CAAClE,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAACoE,IAAI,CAACpE,UAAU,CAAC,EAAE;MAC5CkE,MAAM,CAAClE,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAKoG,KAAK,CAACpG,WAAW,CAAC,IAAIqG,MAAM,CAACrG,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEgE,MAAM,CAAChE,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACf0D,MAAM,CAAC1D,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjB4D,MAAM,CAAC5D,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAO4D,MAAM;EACf,CAAC;;EAED;EACA,MAAMsC,wBAAwB,GAAG,MAAOxC,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMwC,iBAAiB,GAAGrG,oBAAoB,GAAGrG,kBAAkB,CAACqG,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACsG,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAMvC,MAAM,GAAGmC,wBAAwB,CAAC,CAAC;IACzC,IAAI/B,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC3D,aAAa,CAACqD,MAAM,CAAC;MACrB;IACF;IAEAnD,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM2F,SAAS,GAAG;QAChBnI,KAAK,EAAEyB,UAAU;QACjBqE,OAAO,EAAE/D,YAAY;QACrBE,UAAU;QACVkE,IAAI,EAAEhE;MACR,CAAC;;MAED;MACA,IAAI+F,iBAAiB,EAAE;QACrBC,SAAS,CAACC,cAAc,GAAGJ,MAAM,CAACE,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAMtK,QAAQ,CAAC1C,0BAA0B,CAACiN,SAAS,CAAC,CAAC;;MAErD;MACA9B,eAAe,CAAC,CAAC;MACjBzI,QAAQ,CAAC7C,oBAAoB,CAAC;QAC5BsD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACR6G,MAAM,EAAE3G,UAAU;QAClB4G,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DhE,aAAa,CAAC;QAAEkE,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACRhE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM6F,YAAY,GAAIzD,EAAE,IAAK;IAC3B7F,kBAAkB,CAAC6F,EAAE,CAAC;IACtB/F,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMyJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC1K,QAAQ,CAAC5C,oBAAoB,CAAC8D,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM0J,YAAY,GAAGA,CAAA,KAAM;IACzB1J,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMyJ,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,aAAa,KAAK;IAC7D,IAAI;MACF,MAAMvB,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtD,MAAMd,WAAW,GAAG;QAClBhG,MAAM,EAAE,CAACwI,aAAa;QAAE;QACxBtC,WAAW,EAAE,CAACsC,aAAa,GAAGvB,KAAK,GAAG,IAAI,CAAE;MAC9C,CAAC;MAED,MAAMvJ,QAAQ,CAACxC,oBAAoB,CAAC;QAClCwJ,EAAE,EAAE6D,SAAS;QACbvC;MACF,CAAC,CAAC,CAAC;;MAEH;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvClJ,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF;MACA,MAAMmJ,aAAa,GAAG9K,eAAe,CAACmI,MAAM;MAC5C,MAAM4C,YAAY,GAAG/K,eAAe,CAACgL,MAAM,CAAClC,OAAO,IAAIA,OAAO,CAAC1G,MAAM,CAAC,CAAC+F,MAAM;MAC7E,MAAM8C,cAAc,GAAGH,aAAa,GAAGC,YAAY;MACnD,MAAMG,WAAW,GAAGJ,aAAa,GAAG,CAAC,GAAG,CAAEC,YAAY,GAAGD,aAAa,GAAI,GAAG,EAAEK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;MAE7F;MACA,MAAMC,YAAY,GAAG,CAAC,CAAC;MACvBpL,eAAe,CAACqL,OAAO,CAACvC,OAAO,IAAI;QACjC,MAAM5G,KAAK,GAAG4G,OAAO,CAAC5G,KAAK;QAC3B,IAAI,CAACkJ,YAAY,CAAClJ,KAAK,CAAC,EAAE;UACxBkJ,YAAY,CAAClJ,KAAK,CAAC,GAAG;YACpBA,KAAK,EAAEA,KAAK;YACZoJ,cAAc,EAAExC,OAAO,CAACwC,cAAc;YACtC7K,KAAK,EAAE,CAAC;YACR8K,IAAI,EAAE,CAAC;YACPC,MAAM,EAAE;UACV,CAAC;QACH;QACAJ,YAAY,CAAClJ,KAAK,CAAC,CAACzB,KAAK,EAAE;QAC3B,IAAIqI,OAAO,CAAC1G,MAAM,EAAE;UAClBgJ,YAAY,CAAClJ,KAAK,CAAC,CAACqJ,IAAI,EAAE;QAC5B,CAAC,MAAM;UACLH,YAAY,CAAClJ,KAAK,CAAC,CAACsJ,MAAM,EAAE;QAC9B;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrBzL,eAAe,CAACqL,OAAO,CAACvC,OAAO,IAAI;QAAA,IAAA4C,aAAA;QACjC,MAAMjE,SAAS,GAAG,EAAAiE,aAAA,GAAA5C,OAAO,CAAC6C,IAAI,cAAAD,aAAA,uBAAZA,aAAA,CAAcE,KAAK,KAAI,gBAAgB;QACzD,IAAI,CAACH,UAAU,CAAChE,SAAS,CAAC,EAAE;UAC1BgE,UAAU,CAAChE,SAAS,CAAC,GAAG;YACtBmE,KAAK,EAAEnE,SAAS;YAChBhH,KAAK,EAAE,CAAC;YACR8K,IAAI,EAAE,CAAC;YACPC,MAAM,EAAE;UACV,CAAC;QACH;QACAC,UAAU,CAAChE,SAAS,CAAC,CAAChH,KAAK,EAAE;QAC7B,IAAIqI,OAAO,CAAC1G,MAAM,EAAE;UAClBqJ,UAAU,CAAChE,SAAS,CAAC,CAAC8D,IAAI,EAAE;QAC9B,CAAC,MAAM;UACLE,UAAU,CAAChE,SAAS,CAAC,CAAC+D,MAAM,EAAE;QAChC;MACF,CAAC,CAAC;MAEF/J,iBAAiB,CAAC;QAChBoK,QAAQ,EAAE;UACRf,aAAa;UACbC,YAAY;UACZE,cAAc;UACdC;QACF,CAAC;QACDE,YAAY,EAAEnD,MAAM,CAAC6D,MAAM,CAACV,YAAY,CAAC;QACzCK,UAAU,EAAExD,MAAM,CAAC6D,MAAM,CAACL,UAAU;MACtC,CAAC,CAAC;MAEFlK,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOiH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR7G,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;;EAEA,IAAIzB,OAAO,EAAE;IACX,oBAAOZ,OAAA,CAACxB,cAAc;MAAAiO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACE9M,OAAA;MACEiN,OAAO,EAAEA,OAAQ;MACjBC,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJJ,IAAI,eACL/M,OAAA;QAAAmN,QAAA,EAAOH;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMQ,OAAO,gBACXpN,OAAA;IAAK,wBAAgB;IAACkN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnN,OAAA,CAACnB,IAAI;MAACwO,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMU,SAAS,gBACbtN,OAAA;IAAK,wBAAgB;IAACkN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnN,OAAA,CAACjB,QAAQ;MAACsO,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMW,YAAY,gBAChBvN,OAAA;IAAK,wBAAgB;IAACkN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnN,OAAA,CAAChB,QAAQ;MAACqO,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMY,SAAS,gBACbxN,OAAA;IAAK,wBAAgB;IAACkN,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxCnN,OAAA,CAACf,KAAK;MAACoO,IAAI,EAAE;IAAG;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,oBACE5M,OAAA,CAACT,WAAW;IAAA4N,QAAA,gBACVnN,OAAA;MAAKkN,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAEN5M,OAAA;MAAKkN,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFnN,OAAA;QAAKkN,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBnN,OAAA;UAAKkN,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDnN,OAAA;YAAKkN,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCnN,OAAA;cACEyN,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXX,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnEnN,OAAA;gBACE8N,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5M,OAAA;cACEkO,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kEAAwC;cACpDC,KAAK,EAAE9M,UAAW;cAClB+M,QAAQ,EAAGhG,CAAC,IAAK9G,aAAa,CAAC8G,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;cAC/ClB,SAAS,EAAC;YAAsI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5M,OAAA;YAAKkN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnN,OAAA;cACEkN,SAAS,EAAC,oDAAoD;cAC9DkB,KAAK,EAAE9L,WAAY;cACnB+L,QAAQ,EAAGhG,CAAC,IAAK1F,cAAc,CAAC0F,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;cAAAjB,QAAA,gBAEhDnN,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9B2B,KAAK,CAACC,IAAI,CAAC;gBAAE3F,MAAM,EAAE;cAAG,CAAC,EAAE,CAAC4F,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAM9L,KAAK,GAAG8L,CAAC,GAAG,CAAC;gBACnB,MAAMzE,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;gBACrC,MAAMyE,QAAQ,GAAG/L,KAAK,GAAG,EAAE,OAAAyH,MAAA,CAAOzH,KAAK,OAAAyH,MAAA,CAAQzH,KAAK,CAAE;gBACtD,oBACE5C,OAAA;kBAAoBoO,KAAK,KAAA/D,MAAA,CAAKJ,IAAI,OAAAI,MAAA,CAAIsE,QAAQ,CAAG;kBAAAxB,QAAA,cAAA9C,MAAA,CACrCzH,KAAK,OAAAyH,MAAA,CAAIJ,IAAI;gBAAA,GADZrH,KAAK;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5M,OAAA;YAAKkN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnN,OAAA;cACEkN,SAAS,EAAC,oDAAoD;cAC9DkB,KAAK,EAAE7L,YAAa;cACpB8L,QAAQ,EAAGhG,CAAC,IAAKxF,eAAe,CAACwF,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;cAAAjB,QAAA,gBAEjDnN,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC5M,OAAA;gBAAQoO,KAAK,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAa;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C5M,OAAA;gBAAQoO,KAAK,EAAC,OAAO;gBAAAjB,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5M,OAAA;YAAKkN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnN,OAAA;cACEkN,SAAS,EAAC,oDAAoD;cAC9DkB,KAAK,EAAE5L,aAAc;cACrB6L,QAAQ,EAAGhG,CAAC,IAAKtF,gBAAgB,CAACsF,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;cAAAjB,QAAA,gBAElDnN,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC5M,OAAA;gBAAQoO,KAAK,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC5M,OAAA;gBAAQoO,KAAK,EAAC,OAAO;gBAAAjB,QAAA,EAAC;cAAY;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN5M,OAAA;YAAKkN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnN,OAAA;cACEkN,SAAS,EAAC,oDAAoD;cAC9DkB,KAAK,EAAE3L,WAAY;cACnB4L,QAAQ,EAAGhG,CAAC,IAAKpF,cAAc,CAACoF,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;cAAAjB,QAAA,gBAEhDnN,OAAA;gBAAQoO,KAAK,EAAC,EAAE;gBAAAjB,QAAA,EAAC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B5M,OAAA;gBAAQoO,KAAK,EAAC,IAAI;gBAAAjB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC5M,OAAA;gBAAQoO,KAAK,EAAC,IAAI;gBAAAjB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC5M,OAAA;gBAAQoO,KAAK,EAAC,IAAI;gBAAAjB,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5M,OAAA;YAAKkN,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBnN,OAAA,CAACvC,gBAAgB;cACf2Q,KAAK,EAAE/K,eAAgB;cACvBuL,eAAe,EAAElM,aAAc;cAC/B2L,QAAQ,EAAE/K,kBAAmB;cAC7BuL,QAAQ,EAAEvH,iBAAkB;cAC5BwH,OAAO,EAAEpH,yBAA0B;cACnCyG,WAAW,EAAC;YAAqB;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5M,OAAA;YACEiN,OAAO,EAAEjF,YAAa;YACtBkF,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3FnN,OAAA;cAAKkN,SAAS,EAAC,cAAc;cAACW,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAN,QAAA,eACpHnN,OAAA;gBAAMgO,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACc,WAAW,EAAC,GAAG;gBAACjB,CAAC,EAAC;cAA6C;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CAAC,eAEN5M,OAAA;UAAKkN,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnN,OAAA,CAAC6M,sBAAsB;YAACE,IAAI,EAAEK,OAAQ;YAACJ,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAEnD;UAAU;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtF5M,OAAA,CAAC6M,sBAAsB;YAACE,IAAI,EAAEQ,YAAa;YAACP,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAE1C;UAAyB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlM,eAAe,CAACmI,MAAM,KAAK,CAAC,gBAC3B7I,OAAA;MAAKkN,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DnN,OAAA;QAAGkN,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,gBAEN5M,OAAA;MAAKkN,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BnN,OAAA;QAAOkN,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzEnN,OAAA;UAAOkN,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5BnN,OAAA;YAAAmN,QAAA,gBACEnN,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eACjCnN,OAAA;gBAAKkN,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BnN,OAAA;kBAAMkN,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAa;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClD5M,OAAA;kBAAMkN,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA6B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5E5M,OAAA;kBAAMkN,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD5M,OAAA;cAAIkN,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR5M,OAAA;UAAAmN,QAAA,EACGzM,eAAe,CAACsO,GAAG,CAAC,CAACxF,OAAO,EAAEyF,KAAK;YAAA,IAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClCvP,OAAA;cAEEkN,SAAS,EAAE+B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAA9B,QAAA,gBAEvDnN,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAAClM,IAAI,GAAG,CAAC,IAAIG,QAAQ,GAAG6N,KAAK,GAAG;cAAC;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAA+B,cAAA,GAAA1F,OAAO,CAAC6C,IAAI,cAAA6C,cAAA,uBAAZA,cAAA,CAAcpH,QAAQ,IAAG,GAAG,KAAAqH,cAAA,GAAG3F,OAAO,CAAC6C,IAAI,cAAA8C,cAAA,uBAAZA,cAAA,CAActH,SAAS,KAAI;cAAK;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAiC,cAAA,GAAA5F,OAAO,CAAC6C,IAAI,cAAA+C,cAAA,uBAAZA,cAAA,CAAc9C,KAAK,KAAI;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAkC,cAAA,GAAA7F,OAAO,CAAC6C,IAAI,cAAAgD,cAAA,uBAAZA,cAAA,CAAcG,UAAU,KAAI;cAAK;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClE5M,OAAA;gBAAIkN,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eACjCnN,OAAA;kBAAKkN,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnN,OAAA;oBAAAmN,QAAA,EAAO,EAAAmC,cAAA,GAAA9F,OAAO,CAAC6C,IAAI,cAAAiD,cAAA,uBAAZA,cAAA,CAAcG,KAAK,KAAI;kBAAK;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3C5M,OAAA;oBAAAmN,QAAA,EAAO,EAAAoC,cAAA,GAAA/F,OAAO,CAAC6C,IAAI,cAAAkD,cAAA,uBAAZA,cAAA,CAAcG,QAAQ,KAAI;kBAAmB;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE3D,OAAO,CAACwC;cAAc;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvD5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB3D,OAAO,CAACR,WAAW,GAChB,IAAIU,IAAI,CAACF,OAAO,CAACR,WAAW,CAAC,CAAC2G,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAIzD,IAAI,CAACF,OAAO,CAACd,OAAO,CAAC,CAACiH,kBAAkB,CAAC,OAAO;cAAC;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBnN,OAAA;kBAAMkN,SAAS,gDAAA7C,MAAA,CAAgDb,OAAO,CAAC1G,MAAM,GACzE,6BAA6B,GAC7B0G,OAAO,CAACoG,SAAS,GACf,yBAAyB,GACzB,+BAA+B,CAChC;kBAAAzC,QAAA,EACF3D,OAAO,CAAC1G,MAAM,GACX,eAAe,GACf0G,OAAO,CAACoG,SAAS,GACf,SAAS,GACT;gBAAiB;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBnN,OAAA;kBAAMkN,SAAS,EAAC,uBAAuB;kBAAC2C,KAAK,EAAErG,OAAO,CAACT,IAAK;kBAAAoE,QAAA,EACzD3D,OAAO,CAACT,IAAI,GAAIS,OAAO,CAACT,IAAI,CAACF,MAAM,GAAG,EAAE,GAAGW,OAAO,CAACT,IAAI,CAAC+G,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGtG,OAAO,CAACT,IAAI,GAAI;gBAAG;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL5M,OAAA;gBAAIkN,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBnN,OAAA;kBAAKkN,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAE1CnN,OAAA;oBAAKkN,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,eAC/CnN,OAAA;sBACEkO,IAAI,EAAC,UAAU;sBACf6B,OAAO,EAAEvG,OAAO,CAAC1G,MAAO;sBACxBuL,QAAQ,EAAEA,CAAA,KAAMjD,kBAAkB,CAAC5B,OAAO,CAAChC,EAAE,EAAEgC,OAAO,CAAC1G,MAAM,CAAE;sBAC/DoK,SAAS,EAAC,6GAA6G;sBACvH2C,KAAK,EAAErG,OAAO,CAAC1G,MAAM,GAAG,2BAA2B,GAAG;oBAAyB;sBAAA2J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAGN5M,OAAA;oBAAKkN,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BnN,OAAA;sBACEiN,OAAO,EAAEA,CAAA,KAAMpD,UAAU,CAACL,OAAO,CAAChC,EAAE,EAAEgC,OAAO,CAAChB,MAAM,EAAEgB,OAAO,CAAC5G,KAAK,CAAE;sBACrEsK,SAAS,EAAC,mCAAmC;sBAC7C2C,KAAK,EAAC,mBAAc;sBAAA1C,QAAA,eAEpBnN,OAAA,CAAClB,GAAG;wBAACuO,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACT5M,OAAA;sBACEiN,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACG,OAAO,CAAChC,EAAE,CAAE;sBACtC0F,SAAS,EAAC,uCAAuC;sBACjD2C,KAAK,EAAC,qBAAW;sBAAA1C,QAAA,eAEjBnN,OAAA,CAACrB,IAAI;wBAAC0O,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACT5M,OAAA;sBACEiN,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACzB,OAAO,CAAChC,EAAE,CAAE;sBACxC0F,SAAS,EAAC,iCAAiC;sBAC3C2C,KAAK,EAAC,QAAK;sBAAA1C,QAAA,eAEXnN,OAAA,CAACpB,KAAK;wBAACyO,IAAI,EAAE;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GApFApD,OAAO,CAAChC,EAAE;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqFb,CAAC;UAAA,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAjJ,QAAQ,KAAK,OAAO,iBACnB3D,OAAA;MAAKkN,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBnN,OAAA,CAACvB,UAAU;QACTuR,WAAW,EAAE/O,IAAK;QAClBgP,YAAY,EAAGhP,IAAI,IAAKT,QAAQ,CAACjC,cAAc,CAAC0C,IAAI,CAAC,CAAE;QACvDiP,UAAU,EAAE/O,KAAM;QAClBgP,KAAK,EAAE/O;MAAS;QAAAqL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAIAhL,cAAc,iBACb5B,OAAA;MAAKkN,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpFnN,OAAA;QAAKkN,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EnN,OAAA;UAAIkN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClCrL,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACL5M,OAAA;UACEiN,OAAO,EAAEhE,eAAgB;UACzBiE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CnN,OAAA,CAACd,CAAC;YAACmO,IAAI,EAAE;UAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5M,OAAA;QAAAmN,QAAA,GACGrL,cAAc,KAAK,KAAK,iBACvB9B,OAAA;UAAKkN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnN,OAAA;YAAGkN,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvE5M,OAAA;YAAMkN,SAAS,EAAC,WAAW;YAACkD,QAAQ,EAAEhI,sBAAuB;YAAA+E,QAAA,gBAC3DnN,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxH5M,OAAA,CAACtC,eAAe;gBACd0Q,KAAK,EAAE7I,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1BgJ,QAAQ,EAAE7I,oBAAqB;gBAC/BqJ,QAAQ,EAAGxC,IAAI,IAAK;kBAClB/G,YAAY,CAAC+G,IAAI,CAAC7E,EAAE,CAAC;kBACrBhC,oBAAoB,IAAA6E,MAAA,CAAIgC,IAAI,CAACvE,QAAQ,OAAAuC,MAAA,CAAIgC,IAAI,CAACxE,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACFiH,OAAO,EAAEA,CAAA,KAAM;kBACbxJ,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACF2I,WAAW,EAAC,mCAAsB;gBAClCkC,IAAI,EAAC;cAAS;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACD3H,UAAU,CAACuD,MAAM,iBAChBxI,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACuD,MAAM;cAAA;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH5M,OAAA;gBACEkO,IAAI,EAAC,OAAO;gBACZhB,SAAS,6BAAA7C,MAAA,CAA6BpF,UAAU,CAACrC,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3GwL,KAAK,EAAE3I,QAAS;gBAChB4I,QAAQ,EAAGhG,CAAC,IAAK3C,WAAW,CAAC2C,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACD3H,UAAU,CAACrC,KAAK,iBACf5C,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACrC,KAAK;cAAA;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF5M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXhB,SAAS,EAAC,oDAAoD;gBAC9DkB,KAAK,EAAEzI,cAAe;gBACtB0I,QAAQ,EAAGhG,CAAC,IAAKzC,iBAAiB,CAACyC,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H5M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXhB,SAAS,6BAAA7C,MAAA,CAA6BpF,UAAU,CAACyD,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7G0F,KAAK,EAAEvI,UAAW;gBAClBwI,QAAQ,EAAGhG,CAAC,IAAKvC,aAAa,CAACuC,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACD3H,UAAU,CAACyD,OAAO,iBACjB1I,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACyD,OAAO;cAAA;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7F5M,OAAA;gBAAKkN,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CnN,OAAA;kBACEkO,IAAI,EAAC,UAAU;kBACf1G,EAAE,EAAC,WAAW;kBACduI,OAAO,EAAEhK,SAAU;kBACnBsI,QAAQ,EAAGhG,CAAC,IAAKrC,YAAY,CAACqC,CAAC,CAACiG,MAAM,CAACyB,OAAO,CAAE;kBAChD7C,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACF5M,OAAA;kBAAOsQ,OAAO,EAAC,WAAW;kBAACpD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E5M,OAAA;gBACEkN,SAAS,EAAC,oDAAoD;gBAC9DqD,IAAI,EAAC,GAAG;gBACRpC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEnI,OAAQ;gBACfoI,QAAQ,EAAGhG,CAAC,IAAKnC,UAAU,CAACmC,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACL3H,UAAU,CAACmE,MAAM,iBAChBpJ,OAAA;cAAKkN,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjGnN,OAAA,CAACb,WAAW;gBAACkO,IAAI,EAAE,EAAG;gBAACH,SAAS,EAAC;cAA2B;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D5M,OAAA;gBAAAmN,QAAA,EAAIlI,UAAU,CAACmE;cAAM;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACD5M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uHAAuH;cACjIsD,QAAQ,EAAErL,YAAa;cAAAgI,QAAA,EAEtBhI,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACA9K,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAKkN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnN,OAAA;YAAGkN,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElF5M,OAAA;YAAMkN,SAAS,EAAC,WAAW;YAACkD,QAAQ,EAAG/H,CAAC,IAAKwC,wBAAwB,CAACxC,CAAC,CAAE;YAAA8E,QAAA,gBACvEnN,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH5M,OAAA;gBACEkO,IAAI,EAAC,OAAO;gBACZhB,SAAS,6BAAA7C,MAAA,CAA6BpF,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH+J,KAAK,EAAE/J,UAAW;gBAClBgK,QAAQ,EAAGhG,CAAC,IAAK/D,aAAa,CAAC+D,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACD3H,UAAU,CAACZ,UAAU,iBACpBrE,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACZ,UAAU;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnH5M,OAAA;gBACEkN,SAAS,6BAAA7C,MAAA,CAA6BpF,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHuJ,KAAK,EAAEvJ,UAAW;gBAClBwJ,QAAQ,EAAGhG,CAAC,IAAKvD,aAAa,CAACuD,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;gBAC/CqC,QAAQ;gBAAAtD,QAAA,gBAERnN,OAAA;kBAAQoO,KAAK,EAAC,EAAE;kBAAAjB,QAAA,EAAC;gBAAQ;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC5M,OAAA;kBAAQoO,KAAK,EAAC,IAAI;kBAAAjB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC5M,OAAA;kBAAQoO,KAAK,EAAC,IAAI;kBAAAjB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC5M,OAAA;kBAAQoO,KAAK,EAAC,IAAI;kBAAAjB,QAAA,EAAC;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACR3H,UAAU,CAACJ,UAAU,iBACpB7E,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACJ,UAAU;cAAA;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H5M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXhB,SAAS,6BAAA7C,MAAA,CAA6BpF,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClHyJ,KAAK,EAAEzJ,YAAa;gBACpB0J,QAAQ,EAAGhG,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACiG,MAAM,CAACF,KAAK,CAAE;gBACjDqC,QAAQ;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACD3H,UAAU,CAACN,YAAY,iBACtB3E,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACN,YAAY;cAAA;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E5M,OAAA;gBACEkN,SAAS,EAAC,oDAAoD;gBAC9DqD,IAAI,EAAC,GAAG;gBACRpC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAErJ,SAAU;gBACjBsJ,QAAQ,EAAGhG,CAAC,IAAKrD,YAAY,CAACqD,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN5M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uHAAuH;cACjIsD,QAAQ,EAAErL,YAAa;cAAAgI,QAAA,EAEtBhI,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA9K,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAKkN,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBnN,OAAA;YAAGkN,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/D5M,OAAA;YAAMkN,SAAS,EAAC,WAAW;YAACkD,QAAQ,EAAG/H,CAAC,IAAKoC,0BAA0B,CAACpC,CAAC,CAAE;YAAA8E,QAAA,gBACzEnN,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF5M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXhB,SAAS,EAAC,oDAAoD;gBAC9DkB,KAAK,EAAE/H,eAAgB;gBACvBgI,QAAQ,EAAGhG,CAAC,IAAK/B,kBAAkB,CAAC+B,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAAnN,OAAA;kBAAMkN,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H5M,OAAA;gBACEkO,IAAI,EAAC,MAAM;gBACXhB,SAAS,6BAAA7C,MAAA,CAA6BpF,UAAU,CAACsB,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjH6H,KAAK,EAAE7H,WAAY;gBACnB8H,QAAQ,EAAGhG,CAAC,IAAK7B,cAAc,CAAC6B,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,EACD3H,UAAU,CAACsB,WAAW,iBACrBvG,OAAA;gBAAGkN,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxDnN,OAAA,CAACb,WAAW;kBAACkO,IAAI,EAAE,EAAG;kBAACH,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACsB,WAAW;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7F5M,OAAA;gBAAKkN,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CnN,OAAA;kBACEkO,IAAI,EAAC,UAAU;kBACf1G,EAAE,EAAC,YAAY;kBACfuI,OAAO,EAAEtJ,UAAW;kBACpB4H,QAAQ,EAAGhG,CAAC,IAAK3B,aAAa,CAAC2B,CAAC,CAACiG,MAAM,CAACyB,OAAO,CAAE;kBACjD7C,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACF5M,OAAA;kBAAOsQ,OAAO,EAAC,YAAY;kBAACpD,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAE9D;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5M,OAAA;cAAAmN,QAAA,gBACEnN,OAAA;gBAAOkN,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E5M,OAAA;gBACEkN,SAAS,EAAC,oDAAoD;gBAC9DqD,IAAI,EAAC,GAAG;gBACRpC,WAAW,EAAC,uCAAuB;gBACnCC,KAAK,EAAEzH,QAAS;gBAChB0H,QAAQ,EAAGhG,CAAC,IAAKzB,WAAW,CAACyB,CAAC,CAACiG,MAAM,CAACF,KAAK;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACL3H,UAAU,CAACmE,MAAM,iBAChBpJ,OAAA;cAAGkN,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDnN,OAAA,CAACb,WAAW;gBAACkO,IAAI,EAAE,EAAG;gBAACH,SAAS,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAAC3H,UAAU,CAACmE,MAAM;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACD5M,OAAA;cACEkO,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uHAAuH;cACjIsD,QAAQ,EAAErL,YAAa;cAAAgI,QAAA,EAEtBhI,YAAY,GAAG,eAAe,GAAG;YAAc;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA9K,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAAmN,QAAA,EACGlG,WAAW,gBACVjH,OAAA;YAAKkN,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDnN,OAAA,CAACxB,cAAc;cAAAiO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJ/L,cAAc,gBAChBb,OAAA;YAAKkN,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnN,OAAA;cAAK0Q,GAAG,EAAErJ,mBAAoB;cAAC6F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1EnN,OAAA;gBAAKkN,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnEnN,OAAA;kBAAKkN,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnN,OAAA;oBAAKkN,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN5M,OAAA;oBAAAmN,QAAA,gBACEnN,OAAA;sBAAIkN,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClE5M,OAAA;sBAAGkN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpE5M,OAAA;sBAAGkN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAIzD,IAAI,CAAC,CAAC,CAACiG,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGN5M,OAAA;gBAAKkN,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnN,OAAA;kBAAIkN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClE5M,OAAA;kBAAKkN,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnN,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAAzM,oBAAA,GAACU,cAAc,CAACwL,IAAI,cAAAlM,oBAAA,uBAAnBA,oBAAA,CAAqB2H,QAAQ,EAAC,GAAC,GAAA1H,qBAAA,GAACS,cAAc,CAACwL,IAAI,cAAAjM,qBAAA,uBAAnBA,qBAAA,CAAqByH,SAAS;kBAAA;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpH5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAvM,qBAAA,GAAAQ,cAAc,CAACwL,IAAI,cAAAhM,qBAAA,uBAAnBA,qBAAA,CAAqBoP,KAAK,KAAI,UAAU;kBAAA;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrG5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAtM,qBAAA,GAAAO,cAAc,CAACwL,IAAI,cAAA/L,qBAAA,uBAAnBA,qBAAA,CAAqBgM,KAAK,KAAI,UAAU;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3F5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAArM,qBAAA,GAAAM,cAAc,CAACwL,IAAI,cAAA9L,qBAAA,uBAAnBA,qBAAA,CAAqBiP,UAAU,KAAI,UAAU;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5M,OAAA;gBAAAmN,QAAA,gBACEnN,OAAA;kBAAIkN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE5M,OAAA;kBAAKkN,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvEnN,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC/L,cAAc,CAACmL,cAAc;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClF5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC1O,cAAc,CAAC2C,cAAc,CAACmK,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClH5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC1O,cAAc,CAAC2C,cAAc,CAAC8P,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7G5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC1O,cAAc,CAAC,CAAC2C,cAAc,CAACmK,cAAc,IAAI,CAAC,KAAKnK,cAAc,CAAC8P,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9I5M,OAAA;oBAAAmN,QAAA,gBACEnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrD5M,OAAA;sBAAMkN,SAAS,oCAAA7C,MAAA,CAAoCxJ,cAAc,CAAC+P,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjH/P,cAAc,CAAC+P,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5D/P,cAAc,CAAC+P,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAAzD,QAAA,EACFtM,cAAc,CAAC+P,MAAM,KAAK,MAAM,GAAG,eAAe,GACjD/P,cAAc,CAAC+P,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpD/P,cAAc,CAAC+P,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJ5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC/L,cAAc,CAACmI,WAAW,GAAG,IAAIU,IAAI,CAAC7I,cAAc,CAACmI,WAAW,CAAC,CAAC2G,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzK5M,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC/L,cAAc,CAAC6H,OAAO,GAAG,IAAIgB,IAAI,CAAC7I,cAAc,CAAC6H,OAAO,CAAC,CAACiH,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJ/L,cAAc,CAACkI,IAAI,iBAAI/I,OAAA;oBAAAmN,QAAA,gBAAGnN,OAAA;sBAAMkN,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC/L,cAAc,CAACkI,IAAI;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL9L,yBAAyB,IAAIA,yBAAyB,CAAC+P,aAAa,IAAI/P,yBAAyB,CAAC+P,aAAa,CAAChI,MAAM,GAAG,CAAC,iBACzH7I,OAAA;gBAAAmN,QAAA,gBACEnN,OAAA;kBAAIkN,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzE5M,OAAA;kBAAKkN,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBrM,yBAAyB,CAAC+P,aAAa,CAAC7B,GAAG,CAAEhO,OAAO,iBACnDhB,OAAA;oBAAsBkN,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9EnN,OAAA;sBAAKkN,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDnN,OAAA;wBAAIkN,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAEnM,OAAO,CAACkM;sBAAS;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpD5M,OAAA;wBAAMkN,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAACnM,OAAO,CAAC8P,UAAU;sBAAA;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN5M,OAAA;sBAAAmN,QAAA,gBAAGnN,OAAA;wBAAMkN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC1O,cAAc,CAAC8C,OAAO,CAAC+P,MAAM,CAAC;oBAAA;sBAAAtE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/F5M,OAAA;sBAAAmN,QAAA,gBAAGnN,OAAA;wBAAMkN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAIlD,IAAI,CAAC1I,OAAO,CAACgQ,QAAQ,CAAC,CAACrB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrH5L,OAAO,CAAC+H,IAAI,iBAAI/I,OAAA;sBAAAmN,QAAA,gBAAGnN,OAAA;wBAAMkN,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC5L,OAAO,CAAC+H,IAAI;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtF5L,OAAO,CAACwG,EAAE;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5M,OAAA;kBAAKkN,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CnN,OAAA;oBAAGkN,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAACjP,cAAc,CAAC4C,yBAAyB,CAACmQ,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjH5M,OAAA;oBAAGkN,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAACjP,cAAc,CAAC2C,cAAc,CAACmK,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGD5M,OAAA;gBAAKkN,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjCnN,OAAA;kBAAKkN,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDnN,OAAA;oBAAAmN,QAAA,gBACEnN,OAAA;sBAAGkN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClE5M,OAAA;sBAAGkN,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACN5M,OAAA;oBAAKkN,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BnN,OAAA;sBAAGkN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD5M,OAAA;sBAAKkN,SAAS,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5B5M,OAAA;sBAAGkN,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD5M,OAAA;sBAAGkN,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGP5M,OAAA;cAAKkN,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCnN,OAAA;gBACEiN,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAACxI,cAAc,CAAC2G,EAAE,CAAE;gBAC7C0F,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN5M,OAAA;YAAKkN,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED5M,OAAA,CAACtB,YAAY;MACXwS,MAAM,EAAE1P,gBAAiB;MACzB2P,SAAS,EAAEjG,aAAc;MACzB8B,IAAI,EAAC,qGAAmD;MACxDoE,OAAO,EAAEjG;IAAa;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAC1M,EAAA,CA5yCID,kBAAkB;EAAA,QACL7C,WAAW,EACXE,WAAW,EACuED,WAAW,EAEhEA,WAAW,EAsBwBA,WAAW;AAAA;AAAAgU,EAAA,GA3BxFpR,kBAAkB;AA8yCxB,eAAeA,kBAAkB;AAAC,IAAAoR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}