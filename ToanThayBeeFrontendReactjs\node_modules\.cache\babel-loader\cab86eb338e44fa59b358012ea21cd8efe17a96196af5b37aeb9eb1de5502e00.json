{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\App.js\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport LoginPage from \"./pages/LoginPage\";\nimport RegisterPage from \"./pages/RegisterPage\";\nimport Dashboard from \"./pages/Dashboard\";\nimport QuestionManagement from \"./pages/admin/question/questionManagement\";\nimport ClassManagement from \"./pages/admin/class/ClassManagement\";\nimport ExamManagement from \"./pages/admin/exam/ExamManagement\";\nimport StudentManagement from \"./pages/admin/user/StudentManagement\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport NotificationDisplay from \"./components/error/NotificationDisplay\"; // Đảm bảo file này export SuccessDisplay\nimport MaintenanceWrapper from \"./components/MaintenanceWrapper\";\nimport MaintenanceCleaner from \"./components/MaintenanceCleaner\";\nimport QuestionDetailAdmin from \"./pages/admin/question/QuestionDetailAdmin\";\nimport ExamDetailAdmin from \"./pages/admin/exam/ExamDetailAdmin\";\nimport QuestionOfExamAdmin from \"./pages/admin/exam/QuestionOfExamAdmin\";\nimport CodeManagement from \"./pages/admin/CodeManagement\";\nimport PreviewExamAdmin from \"./pages/admin/exam/PreviewExamAdmin\";\nimport StudentDetailAdmin from \"./pages/admin/user/StudentDetailAdmin\";\nimport ClassDetailAdmin from \"./pages/admin/class/ClassDetailAdmin\";\nimport Home from \"./pages/user/home/<USER>\";\nimport PracticePage from \"./pages/user/practice/PracticePage\";\nimport ExamDetailPage from \"./pages/user/practice/ExamDetail\";\nimport DoExamPage from \"./pages/user/practice/DoExamPage\";\nimport PreviewExamPage from \"./pages/user/practice/PreviewExam\";\nimport RankingPage from \"./pages/user/practice/RankingPage\";\nimport HistoryDoExamPage from \"./pages/user/practice/HistoryDoExamPage\";\nimport ScorePage from \"./pages/user/practice/ScorePage\";\nimport ClassUserPage from \"./pages/user/class/ClassUserPage\";\nimport ClassDetailPage from \"./pages/user/class/ClassDetailPage\";\nimport LearningPage from \"./pages/user/class/LearningPage\";\nimport ClassUserManagement from \"./pages/admin/class/ClassUserManagement\";\nimport LessonManagement from \"./pages/admin/class/LessonManagement\";\nimport UserClassManagement from \"./pages/admin/user/UserClassManagement\";\nimport TrackingPage from \"./pages/admin/exam/TrackingExamAdmin\";\nimport ArticlePostPage from \"./pages/admin/ArticlePostPage\";\nimport ArticleManagement from \"./pages/admin/ArticleManagement\";\nimport ArticlePage from \"./pages/user/article/ArticlePage\";\nimport ArticleListPage from \"./pages/user/article/ArticleListPage\";\nimport HomePageManagement from \"./pages/admin/HomePageManagement\";\nimport AchievementManagement from \"./pages/admin/achievement/AchievementManagement\";\nimport OverViewPage from \"./pages/user/home/<USER>\";\nimport SpinnerDemo from \"./components/loading/SpinnerDemo\";\nimport QuestionReportManagement from \"./pages/admin/QuestionReportManagement\";\nimport NotificationsPage from \"./pages/user/notifications/NotificationsPage\";\nimport TuitionPaymentList from \"./pages/admin/tuition/TuitionPaymentList\";\nimport UserTuitionPayments from \"./pages/user/tuition/UserTuitionPayments\";\nimport UserTuitionPaymentDetail from \"./pages/user/tuition/UserTuitionPaymentDetail\";\nimport AttendancePage from \"./pages/admin/class/AttendancePage\";\nimport UserAttendancePage from \"./pages/user/attendance/UserAttendancePage\";\nimport AdminUserSearchPage from \"./pages/admin/attendance/AdminUserSearchPage\";\nimport AdminMobileAttendancePage from \"./pages/admin/attendance/AdminMobileAttendancePage\";\nimport AllAchievementsPage from \"./pages/user/achievements/AllAchievementsPage\";\nimport AllFeaturesPage from \"./pages/user/features/AllFeaturesPage\";\nimport AllSchedulePage from \"./pages/user/schedule/AllSchedulePage\";\nimport ScrollToTop from \"./components/ScrollToTop\";\n\n// import TestPage from \"./pages/TestPage\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(MaintenanceWrapper, {\n      children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(MaintenanceCleaner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(NotificationDisplay, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/achievements\",\n          element: /*#__PURE__*/_jsxDEV(AllAchievementsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/features\",\n          element: /*#__PURE__*/_jsxDEV(AllFeaturesPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/schedule\",\n          element: /*#__PURE__*/_jsxDEV(AllSchedulePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 37\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice\",\n            element: /*#__PURE__*/_jsxDEV(PracticePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice/exam/:examId\",\n            element: /*#__PURE__*/_jsxDEV(ExamDetailPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice/exam/:examId/do\",\n            element: /*#__PURE__*/_jsxDEV(DoExamPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 74\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice/exam/:examId/preview\",\n            element: /*#__PURE__*/_jsxDEV(PreviewExamPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 79\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice/exam/:examId/ranking\",\n            element: /*#__PURE__*/_jsxDEV(RankingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 79\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice/exam/:examId/history\",\n            element: /*#__PURE__*/_jsxDEV(HistoryDoExamPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 79\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/practice/exam/attempt/:attemptId/score\",\n            element: /*#__PURE__*/_jsxDEV(ScorePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 88\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/class\",\n            element: /*#__PURE__*/_jsxDEV(ClassUserPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/class/:classCode\",\n            element: /*#__PURE__*/_jsxDEV(ClassDetailPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/class/:classCode/learning\",\n            element: /*#__PURE__*/_jsxDEV(LearningPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 75\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/articles\",\n            element: /*#__PURE__*/_jsxDEV(ArticleListPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/articles/:id\",\n            element: /*#__PURE__*/_jsxDEV(ArticlePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/overview\",\n            element: /*#__PURE__*/_jsxDEV(OverViewPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/notifications\",\n            element: /*#__PURE__*/_jsxDEV(NotificationsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 63\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/tuition-payments\",\n            element: /*#__PURE__*/_jsxDEV(UserTuitionPayments, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/tuition-payment/:id\",\n            element: /*#__PURE__*/_jsxDEV(UserTuitionPaymentDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 69\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/attendance\",\n            element: /*#__PURE__*/_jsxDEV(UserAttendancePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            allowedRoles: [\"AD\", \"AS\", \"GV\"]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 37\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/question-management\",\n            element: /*#__PURE__*/_jsxDEV(QuestionManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 75\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/question-management/:questionId\",\n            element: /*#__PURE__*/_jsxDEV(QuestionDetailAdmin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 87\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/class-management\",\n            element: /*#__PURE__*/_jsxDEV(ClassManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 72\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/class-management/:classId\",\n            element: /*#__PURE__*/_jsxDEV(ClassDetailAdmin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 81\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/class-management/:classId/users\",\n            element: /*#__PURE__*/_jsxDEV(ClassUserManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 87\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/class-management/:classId/lessons\",\n            element: /*#__PURE__*/_jsxDEV(LessonManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 89\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/class-management/:classId/attendance\",\n            element: /*#__PURE__*/_jsxDEV(AttendancePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 92\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exam-management\",\n            element: /*#__PURE__*/_jsxDEV(ExamManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exam-management/:examId\",\n            element: /*#__PURE__*/_jsxDEV(ExamDetailAdmin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 79\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exam-management/:examId/questions\",\n            element: /*#__PURE__*/_jsxDEV(QuestionOfExamAdmin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 89\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exam-management/:examId/preview\",\n            element: /*#__PURE__*/_jsxDEV(PreviewExamAdmin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 87\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exam-management/:examId/tracking\",\n            element: /*#__PURE__*/_jsxDEV(TrackingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 88\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/student-management\",\n            element: /*#__PURE__*/_jsxDEV(StudentManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 74\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/student-management/:userId\",\n            element: /*#__PURE__*/_jsxDEV(StudentDetailAdmin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 82\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/student-management/:userId/classes\",\n            element: /*#__PURE__*/_jsxDEV(UserClassManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 90\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/homepage-management\",\n            element: /*#__PURE__*/_jsxDEV(HomePageManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 75\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/achievement-management\",\n            element: /*#__PURE__*/_jsxDEV(AchievementManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 78\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/question-report-management\",\n            element: /*#__PURE__*/_jsxDEV(QuestionReportManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 82\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/attendance/user/:userId\",\n            element: /*#__PURE__*/_jsxDEV(AdminMobileAttendancePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 79\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/tuition-payment\",\n            element: /*#__PURE__*/_jsxDEV(TuitionPaymentList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            allowedRoles: [\"AD\"]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 37\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/code-management\",\n            element: /*#__PURE__*/_jsxDEV(CodeManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/article-management\",\n            element: /*#__PURE__*/_jsxDEV(ArticleManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 74\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/article-post\",\n            element: /*#__PURE__*/_jsxDEV(ArticlePostPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 68\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/article-management/edit/:id\",\n            element: /*#__PURE__*/_jsxDEV(ArticlePostPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 83\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/spinner-demo\",\n            element: /*#__PURE__*/_jsxDEV(SpinnerDemo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 68\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 9\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "LoginPage", "RegisterPage", "Dashboard", "QuestionManagement", "ClassManagement", "ExamManagement", "StudentManagement", "ProtectedRoute", "NotificationDisplay", "MaintenanceWrapper", "MaintenanceCleaner", "QuestionDetailAdmin", "ExamDetailAdmin", "QuestionOfExamAdmin", "CodeManagement", "PreviewExamAdmin", "StudentDetailAdmin", "ClassDetailAdmin", "Home", "PracticePage", "ExamDetailPage", "DoExamPage", "PreviewExamPage", "RankingPage", "HistoryDoExamPage", "ScorePage", "ClassUserPage", "ClassDetailPage", "LearningPage", "ClassUserManagement", "LessonManagement", "UserClassManagement", "TrackingPage", "ArticlePostPage", "ArticleManagement", "ArticlePage", "ArticleListPage", "HomePageManagement", "AchievementManagement", "OverViewPage", "SpinnerDemo", "QuestionReportManagement", "NotificationsPage", "TuitionPaymentList", "UserTuitionPayments", "UserTuitionPaymentDetail", "AttendancePage", "UserAttendancePage", "AdminUserSearchPage", "AdminMobileAttendancePage", "AllAchievementsPage", "AllFeaturesPage", "AllSchedulePage", "ScrollToTop", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "allowedRoles", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport LoginPage from \"./pages/LoginPage\";\r\nimport RegisterPage from \"./pages/RegisterPage\";\r\nimport Dashboard from \"./pages/Dashboard\";\r\nimport QuestionManagement from \"./pages/admin/question/questionManagement\";\r\nimport ClassManagement from \"./pages/admin/class/ClassManagement\";\r\nimport ExamManagement from \"./pages/admin/exam/ExamManagement\";\r\nimport StudentManagement from \"./pages/admin/user/StudentManagement\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport NotificationDisplay from \"./components/error/NotificationDisplay\"; // Đ<PERSON><PERSON> bảo file này export SuccessDisplay\r\nimport MaintenanceWrapper from \"./components/MaintenanceWrapper\";\r\nimport MaintenanceCleaner from \"./components/MaintenanceCleaner\";\r\nimport QuestionDetailAdmin from \"./pages/admin/question/QuestionDetailAdmin\";\r\nimport ExamDetailAdmin from \"./pages/admin/exam/ExamDetailAdmin\";\r\nimport QuestionOfExamAdmin from \"./pages/admin/exam/QuestionOfExamAdmin\";\r\nimport CodeManagement from \"./pages/admin/CodeManagement\";\r\nimport PreviewExamAdmin from \"./pages/admin/exam/PreviewExamAdmin\";\r\nimport StudentDetailAdmin from \"./pages/admin/user/StudentDetailAdmin\";\r\nimport ClassDetailAdmin from \"./pages/admin/class/ClassDetailAdmin\";\r\nimport Home from \"./pages/user/home/<USER>\"\r\nimport PracticePage from \"./pages/user/practice/PracticePage\";\r\nimport ExamDetailPage from \"./pages/user/practice/ExamDetail\";\r\nimport DoExamPage from \"./pages/user/practice/DoExamPage\";\r\nimport PreviewExamPage from \"./pages/user/practice/PreviewExam\";\r\nimport RankingPage from \"./pages/user/practice/RankingPage\";\r\nimport HistoryDoExamPage from \"./pages/user/practice/HistoryDoExamPage\";\r\nimport ScorePage from \"./pages/user/practice/ScorePage\";\r\nimport ClassUserPage from \"./pages/user/class/ClassUserPage\";\r\nimport ClassDetailPage from \"./pages/user/class/ClassDetailPage\";\r\nimport LearningPage from \"./pages/user/class/LearningPage\";\r\nimport ClassUserManagement from \"./pages/admin/class/ClassUserManagement\";\r\nimport LessonManagement from \"./pages/admin/class/LessonManagement\";\r\nimport UserClassManagement from \"./pages/admin/user/UserClassManagement\";\r\nimport TrackingPage from \"./pages/admin/exam/TrackingExamAdmin\";\r\nimport ArticlePostPage from \"./pages/admin/ArticlePostPage\";\r\nimport ArticleManagement from \"./pages/admin/ArticleManagement\";\r\nimport ArticlePage from \"./pages/user/article/ArticlePage\";\r\nimport ArticleListPage from \"./pages/user/article/ArticleListPage\";\r\nimport HomePageManagement from \"./pages/admin/HomePageManagement\";\r\nimport AchievementManagement from \"./pages/admin/achievement/AchievementManagement\";\r\nimport OverViewPage from \"./pages/user/home/<USER>\";\r\nimport SpinnerDemo from \"./components/loading/SpinnerDemo\";\r\nimport QuestionReportManagement from \"./pages/admin/QuestionReportManagement\";\r\nimport NotificationsPage from \"./pages/user/notifications/NotificationsPage\";\r\nimport TuitionPaymentList from \"./pages/admin/tuition/TuitionPaymentList\";\r\nimport UserTuitionPayments from \"./pages/user/tuition/UserTuitionPayments\";\r\nimport UserTuitionPaymentDetail from \"./pages/user/tuition/UserTuitionPaymentDetail\";\r\nimport AttendancePage from \"./pages/admin/class/AttendancePage\";\r\nimport UserAttendancePage from \"./pages/user/attendance/UserAttendancePage\";\r\nimport AdminUserSearchPage from \"./pages/admin/attendance/AdminUserSearchPage\";\r\nimport AdminMobileAttendancePage from \"./pages/admin/attendance/AdminMobileAttendancePage\";\r\nimport AllAchievementsPage from \"./pages/user/achievements/AllAchievementsPage\";\r\nimport AllFeaturesPage from \"./pages/user/features/AllFeaturesPage\";\r\nimport AllSchedulePage from \"./pages/user/schedule/AllSchedulePage\";\r\nimport ScrollToTop from \"./components/ScrollToTop\";\r\n\r\n// import TestPage from \"./pages/TestPage\";\r\n\r\nfunction App() {\r\n    return (\r\n        <BrowserRouter>\r\n            <MaintenanceWrapper>\r\n                <ScrollToTop />\r\n                {/* Auto-clear maintenance mode if FORCE_DISABLE is true */}\r\n                <MaintenanceCleaner />\r\n\r\n                {/* Hiển thị lỗi toàn cục */}\r\n                <NotificationDisplay />\r\n\r\n                <Routes>\r\n                    {/* Trang công khai */}\r\n                    <Route path=\"/\" element={<Home />} />\r\n                    <Route path=\"/achievements\" element={<AllAchievementsPage />} />\r\n                    <Route path=\"/features\" element={<AllFeaturesPage />} />\r\n                    <Route path=\"/schedule\" element={<AllSchedulePage />} />\r\n                    <Route path=\"/login\" element={<LoginPage />} />\r\n\r\n                    {/* <Route path=\"/admin/test\" element={<TestPage />} /> */}\r\n\r\n                    {/* Trang cần đăng nhập */}\r\n                    <Route element={<ProtectedRoute />}>\r\n\r\n                        <Route path=\"/practice\" element={<PracticePage />} />\r\n                        <Route path=\"/practice/exam/:examId\" element={<ExamDetailPage />} />\r\n                        <Route path=\"/practice/exam/:examId/do\" element={<DoExamPage />} />\r\n                        <Route path=\"/practice/exam/:examId/preview\" element={<PreviewExamPage />} />\r\n                        <Route path=\"/practice/exam/:examId/ranking\" element={<RankingPage />} />\r\n                        <Route path=\"/practice/exam/:examId/history\" element={<HistoryDoExamPage />} />\r\n                        <Route path=\"/practice/exam/attempt/:attemptId/score\" element={<ScorePage />} />\r\n                        <Route path=\"/class\" element={<ClassUserPage />} />\r\n                        <Route path=\"/class/:classCode\" element={<ClassDetailPage />} />\r\n                        <Route path=\"/class/:classCode/learning\" element={<LearningPage />} />\r\n                        <Route path=\"/articles\" element={<ArticleListPage />} />\r\n                        <Route path=\"/articles/:id\" element={<ArticlePage />} />\r\n                        <Route path=\"/overview\" element={<OverViewPage />} />\r\n                        <Route path=\"/notifications\" element={<NotificationsPage />} />\r\n                        <Route path=\"/tuition-payments\" element={<UserTuitionPayments />} />\r\n                        <Route path=\"/tuition-payment/:id\" element={<UserTuitionPaymentDetail />} />\r\n                        <Route path=\"/attendance\" element={<UserAttendancePage />} />\r\n                    </Route>\r\n\r\n                    {/* Trang Admin chỉ dành cho người có quyền */}\r\n                    <Route element={<ProtectedRoute allowedRoles={[\"AD\", \"AS\", \"GV\"]} />}>\r\n                        <Route path=\"/register\" element={<RegisterPage />} />\r\n\r\n                        <Route path=\"/admin/question-management\" element={<QuestionManagement />} />\r\n                        <Route path=\"/admin/question-management/:questionId\" element={<QuestionDetailAdmin />} />\r\n\r\n                        <Route path=\"/admin/class-management\" element={<ClassManagement />} />\r\n                        <Route path=\"/admin/class-management/:classId\" element={<ClassDetailAdmin />} />\r\n                        <Route path=\"/admin/class-management/:classId/users\" element={<ClassUserManagement />} />\r\n                        <Route path=\"/admin/class-management/:classId/lessons\" element={<LessonManagement />} />\r\n                        <Route path=\"/admin/class-management/:classId/attendance\" element={<AttendancePage />} />\r\n\r\n                        <Route path=\"/admin/exam-management\" element={<ExamManagement />} />\r\n                        <Route path=\"/admin/exam-management/:examId\" element={<ExamDetailAdmin />} />\r\n                        <Route path=\"/admin/exam-management/:examId/questions\" element={<QuestionOfExamAdmin />} />\r\n                        <Route path=\"/admin/exam-management/:examId/preview\" element={<PreviewExamAdmin />} />\r\n                        <Route path=\"/admin/exam-management/:examId/tracking\" element={<TrackingPage />} />\r\n                        {/* Chỉ dành cho Admin */}\r\n                        <Route path=\"/admin/student-management\" element={<StudentManagement />} />\r\n                        <Route path=\"/admin/student-management/:userId\" element={<StudentDetailAdmin />} />\r\n                        <Route path=\"/admin/student-management/:userId/classes\" element={<UserClassManagement />} />\r\n                        <Route path=\"/admin/homepage-management\" element={<HomePageManagement />} />\r\n                        <Route path=\"/admin/achievement-management\" element={<AchievementManagement />} />\r\n                        <Route path=\"/admin/question-report-management\" element={<QuestionReportManagement />} />\r\n\r\n                        {/* Attendance Management Routes */}\r\n                        {/* <Route path=\"/admin/attendance\" element={<AdminUserSearchPage />} /> */}\r\n                        <Route path=\"/admin/attendance/user/:userId\" element={<AdminMobileAttendancePage />} />\r\n\r\n                        {/* Tuition Management Routes */}\r\n                        <Route path=\"/admin/tuition-payment\" element={<TuitionPaymentList />} />\r\n                    </Route>\r\n\r\n                    <Route element={<ProtectedRoute allowedRoles={[\"AD\"]} />}>\r\n                        <Route path=\"/admin/code-management\" element={<CodeManagement />} />\r\n                        <Route path=\"/admin/article-management\" element={<ArticleManagement />} />\r\n                        <Route path=\"/admin/article-post\" element={<ArticlePostPage />} />\r\n                        <Route path=\"/admin/article-management/edit/:id\" element={<ArticlePostPage />} />\r\n                        <Route path=\"/admin/spinner-demo\" element={<SpinnerDemo />} />\r\n                    </Route>\r\n\r\n                </Routes>\r\n            </MaintenanceWrapper>\r\n        </BrowserRouter>\r\n    );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,iBAAiB,MAAM,sCAAsC;AACpE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,wCAAwC,CAAC,CAAC;AAC1E,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,YAAY,MAAM,oCAAoC;AAC7D,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,UAAU,MAAM,kCAAkC;AACzD,OAAOC,eAAe,MAAM,mCAAmC;AAC/D,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,SAAS,MAAM,iCAAiC;AACvD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,mBAAmB,MAAM,yCAAyC;AACzE,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,wBAAwB,MAAM,wCAAwC;AAC7E,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,OAAOC,wBAAwB,MAAM,+CAA+C;AACpF,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,kBAAkB,MAAM,4CAA4C;AAC3E,OAAOC,mBAAmB,MAAM,8CAA8C;AAC9E,OAAOC,yBAAyB,MAAM,oDAAoD;AAC1F,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,WAAW,MAAM,0BAA0B;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,SAASC,GAAGA,CAAA,EAAG;EACX,oBACID,OAAA,CAAC1D,aAAa;IAAA4D,QAAA,eACVF,OAAA,CAAC9C,kBAAkB;MAAAgD,QAAA,gBACfF,OAAA,CAACF,WAAW;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEfN,OAAA,CAAC7C,kBAAkB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGtBN,OAAA,CAAC/C,mBAAmB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvBN,OAAA,CAACzD,MAAM;QAAA2D,QAAA,gBAEHF,OAAA,CAACxD,KAAK;UAAC+D,IAAI,EAAC,GAAG;UAACC,OAAO,eAAER,OAAA,CAACrC,IAAI;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCN,OAAA,CAACxD,KAAK;UAAC+D,IAAI,EAAC,eAAe;UAACC,OAAO,eAAER,OAAA,CAACL,mBAAmB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEN,OAAA,CAACxD,KAAK;UAAC+D,IAAI,EAAC,WAAW;UAACC,OAAO,eAAER,OAAA,CAACJ,eAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDN,OAAA,CAACxD,KAAK;UAAC+D,IAAI,EAAC,WAAW;UAACC,OAAO,eAAER,OAAA,CAACH,eAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDN,OAAA,CAACxD,KAAK;UAAC+D,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAER,OAAA,CAACvD,SAAS;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAK/CN,OAAA,CAACxD,KAAK;UAACgE,OAAO,eAAER,OAAA,CAAChD,cAAc;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,gBAE/BF,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,WAAW;YAACC,OAAO,eAAER,OAAA,CAACpC,YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAER,OAAA,CAACnC,cAAc;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,2BAA2B;YAACC,OAAO,eAAER,OAAA,CAAClC,UAAU;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAER,OAAA,CAACjC,eAAe;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAER,OAAA,CAAChC,WAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAER,OAAA,CAAC/B,iBAAiB;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,yCAAyC;YAACC,OAAO,eAAER,OAAA,CAAC9B,SAAS;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAER,OAAA,CAAC7B,aAAa;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAER,OAAA,CAAC5B,eAAe;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAER,OAAA,CAAC3B,YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,WAAW;YAACC,OAAO,eAAER,OAAA,CAACnB,eAAe;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,eAAe;YAACC,OAAO,eAAER,OAAA,CAACpB,WAAW;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,WAAW;YAACC,OAAO,eAAER,OAAA,CAAChB,YAAY;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAER,OAAA,CAACb,iBAAiB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAER,OAAA,CAACX,mBAAmB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,sBAAsB;YAACC,OAAO,eAAER,OAAA,CAACV,wBAAwB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,aAAa;YAACC,OAAO,eAAER,OAAA,CAACR,kBAAkB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAGRN,OAAA,CAACxD,KAAK;UAACgE,OAAO,eAAER,OAAA,CAAChD,cAAc;YAACyD,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,gBACjEF,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,WAAW;YAACC,OAAO,eAAER,OAAA,CAACtD,YAAY;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAErDN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAER,OAAA,CAACpD,kBAAkB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wCAAwC;YAACC,OAAO,eAAER,OAAA,CAAC5C,mBAAmB;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEzFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,yBAAyB;YAACC,OAAO,eAAER,OAAA,CAACnD,eAAe;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,kCAAkC;YAACC,OAAO,eAAER,OAAA,CAACtC,gBAAgB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wCAAwC;YAACC,OAAO,eAAER,OAAA,CAAC1B,mBAAmB;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,0CAA0C;YAACC,OAAO,eAAER,OAAA,CAACzB,gBAAgB;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,6CAA6C;YAACC,OAAO,eAAER,OAAA,CAACT,cAAc;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEzFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAER,OAAA,CAAClD,cAAc;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAER,OAAA,CAAC3C,eAAe;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,0CAA0C;YAACC,OAAO,eAAER,OAAA,CAAC1C,mBAAmB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3FN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wCAAwC;YAACC,OAAO,eAAER,OAAA,CAACxC,gBAAgB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,yCAAyC;YAACC,OAAO,eAAER,OAAA,CAACvB,YAAY;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEnFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,2BAA2B;YAACC,OAAO,eAAER,OAAA,CAACjD,iBAAiB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,mCAAmC;YAACC,OAAO,eAAER,OAAA,CAACvC,kBAAkB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,2CAA2C;YAACC,OAAO,eAAER,OAAA,CAACxB,mBAAmB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5FN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAER,OAAA,CAAClB,kBAAkB;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,+BAA+B;YAACC,OAAO,eAAER,OAAA,CAACjB,qBAAqB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,mCAAmC;YAACC,OAAO,eAAER,OAAA,CAACd,wBAAwB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAIzFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAER,OAAA,CAACN,yBAAyB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGvFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAER,OAAA,CAACZ,kBAAkB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eAERN,OAAA,CAACxD,KAAK;UAACgE,OAAO,eAAER,OAAA,CAAChD,cAAc;YAACyD,YAAY,EAAE,CAAC,IAAI;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,gBACrDF,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAER,OAAA,CAACzC,cAAc;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,2BAA2B;YAACC,OAAO,eAAER,OAAA,CAACrB,iBAAiB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1EN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAER,OAAA,CAACtB,eAAe;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClEN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,oCAAoC;YAACC,OAAO,eAAER,OAAA,CAACtB,eAAe;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjFN,OAAA,CAACxD,KAAK;YAAC+D,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAER,OAAA,CAACf,WAAW;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAExB;AAACI,EAAA,GAzFQT,GAAG;AA2FZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}