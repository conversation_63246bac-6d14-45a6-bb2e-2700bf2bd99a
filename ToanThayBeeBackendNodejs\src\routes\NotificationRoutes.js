import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as NotificationController from '../controllers/NotificationController.js'

const router = express.Router()

// Get user notifications
router.get('/v1/notifications',
    requireRoles([]),
    as<PERSON><PERSON><PERSON><PERSON>(NotificationController.getUserNotifications)
)

// Get unread notification count
router.get('/v1/notifications/unread-count',
    require<PERSON>oles([]),
    async<PERSON><PERSON><PERSON>(NotificationController.getUnreadCount)
)

// Mark notifications as read
router.post('/v1/notifications/mark-as-read',
    requireRoles([]),
    async<PERSON>and<PERSON>(NotificationController.markAsRead)
)

// Mark all notifications as read
router.post('/v1/notifications/mark-all-as-read',
    require<PERSON>oles([]),
    as<PERSON><PERSON><PERSON><PERSON>(NotificationController.markAllAsRead)
)

// Delete notifications
router.delete('/v1/notifications',
    requireRoles([]),
    async<PERSON><PERSON><PERSON>(NotificationController.deleteNotifications)
)

// Delete notification
router.delete('/v1/notifications/:id',
    requireRoles([]),
    asyncHandler(NotificationController.deleteNotification)
)

export default router
