{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\input\\\\CustomSearchInput.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomSearchInput = _ref => {\n  _s();\n  let {\n    placeholder = \"Tìm kiếm...\",\n    value,\n    onChange\n  } = _ref;\n  const inputRef = useRef(null);\n  useEffect(() => {\n    const handleCtrlF = e => {\n      const isMac = navigator.platform.toUpperCase().includes('MAC');\n      const isCtrlOrCmd = isMac ? e.metaKey : e.ctrlKey;\n      if (isCtrlOrCmd && e.key.toLowerCase() === 'f') {\n        var _inputRef$current;\n        e.preventDefault(); // Chặn Ctrl+F mặc định\n        (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus(); // Focus vào ô tìm kiếm\n      }\n    };\n    window.addEventListener('keydown', handleCtrlF);\n    return () => window.removeEventListener('keydown', handleCtrlF);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-[300px] relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 16 16\",\n      fill: \"none\",\n      className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n        stroke: \"#131214\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      ref: inputRef,\n      type: \"text\",\n      placeholder: placeholder,\n      value: value,\n      onChange: onChange,\n      className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 9\n  }, this);\n};\n_s(CustomSearchInput, \"cBQ6FQ+sf5H+lvNONLKqtm4aeQ8=\");\n_c = CustomSearchInput;\nexport default CustomSearchInput;\nvar _c;\n$RefreshReg$(_c, \"CustomSearchInput\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "CustomSearchInput", "_ref", "_s", "placeholder", "value", "onChange", "inputRef", "handleCtrlF", "e", "isMac", "navigator", "platform", "toUpperCase", "includes", "isCtrlOrCmd", "metaKey", "ctrl<PERSON>ey", "key", "toLowerCase", "_inputRef$current", "preventDefault", "current", "focus", "window", "addEventListener", "removeEventListener", "className", "children", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/input/CustomSearchInput.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\n\r\nconst CustomSearchInput = ({ placeholder = \"Tìm kiếm...\", value, onChange }) => {\r\n    const inputRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        const handleCtrlF = (e) => {\r\n            const isMac = navigator.platform.toUpperCase().includes('MAC');\r\n            const isCtrlOrCmd = isMac ? e.metaKey : e.ctrlKey;\r\n\r\n            if (isCtrlOrCmd && e.key.toLowerCase() === 'f') {\r\n                e.preventDefault(); // Chặn Ctrl+F mặc định\r\n                inputRef.current?.focus(); // Focus vào ô tìm kiếm\r\n            }\r\n        };\r\n\r\n        window.addEventListener('keydown', handleCtrlF);\r\n        return () => window.removeEventListener('keydown', handleCtrlF);\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"w-[300px] relative\">\r\n            <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n            >\r\n                <path\r\n                    d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                    stroke=\"#131214\"\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                />\r\n            </svg>\r\n            <input\r\n                ref={inputRef}\r\n                type=\"text\"\r\n                placeholder={placeholder}\r\n                value={value}\r\n                onChange={onChange}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CustomSearchInput;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,iBAAiB,GAAGC,IAAA,IAAsD;EAAAC,EAAA;EAAA,IAArD;IAAEC,WAAW,GAAG,aAAa;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAAJ,IAAA;EACvE,MAAMK,QAAQ,GAAGT,MAAM,CAAC,IAAI,CAAC;EAE7BD,SAAS,CAAC,MAAM;IACZ,MAAMW,WAAW,GAAIC,CAAC,IAAK;MACvB,MAAMC,KAAK,GAAGC,SAAS,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;MAC9D,MAAMC,WAAW,GAAGL,KAAK,GAAGD,CAAC,CAACO,OAAO,GAAGP,CAAC,CAACQ,OAAO;MAEjD,IAAIF,WAAW,IAAIN,CAAC,CAACS,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QAAA,IAAAC,iBAAA;QAC5CX,CAAC,CAACY,cAAc,CAAC,CAAC,CAAC,CAAC;QACpB,CAAAD,iBAAA,GAAAb,QAAQ,CAACe,OAAO,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/B;IACJ,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEjB,WAAW,CAAC;IAC/C,OAAO,MAAMgB,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAElB,WAAW,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIR,OAAA;IAAK2B,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAC/B5B,OAAA;MACI6B,KAAK,EAAC,4BAA4B;MAClCC,KAAK,EAAC,IAAI;MACVC,MAAM,EAAC,IAAI;MACXC,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,MAAM;MACXN,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eAEnE5B,OAAA;QACIkC,CAAC,EAAC,mPAAmP;QACrPC,MAAM,EAAC,SAAS;QAChBC,aAAa,EAAC,OAAO;QACrBC,cAAc,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACNzC,OAAA;MACI0C,GAAG,EAAEnC,QAAS;MACdoC,IAAI,EAAC,MAAM;MACXvC,WAAW,EAAEA,WAAY;MACzBC,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBqB,SAAS,EAAC;IAAsI;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACtC,EAAA,CA7CIF,iBAAiB;AAAA2C,EAAA,GAAjB3C,iBAAiB;AA+CvB,eAAeA,iBAAiB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}