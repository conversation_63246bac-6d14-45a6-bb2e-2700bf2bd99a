import { useSelector, useDispatch } from "react-redux";
import { useState, useRef, useEffect } from "react";
import { BeeMathLogo } from "../logo/BeeMathLogo";
import ChoiceHeader from "./ChoiceHeader";
import InputSearch from "../input/InputSearch";
import { logout } from "../../features/auth/authSlice";
import { motion, AnimatePresence } from "framer-motion";
import { useNavigate } from "react-router-dom";
import JoinClassModal from "../modal/JoinClassModal";
import StudentCardModal from "../modal/StudentCardModal";
import NotificationPanel from "../notification/NotificationPanel";
import { Bell, User, BookOpen, UserPlus, Eye, LogOut, CreditCard, Calendar } from "lucide-react";
import { socket } from "../../services/socket";
import { fetchUnreadCount, updateUnreadCount } from "../../features/notification/notificationSlice";

const Choice = () => {
    return (
        <>
            <ChoiceHeader title="Tổng quan" route="/overview" />
            <ChoiceHeader title="Lớp học" route="/class" />
            <ChoiceHeader title="Lý thuyết" route="/articles" />
            <ChoiceHeader title="Luyện đề" route="/practice" />
        </>
    )
}

const Header = () => {
    const { user } = useSelector(state => state.auth);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const dropdownRef = useRef();
    const [menuOpen, setMenuOpen] = useState(false);
    const menuRef = useRef();
    const toggleMenuRef = useRef();
    const [isStudentCardOpen, setIsStudentCardOpen] = useState(false);
    const [notificationOpen, setNotificationOpen] = useState(false);
    const notificationRef = useRef();
    // Get unread notification count from Redux store
    const { unreadCount } = useSelector((state) => state.notifications);

    // Connect socket and listen for notification updates
    useEffect(() => {
        dispatch(fetchUnreadCount());
    }, [dispatch]);

    const handleClick = () => {
        if (!user) {
            navigate("/login");
            return
        }
        setDropdownOpen(!dropdownOpen);
    }

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
                setDropdownOpen(false);
            }
        };
        document.addEventListener("click", handleClickOutside);
        return () => document.removeEventListener("click", handleClickOutside);
    }, []);

    useEffect(() => {
        const handleClickOutside = (e) => {
            // Đóng dropdown nếu click ra ngoài
            if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
                setDropdownOpen(false);
            }

            // Đóng menu nếu click ra ngoài (không phải chính nút mở)
            if (
                menuRef.current &&
                !menuRef.current.contains(e.target) &&
                toggleMenuRef.current &&
                !toggleMenuRef.current.contains(e.target)
            ) {
                setMenuOpen(false);
            }

            // Đóng notification panel nếu click ra ngoài
            if (notificationRef.current && !notificationRef.current.contains(e.target)) {
                setNotificationOpen(false);
            }
        };

        document.addEventListener("click", handleClickOutside);
        return () => document.removeEventListener("click", handleClickOutside);
    }, []);



    return (
        <header className="fixed top-0 left-0 right-0 z-40 bg-white">
            <StudentCardModal isOpen={isStudentCardOpen} onClose={() => setIsStudentCardOpen(false)} user={user} />
            <JoinClassModal isOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
            <div className="w-full lg:shadow-md lg:shadow-sky-200 overflow-hidden px-2 lg:px-[2rem] pt-[1rem] pb-[6px] mb-2 lg:mb-0">
                <div className="my-0 flex flex-row items-center justify-between">
                    <div className="flex flex-row items-center justify-start gap-0 lg:gap-4 w-[16rem]">
                        <BeeMathLogo className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8" />
                        <div
                            onClick={() => navigate("/overview")}
                            className="text-base cursor-pointer sm:text-lg md:text-xl lg:text-2xl font-bold font-bevietnam text-zinc-900 tracking-tight">
                            <span className="text-yellow-500">Toán</span> <span className="text-sky-500">Thầy Bee</span>
                        </div>
                    </div>

                    {/* Search - chỉ hiển thị ở desktop */}
                    {/* <InputSearch placeholder="Nhập id câu hỏi" className="hidden lg:block w-[16rem] h-10" /> */}
                    <div className="hidden lg:flex flex-row items-center gap-0 lg:gap-8 pb-0 lg:pt-[1rem]">
                        <Choice />
                    </div>
                    {/* Thông báo + avatar */}
                    <div className="flex flex-row items-center justify-end gap-0 lg:gap-4 w-[16rem]">
                        {/* Icon thông báo - desktop only */}
                        {user && (
                            <div className="relative hidden lg:block" ref={notificationRef}>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setNotificationOpen(prev => !prev);
                                    }}
                                    className="relative p-2 text-gray-600 hover:text-sky-600 hover:bg-gray-100 rounded-full transition-colors"
                                    title="Notifications"
                                >
                                    <Bell size={20} />
                                    {unreadCount > 0 && (
                                        <span className="absolute top-1 right-1 w-4 h-4 bg-red-500 text-white text-[10px] font-bold rounded-full flex items-center justify-center">
                                            {unreadCount > 99 ? '99+' : unreadCount}
                                        </span>
                                    )}
                                </button>
                                <NotificationPanel
                                    isOpen={notificationOpen}
                                    onClose={() => setNotificationOpen(false)}
                                />
                            </div>
                        )}

                        {/* Avatar + Dropdown */}
                        <div className="relative w-full" ref={dropdownRef}>
                            <div
                                onClick={handleClick}
                                className="w-full flex items-center pr-2 lg:pr-3 gap-2 lg:gap-3 bg-white rounded-full border border-gray-200 cursor-pointer hover:shadow-sm transition-all"
                            >
                                <div
                                    className={`relative flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 flex justify-center items-center`}
                                >
                                    {/* Vòng sóng */}
                                    <div className={`absolute flex items-center justify-center w-full h-full rounded-full  border-2 ${user ? 'border-yellow-400 animate-wave' : ''}`}></div>

                                    {/* Avatar */}
                                    <div className={`relative flex items-center justify-center w-full h-full rounded-full overflow-hidden p-1`}>
                                        {user?.avatarUrl ? (
                                            <div className="w-full h-full flex rounded-full overflow-hidden">
                                                <img
                                                    src={user.avatarUrl}
                                                    alt="avatar"
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>

                                        ) : (
                                            <svg width="30" height="30" viewBox="0 0 40 40" fill="none">
                                                <path
                                                    d="M20 2.5C10.335 2.5 2.5 10.335 2.5 20C2.5 29.665 10.335 37.5 20 37.5C29.665 37.5 37.5 29.665 37.5 20C37.5 10.335 29.665 2.5 20 2.5ZM20 22.5C16.6983 22.5 14.1667 19.88 14.1667 16.6667C14.1667 13.4533 16.6983 10.8333 20 10.8333C23.3017 10.8333 25.8333 13.4533 25.8333 16.6667C25.8333 19.88 23.3017 22.5 20 22.5ZM10 32.3C10.1 30.38 10.8 29.03 11.73 28.07C12.72 27.05 14 26.41 15.2 26.03C15.41 25.96 15.73 26.01 16.09 26.26C16.88 26.8 18.25 27.5 20 27.5C21.75 27.5 23.12 26.8 23.91 26.26C24.27 26.01 24.59 25.96 24.8 26.03C26 26.41 27.28 27.05 28.27 28.07C29.2 29.03 29.9 30.39 30 32.29C27.18 34.59 23.65 35.84 20 35.83C16.35 35.84 12.82 34.58 10 32.3Z"
                                                    fill="#94A3B8"
                                                />
                                            </svg>
                                        )}
                                    </div>
                                </div>

                                <div className="overflow-hidden flex-grow">
                                    <p className="text-xs sm:text-sm md:text-base lg:text-sm font-semibold text-sky-700 whitespace-nowrap overflow-x-auto hide-scrollbar">
                                        {user ? (
                                            user?.lastName + " " + user?.firstName
                                        ) : (
                                            "Đăng nhập"
                                        )}
                                    </p>
                                </div>
                            </div>

                            <AnimatePresence>
                                {dropdownOpen && user && (
                                    <motion.div
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -10 }}
                                        transition={{ duration: 0.2 }}
                                        className="fixed right-2 sm:right-4 md:right-8 mt-2 w-[14rem] sm:w-80 md:w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-40 max-h-[80vh] sm:max-h-[85vh] flex flex-col"
                                    >
                                        {/* Menu items */}
                                        <div className="overflow-y-auto">
                                            <div className="divide-y divide-gray-100">
                                                <div
                                                    onClick={() => setIsStudentCardOpen(true)}
                                                    className="p-2 sm:p-4 md:p-2 text-xs sm:text-base md:text-sm text-gray-700 hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer flex items-center gap-3">
                                                    <div className="p-2 bg-blue-100 rounded-full flex-shrink-0">
                                                        <User className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-blue-600" />
                                                    </div>
                                                    <span className="flex-1">Thông tin cá nhân</span>
                                                </div>

                                                <div
                                                    onClick={() => setIsModalOpen(true)}
                                                    className="p-2 sm:p-4 md:p-2 text-xs sm:text-base md:text-sm text-gray-700 hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer flex items-center gap-3">
                                                    <div className="p-2 bg-pink-100 rounded-full flex-shrink-0">
                                                        <UserPlus className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-pink-600" />
                                                    </div>
                                                    <span className="flex-1">Tham gia bằng mã lớp</span>
                                                </div>

                                                <div
                                                    onClick={() => {
                                                        setDropdownOpen(false);
                                                        navigate("/overview")
                                                    }}
                                                    className="p-2 sm:p-4 md:p-2 text-xs sm:text-base md:text-sm text-gray-700 hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer flex items-center gap-3">
                                                    <div className="p-2 bg-green-100 rounded-full flex-shrink-0">
                                                        <BookOpen className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-green-600" />
                                                    </div>
                                                    <span className="flex-1">Quản lý học tập</span>
                                                </div>
                                                <div
                                                    onClick={() => {
                                                        setDropdownOpen(false);
                                                        navigate("/tuition-payments")
                                                    }}
                                                    className="p-2 sm:p-4 md:p-2 text-xs sm:text-base md:text-sm text-gray-700 hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer flex items-center gap-3">
                                                    <div className="p-2 bg-yellow-100 rounded-full flex-shrink-0">
                                                        <CreditCard className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-yellow-600" />
                                                    </div>
                                                    <span className="flex-1">Học phí</span>
                                                </div>

                                                <div
                                                    onClick={() => {
                                                        setDropdownOpen(false);
                                                        navigate("/attendance")
                                                    }}
                                                    className="p-2 sm:p-4 md:p-2 text-xs sm:text-base md:text-sm text-gray-700 hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer flex items-center gap-3">
                                                    <div className="p-2 bg-sky-100 rounded-full flex-shrink-0">
                                                        <Calendar className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-sky-600" />
                                                    </div>
                                                    <span className="flex-1">Lịch sử điểm danh</span>
                                                </div>

                                                {user?.userType !== 'HS1' && (
                                                    <div
                                                        onClick={() => {
                                                            setDropdownOpen(false);
                                                            navigate("/admin/exam-management")
                                                        }}
                                                        className="p-2 sm:p-4 md:p-2 text-xs sm:text-base md:text-sm text-gray-700 hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer flex items-center gap-3">
                                                        <div className="p-2 bg-purple-100 rounded-full flex-shrink-0">
                                                            <Eye className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 text-purple-600" />
                                                        </div>
                                                        <span className="flex-1">Trang admin</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Footer with logout */}
                                        <div className="p-2 border-t border-gray-100">
                                            <button
                                                onClick={() => dispatch(logout())}
                                                className="w-full p-2 flex items-center justify-center gap-2 text-xs sm:text-base md:text-sm text-red-500 hover:bg-red-50 active:bg-red-100 rounded-md transition-colors"
                                            >
                                                <LogOut className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4" />
                                                <span>Đăng xuất</span>
                                            </button>
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </div>
                    </div>
                </div>                {/* Menu desktop */}

            </div>
            <div className="bg-white shadow-md shadow-sky-200 flex lg:hidden flex-row w-full justify-between items-center gap-0 lg:gap-8 pb-0 lg:pt-[1rem]">
                <Choice />
                {user && (
                    <div className="relative px-3" ref={notificationRef}>
                        <button
                            onClick={() => setNotificationOpen(!notificationOpen)}
                            className="relative p-2 text-gray-600 hover:text-sky-600 hover:bg-gray-100 rounded-full transition-colors"
                            title="Notifications"
                        >
                            <Bell size={18} />
                            {unreadCount > 0 && (
                                <span className="absolute top-1 right-1 w-3.5 h-3.5 bg-red-500 text-white text-[8px] font-bold rounded-full flex items-center justify-center">
                                    {unreadCount > 99 ? '99+' : unreadCount}
                                </span>
                            )}
                        </button>
                        <NotificationPanel
                            isOpen={notificationOpen}
                            onClose={() => setNotificationOpen(false)}
                        />
                    </div>
                )}
            </div>
        </header>

    );
};

export default Header;