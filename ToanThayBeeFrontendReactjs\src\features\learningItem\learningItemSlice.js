import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as LearningItemApi from "../../services/learningItemApi";
import { apiHandler } from "../../utils/apiHandler";


export const getUncompletedLearningItem = createAsyncThunk(
    "learningItems/getUncompletedLearningItem",
    async ({ page = 1, limit = 10 }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getUncompletedLearningItemAPI, { page, limit }, () => { }, false, false, false, false);
    }
);

const initialState = {
    learningItems: [],
    loading: false,
    pagination: {
        page: 1,
        limit: 10,
        totalItems: 0,
        totalPages: 0
    }
};

const learningItemSlice = createSlice({
    name: "learningItems",
    initialState,
    reducers: {
        resetLearningItems: (state) => {
            state.learningItems = [];
            state.pagination = {
                page: 1,
                pageSize: 10,
                total: 0,
                totalPages: 0
            };
        },
        setCurrentPageLT: (state, action) => {
            state.pagination.page = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getUncompletedLearningItem.pending, (state) => {
                state.loading = true;
                state.learningItems = [];
            })
            .addCase(getUncompletedLearningItem.fulfilled, (state, action) => {
                let { data } = action.payload;
                if (!action.payload || !action.payload.data) {
                    state.loading = false;
                    return;
                }

                state.loading = false;
                state.learningItems = data.data;
                state.pagination = data.pagination;
            })
            .addCase(getUncompletedLearningItem.rejected, (state) => {
                state.loading = false;
            });
    }

});

export const { resetLearningItems, setCurrentPageLT } = learningItemSlice.actions;

export default learningItemSlice.reducer;
