{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\layouts\\\\UserLayout.jsx\",\n  _s = $RefreshSig$();\nimport Header from \"../components/header/Header\";\nimport UnpaidTuitionModal from \"../components/UnpaidTuitionModal\";\nimport { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { checkTuitionPaymentNotPaid, setShowUnpaidModal } from \"../features/tuition/tuitionSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserLayout = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [headerHeight, setHeaderHeight] = useState(0);\n  const dispatch = useDispatch();\n  const {\n    isFirstTimeCheckTuition,\n    tuitionPaymentNotPaid,\n    showUnpaidModal\n  } = useSelector(state => state.tuition);\n  useEffect(() => {\n    if (!isFirstTimeCheckTuition) return;\n    dispatch(checkTuitionPaymentNotPaid());\n  }, [dispatch, isFirstTimeCheckTuition]);\n\n  // Show modal when unpaid tuition payments are detected\n  useEffect(() => {\n    if (tuitionPaymentNotPaid && tuitionPaymentNotPaid.length > 0) {\n      setShowUnpaidModal(true);\n    }\n  }, [tuitionPaymentNotPaid]);\n  useEffect(() => {\n    // Get the header height after it's rendered\n    const header = document.querySelector('header');\n    if (header) {\n      setHeaderHeight(header.offsetHeight);\n\n      // Update header height on window resize\n      const handleResize = () => {\n        setHeaderHeight(header.offsetHeight);\n      };\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-row w-full bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col w-full justify-center sm:mt-0 mt-4\",\n      style: {\n        paddingTop: \"\".concat(headerHeight, \"px\")\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(UnpaidTuitionModal, {\n      isOpen: showUnpaidModal,\n      onClose: () => setShowUnpaidModal(false),\n      unpaidPayments: tuitionPaymentNotPaid\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 9\n  }, this);\n};\n_s(UserLayout, \"gn3IpGyQUnmdGQe8VFZ67j9NMLs=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = UserLayout;\nexport default UserLayout;\nvar _c;\n$RefreshReg$(_c, \"UserLayout\");", "map": {"version": 3, "names": ["Header", "UnpaidTuitionModal", "useEffect", "useState", "useDispatch", "useSelector", "checkTuitionPaymentNotPaid", "setShowUnpaidModal", "jsxDEV", "_jsxDEV", "UserLayout", "_ref", "_s", "children", "headerHeight", "setHeaderHeight", "dispatch", "isFirstTimeCheckTuition", "tuitionPaymentNotPaid", "showUnpaidModal", "state", "tuition", "length", "header", "document", "querySelector", "offsetHeight", "handleResize", "window", "addEventListener", "removeEventListener", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "paddingTop", "concat", "isOpen", "onClose", "unpaidPayments", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/layouts/UserLayout.jsx"], "sourcesContent": ["import Header from \"../components/header/Header\";\r\nimport UnpaidTuitionModal from \"../components/UnpaidTuitionModal\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { checkTuitionPaymentNotPaid, setShowUnpaidModal } from \"../features/tuition/tuitionSlice\";\r\n\r\nconst UserLayout = ({ children }) => {\r\n    const [headerHeight, setHeaderHeight] = useState(0);\r\n    const dispatch = useDispatch();\r\n    const { isFirstTimeCheckTuition, tuitionPaymentNotPaid, showUnpaidModal } = useSelector((state) => state.tuition);\r\n\r\n    useEffect(() => {\r\n        if (!isFirstTimeCheckTuition) return;\r\n        dispatch(checkTuitionPaymentNotPaid());\r\n    }, [dispatch, isFirstTimeCheckTuition]);\r\n\r\n    // Show modal when unpaid tuition payments are detected\r\n    useEffect(() => {\r\n        if (tuitionPaymentNotPaid && tuitionPaymentNotPaid.length > 0) {\r\n            setShowUnpaidModal(true);\r\n        }\r\n    }, [tuitionPaymentNotPaid]);\r\n\r\n    useEffect(() => {\r\n        // Get the header height after it's rendered\r\n        const header = document.querySelector('header');\r\n        if (header) {\r\n            setHeaderHeight(header.offsetHeight);\r\n\r\n            // Update header height on window resize\r\n            const handleResize = () => {\r\n                setHeaderHeight(header.offsetHeight);\r\n            };\r\n\r\n            window.addEventListener('resize', handleResize);\r\n            return () => window.removeEventListener('resize', handleResize);\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"flex flex-row w-full bg-gray-50\">\r\n            <Header />\r\n            <div className=\"flex flex-col w-full justify-center sm:mt-0 mt-4\" style={{ paddingTop: `${headerHeight}px` }}>\r\n                {children}\r\n            </div>\r\n\r\n            {/* Unpaid Tuition Modal */}\r\n            <UnpaidTuitionModal\r\n                isOpen={showUnpaidModal}\r\n                onClose={() => setShowUnpaidModal(false)}\r\n                unpaidPayments={tuitionPaymentNotPaid}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n\r\n\r\nexport default UserLayout;"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,6BAA6B;AAChD,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,0BAA0B,EAAEC,kBAAkB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElG,MAAMC,UAAU,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC5B,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEa,uBAAuB;IAAEC,qBAAqB;IAAEC;EAAgB,CAAC,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAEjHnB,SAAS,CAAC,MAAM;IACZ,IAAI,CAACe,uBAAuB,EAAE;IAC9BD,QAAQ,CAACV,0BAA0B,CAAC,CAAC,CAAC;EAC1C,CAAC,EAAE,CAACU,QAAQ,EAAEC,uBAAuB,CAAC,CAAC;;EAEvC;EACAf,SAAS,CAAC,MAAM;IACZ,IAAIgB,qBAAqB,IAAIA,qBAAqB,CAACI,MAAM,GAAG,CAAC,EAAE;MAC3Df,kBAAkB,CAAC,IAAI,CAAC;IAC5B;EACJ,CAAC,EAAE,CAACW,qBAAqB,CAAC,CAAC;EAE3BhB,SAAS,CAAC,MAAM;IACZ;IACA,MAAMqB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIF,MAAM,EAAE;MACRR,eAAe,CAACQ,MAAM,CAACG,YAAY,CAAC;;MAEpC;MACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACvBZ,eAAe,CAACQ,MAAM,CAACG,YAAY,CAAC;MACxC,CAAC;MAEDE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MAC/C,OAAO,MAAMC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IACnE;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIlB,OAAA;IAAKsB,SAAS,EAAC,iCAAiC;IAAAlB,QAAA,gBAC5CJ,OAAA,CAACT,MAAM;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV1B,OAAA;MAAKsB,SAAS,EAAC,kDAAkD;MAACK,KAAK,EAAE;QAAEC,UAAU,KAAAC,MAAA,CAAKxB,YAAY;MAAK,CAAE;MAAAD,QAAA,EACxGA;IAAQ;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGN1B,OAAA,CAACR,kBAAkB;MACfsC,MAAM,EAAEpB,eAAgB;MACxBqB,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAAC,KAAK,CAAE;MACzCkC,cAAc,EAAEvB;IAAsB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAvB,EAAA,CAhDKF,UAAU;EAAA,QAEKN,WAAW,EACgDC,WAAW;AAAA;AAAAqC,EAAA,GAHrFhC,UAAU;AAmDhB,eAAeA,UAAU;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}