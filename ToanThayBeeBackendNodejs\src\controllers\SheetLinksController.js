import db from '../models/index.js';
import * as googleSheetsService from '../services/googleSheets.service.js';

// Tạo sheet link mới
export const createSheetLink = async (req, res) => {
    try {
        const { title, description, sheetUrl, category, relatedId, accessLevel, type } = req.body;
        const createdBy = req.user.id;

        // Validation
        if (!title || !sheetUrl) {
            return res.status(400).json({
                message: 'Tiêu đề và link sheet là bắt buộc!'
            });
        }

        // Kiểm tra URL hợp lệ
        try {
            new URL(sheetUrl);
        } catch (error) {
            return res.status(400).json({
                message: 'Link sheet không hợp lệ!'
            });
        }

        // Tạo sheet link mới
        const newSheetLink = await db.SheetLinks.create({
            title,
            description,
            sheetUrl,
            category,
            createdBy,
            accessLevel: accessLevel || 'CLASS_ONLY',
            type: type || 'CUSTOM',
            relatedId: relatedId || null,
            isActive: true,
            lastUpdated: new Date()
        });

        // Lấy thông tin chi tiết với associations
        const sheetLinkWithDetails = await db.SheetLinks.findByPk(newSheetLink.id);

        return res.status(201).json({
            message: 'Tạo sheet link thành công!',
            data: sheetLinkWithDetails
        });

    } catch (error) {
        console.error('Error creating sheet link:', error);
        return res.status(500).json({
            message: 'Có lỗi xảy ra khi tạo sheet link!'
        });
    }
};

// Lấy danh sách sheet links
export const getSheetLinks = async (req, res) => {
    try {
        const { relatedId, category, accessLevel, page = 1, limit = 10 } = req.query;
        const userId = req.user.id;
        const userRole = req.user.role;

        // Build where conditions
        const whereConditions = {
            isActive: true
        };

        // Filter by class
        if (classId) {
            whereConditions.relatedId = relatedId;
        }

        // Filter by category
        if (category) {
            whereConditions.category = category;
        }

        // Filter by access level based on user role
        if (userRole === 'ADMIN' || userRole === 'TEACHER' || userRole === 'ASSISTANT') {
            // Admin/Teacher có thể xem tất cả
            if (accessLevel) {
                whereConditions.accessLevel = accessLevel;
            }
        } else {
            // Student chỉ xem được PUBLIC và CLASS_ONLY (nếu thuộc lớp đó)
            whereConditions.accessLevel = ['PUBLIC', 'CLASS_ONLY'];
        }

        // Pagination
        const offset = (page - 1) * limit;

        const { count, rows } = await db.SheetLinks.findAndCountAll({
            where: whereConditions,
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    attributes: ['id', 'name', 'class_code']
                },
                {
                    model: db.User,
                    as: 'creator',
                    attributes: ['id', 'firstName', 'lastName']
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        return res.status(200).json({
            message: 'Lấy danh sách sheet links thành công!',
            data: {
                sheetLinks: rows,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: Math.ceil(count / limit),
                    totalItems: count,
                    itemsPerPage: parseInt(limit)
                }
            }
        });

    } catch (error) {
        console.error('Error getting sheet links:', error);
        return res.status(500).json({
            message: 'Có lỗi xảy ra khi lấy danh sách sheet links!'
        });
    }
};

// Lấy chi tiết sheet link
export const getSheetLinkById = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;
        const userRole = req.user.role;

        const sheetLink = await db.SheetLinks.findOne({
            where: {
                id,
                isActive: true
            },
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    attributes: ['id', 'name', 'class_code']
                },
                {
                    model: db.User,
                    as: 'creator',
                    attributes: ['id', 'firstName', 'lastName']
                }
            ]
        });

        if (!sheetLink) {
            return res.status(404).json({
                message: 'Không tìm thấy sheet link!'
            });
        }

        // Check access permission
        if (userRole !== 'ADMIN' && userRole !== 'TEACHER' && userRole !== 'ASSISTANT') {
            if (sheetLink.accessLevel === 'ADMIN_ONLY') {
                return res.status(403).json({
                    message: 'Bạn không có quyền truy cập sheet này!'
                });
            }
        }

        return res.status(200).json({
            message: 'Lấy chi tiết sheet link thành công!',
            data: sheetLink
        });

    } catch (error) {
        console.error('Error getting sheet link by id:', error);
        return res.status(500).json({
            message: 'Có lỗi xảy ra khi lấy chi tiết sheet link!'
        });
    }
};

// Test Google Sheets connection
export const testGoogleSheetsConnection = async (req, res) => {
    try {
        const { sheetId } = req.params;

        // Tìm sheet link
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink) {
            return res.status(404).json({
                message: 'Không tìm thấy sheet link!'
            });
        }

        // Test tạo header
        await googleSheetsService.createSheetHeader(sheetId);

        return res.status(200).json({
            message: 'Test Google Sheets connection thành công!',
            data: {
                sheetId: sheetId,
                sheetUrl: sheetLink.sheetUrl,
                title: sheetLink.title
            }
        });

    } catch (error) {
        console.error('Error testing Google Sheets connection:', error);
        return res.status(500).json({
            message: 'Lỗi khi test Google Sheets connection!',
            error: error.message
        });
    }
};

// Test thêm user vào sheet
export const testAddUserToSheet = async (req, res) => {
    try {
        const { sheetId } = req.params;

        // Tạo mock user data để test
        const mockUserData = {
            id: 999,
            firstName: 'Test',
            lastName: 'User',
            phone: '0123456789',
            email: '<EMAIL>',
            highSchool: 'Test High School',
            class: '12A1',
            graduationYear: '2024',
            userType: 'STUDENT',
            isActive: true
        };

        // Test thêm user vào sheet
        await googleSheetsService.addUserToSheet(mockUserData, sheetId);

        return res.status(200).json({
            message: 'Test thêm user vào sheet thành công!',
            data: mockUserData
        });

    } catch (error) {
        console.error('Error testing add user to sheet:', error);
        return res.status(500).json({
            message: 'Lỗi khi test thêm user vào sheet!',
            error: error.message
        });
    }
};

// CHẠY MỘT LẦN RỒI XÓA ĐI - Thêm username/password vào Google Sheets
export const addUsernamePasswordToSheets = async (req, res) => {
    try {
        console.log('🚀 API được gọi: Thêm username/password vào Google Sheets');

        // Kiểm tra quyền admin
        if (req.user.role !== 'ADMIN') {
            return res.status(403).json({
                message: 'Chỉ admin mới có quyền thực hiện chức năng này!'
            });
        }

        // Gọi service để xử lý tất cả sheets
        const result = await googleSheetsService.addUsernamePasswordToAllSheets();

        return res.status(200).json({
            message: 'Hoàn thành thêm username/password vào Google Sheets!',
            data: result
        });

    } catch (error) {
        console.error('❌ Error adding username/password to sheets:', error);
        return res.status(500).json({
            message: 'Có lỗi xảy ra khi thêm username/password vào Google Sheets!',
            error: error.message
        });
    }
};

// CHẠY MỘT LẦN RỒI XÓA ĐI - Thêm username/password vào một sheet cụ thể
export const addUsernamePasswordToSingleSheet = async (req, res) => {
    try {
        const { sheetId } = req.params;
        console.log(`🚀 API được gọi: Thêm username/password vào sheet ${sheetId}`);

        // Kiểm tra quyền admin

        // Kiểm tra sheet có tồn tại không
        const sheetLink = await db.SheetLinks.findByPk(sheetId);
        if (!sheetLink) {
            return res.status(404).json({
                message: 'Không tìm thấy sheet link!'
            });
        }

        // Gọi service để xử lý sheet cụ thể
        const result = await googleSheetsService.addUsernamePasswordToSheet(sheetId);

        return res.status(200).json({
            message: `Hoàn thành thêm username/password vào sheet ${sheetLink.name}!`,
            data: result
        });

    } catch (error) {
        console.error(`❌ Error adding username/password to sheet ${req.params.sheetId}:`, error);
        return res.status(500).json({
            message: 'Có lỗi xảy ra khi thêm username/password vào Google Sheet!',
            error: error.message
        });
    }
};
