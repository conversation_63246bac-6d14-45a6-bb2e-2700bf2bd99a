[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\Dashboard.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\RegisterPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\socket.js": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MenuSidebar.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\Choice.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx": "271", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx": "272"}, {"size": 837, "mtime": 1748800674146, "results": "273", "hashOfConfig": "274"}, {"size": 375, "mtime": 1744531393988, "results": "275", "hashOfConfig": "274"}, {"size": 9856, "mtime": 1750161060767, "results": "276", "hashOfConfig": "274"}, {"size": 2238, "mtime": 1749801515196, "results": "277", "hashOfConfig": "274"}, {"size": 843, "mtime": 1744531393977, "results": "278", "hashOfConfig": "274"}, {"size": 9018, "mtime": 1748220639296, "results": "279", "hashOfConfig": "274"}, {"size": 1183, "mtime": 1749697714870, "results": "280", "hashOfConfig": "274"}, {"size": 5016, "mtime": 1748330106066, "results": "281", "hashOfConfig": "274"}, {"size": 4828, "mtime": 1746378664900, "results": "282", "hashOfConfig": "274"}, {"size": 2012, "mtime": 1744531393979, "results": "283", "hashOfConfig": "274"}, {"size": 1271, "mtime": 1744531393978, "results": "284", "hashOfConfig": "274"}, {"size": 21195, "mtime": 1747223318312, "results": "285", "hashOfConfig": "274"}, {"size": 11683, "mtime": 1748250288653, "results": "286", "hashOfConfig": "274"}, {"size": 1979, "mtime": 1749722105422, "results": "287", "hashOfConfig": "274"}, {"size": 551, "mtime": 1744531393982, "results": "288", "hashOfConfig": "274"}, {"size": 275, "mtime": 1748215697376, "results": "289", "hashOfConfig": "274"}, {"size": 1739, "mtime": 1749721392840, "results": "290", "hashOfConfig": "274"}, {"size": 45583, "mtime": 1749698060672, "results": "291", "hashOfConfig": "274"}, {"size": 9142, "mtime": 1749697382803, "results": "292", "hashOfConfig": "274"}, {"size": 401, "mtime": 1744531393980, "results": "293", "hashOfConfig": "274"}, {"size": 21111, "mtime": 1748220666759, "results": "294", "hashOfConfig": "274"}, {"size": 5290, "mtime": 1749723419715, "results": "295", "hashOfConfig": "274"}, {"size": 2156, "mtime": 1749729430930, "results": "296", "hashOfConfig": "274"}, {"size": 4548, "mtime": 1749978521128, "results": "297", "hashOfConfig": "274"}, {"size": 348, "mtime": 1749202010110, "results": "298", "hashOfConfig": "274"}, {"size": 3119, "mtime": 1749720998016, "results": "299", "hashOfConfig": "274"}, {"size": 8773, "mtime": 1750239052490, "results": "300", "hashOfConfig": "274"}, {"size": 1080, "mtime": 1747460558584, "results": "301", "hashOfConfig": "274"}, {"size": 6445, "mtime": 1749721854651, "results": "302", "hashOfConfig": "274"}, {"size": 3345, "mtime": 1748250288648, "results": "303", "hashOfConfig": "274"}, {"size": 3099, "mtime": 1744531393973, "results": "304", "hashOfConfig": "274"}, {"size": 6929, "mtime": 1749723272968, "results": "305", "hashOfConfig": "274"}, {"size": 17415, "mtime": 1749731127191, "results": "306", "hashOfConfig": "274"}, {"size": 12809, "mtime": 1749801873182, "results": "307", "hashOfConfig": "274"}, {"size": 1380, "mtime": 1744531393975, "results": "308", "hashOfConfig": "274"}, {"size": 2643, "mtime": 1747353945002, "results": "309", "hashOfConfig": "274"}, {"size": 4309, "mtime": 1748709335425, "results": "310", "hashOfConfig": "274"}, {"size": 4640, "mtime": 1748220605989, "results": "311", "hashOfConfig": "274"}, {"size": 2480, "mtime": 1747721626218, "results": "312", "hashOfConfig": "274"}, {"size": 5150, "mtime": 1748398556383, "results": "313", "hashOfConfig": "274"}, {"size": 1337, "mtime": 1747720637516, "results": "314", "hashOfConfig": "274"}, {"size": 690, "mtime": 1744531393950, "results": "315", "hashOfConfig": "274"}, {"size": 1339, "mtime": 1747223318344, "results": "316", "hashOfConfig": "274"}, {"size": 673, "mtime": 1744531393976, "results": "317", "hashOfConfig": "274"}, {"size": 903, "mtime": 1744531393976, "results": "318", "hashOfConfig": "274"}, {"size": 2284, "mtime": 1744531393959, "results": "319", "hashOfConfig": "274"}, {"size": 1151, "mtime": 1749730250381, "results": "320", "hashOfConfig": "274"}, {"size": 3200, "mtime": 1744531393954, "results": "321", "hashOfConfig": "274"}, {"size": 3578, "mtime": 1747223318325, "results": "322", "hashOfConfig": "274"}, {"size": 635, "mtime": 1744531393961, "results": "323", "hashOfConfig": "274"}, {"size": 1227, "mtime": 1744531393952, "results": "324", "hashOfConfig": "274"}, {"size": 5587, "mtime": 1744531393969, "results": "325", "hashOfConfig": "274"}, {"size": 7571, "mtime": 1747902858134, "results": "326", "hashOfConfig": "274"}, {"size": 1218, "mtime": 1744531393963, "results": "327", "hashOfConfig": "274"}, {"size": 9586, "mtime": 1749722764769, "results": "328", "hashOfConfig": "274"}, {"size": 5657, "mtime": 1744531393962, "results": "329", "hashOfConfig": "274"}, {"size": 28691, "mtime": 1744958278477, "results": "330", "hashOfConfig": "274"}, {"size": 19936, "mtime": 1748984865157, "results": "331", "hashOfConfig": "274"}, {"size": 5631, "mtime": 1749721260990, "results": "332", "hashOfConfig": "274"}, {"size": 8139, "mtime": 1749721464118, "results": "333", "hashOfConfig": "274"}, {"size": 911, "mtime": 1744531393943, "results": "334", "hashOfConfig": "274"}, {"size": 3647, "mtime": 1747223318326, "results": "335", "hashOfConfig": "274"}, {"size": 30556, "mtime": 1749698031543, "results": "336", "hashOfConfig": "274"}, {"size": 9572, "mtime": 1749693815954, "results": "337", "hashOfConfig": "274"}, {"size": 448, "mtime": 1749815688858, "results": "338", "hashOfConfig": "274"}, {"size": 1574, "mtime": 1744531393960, "results": "339", "hashOfConfig": "274"}, {"size": 7401, "mtime": 1747223318342, "results": "340", "hashOfConfig": "274"}, {"size": 2914, "mtime": 1747223318342, "results": "341", "hashOfConfig": "274"}, {"size": 28398, "mtime": 1749425223637, "results": "342", "hashOfConfig": "274"}, {"size": 17496, "mtime": 1748709335423, "results": "343", "hashOfConfig": "274"}, {"size": 19585, "mtime": 1747902858113, "results": "344", "hashOfConfig": "274"}, {"size": 1734, "mtime": 1744531393948, "results": "345", "hashOfConfig": "274"}, {"size": 55645, "mtime": 1748984865157, "results": "346", "hashOfConfig": "274"}, {"size": 6448, "mtime": 1749729618233, "results": "347", "hashOfConfig": "274"}, {"size": 5637, "mtime": 1747254491214, "results": "348", "hashOfConfig": "274"}, {"size": 21475, "mtime": 1748984865155, "results": "349", "hashOfConfig": "274"}, {"size": 10256, "mtime": 1744531393939, "results": "350", "hashOfConfig": "274"}, {"size": 1880, "mtime": 1750240499610, "results": "351", "hashOfConfig": "274"}, {"size": 18529, "mtime": 1749202010110, "results": "352", "hashOfConfig": "274"}, {"size": 6162, "mtime": 1748250288622, "results": "353", "hashOfConfig": "274"}, {"size": 3022, "mtime": 1747719253353, "results": "354", "hashOfConfig": "274"}, {"size": 7915, "mtime": 1748392811401, "results": "355", "hashOfConfig": "274"}, {"size": 5023, "mtime": 1749697044412, "results": "356", "hashOfConfig": "274"}, {"size": 3490, "mtime": 1749748056906, "results": "357", "hashOfConfig": "274"}, {"size": 935, "mtime": 1745405710864, "results": "358", "hashOfConfig": "274"}, {"size": 949, "mtime": 1747223318316, "results": "359", "hashOfConfig": "274"}, {"size": 3267, "mtime": 1747354362354, "results": "360", "hashOfConfig": "274"}, {"size": 3004, "mtime": 1749747762024, "results": "361", "hashOfConfig": "274"}, {"size": 1302, "mtime": 1748326437434, "results": "362", "hashOfConfig": "274"}, {"size": 2380, "mtime": 1744531393947, "results": "363", "hashOfConfig": "274"}, {"size": 2201, "mtime": 1744531393951, "results": "364", "hashOfConfig": "274"}, {"size": 14316, "mtime": 1749895693266, "results": "365", "hashOfConfig": "274"}, {"size": 1990, "mtime": 1744531393948, "results": "366", "hashOfConfig": "274"}, {"size": 841, "mtime": 1748984865154, "results": "367", "hashOfConfig": "274"}, {"size": 5565, "mtime": 1745690690469, "results": "368", "hashOfConfig": "274"}, {"size": 2295, "mtime": 1747223318353, "results": "369", "hashOfConfig": "274"}, {"size": 3146, "mtime": 1744531393940, "results": "370", "hashOfConfig": "274"}, {"size": 4074, "mtime": 1747223318326, "results": "371", "hashOfConfig": "274"}, {"size": 2787, "mtime": 1750173366905, "results": "372", "hashOfConfig": "274"}, {"size": 10634, "mtime": 1745483150534, "results": "373", "hashOfConfig": "374"}, {"size": 3707, "mtime": 1749722678323, "results": "375", "hashOfConfig": "274"}, {"size": 6034, "mtime": 1748364026312, "results": "376", "hashOfConfig": "274"}, {"size": 4147, "mtime": 1749731674278, "results": "377", "hashOfConfig": "274"}, {"size": 829, "mtime": 1744531393990, "results": "378", "hashOfConfig": "274"}, {"size": 3423, "mtime": 1749801242912, "results": "379", "hashOfConfig": "274"}, {"size": 297, "mtime": 1744531393989, "results": "380", "hashOfConfig": "274"}, {"size": 313, "mtime": 1744531393940, "results": "381", "hashOfConfig": "274"}, {"size": 4754, "mtime": 1749730388262, "results": "382", "hashOfConfig": "274"}, {"size": 1652, "mtime": 1748250288663, "results": "383", "hashOfConfig": "274"}, {"size": 993, "mtime": 1747223318349, "results": "384", "hashOfConfig": "274"}, {"size": 475, "mtime": 1748984865156, "results": "385", "hashOfConfig": "274"}, {"size": 902, "mtime": 1747223318353, "results": "386", "hashOfConfig": "274"}, {"size": 3053, "mtime": 1744531393946, "results": "387", "hashOfConfig": "274"}, {"size": 47782, "mtime": 1748984865154, "results": "388", "hashOfConfig": "274"}, {"size": 3402, "mtime": 1748781512174, "results": "389", "hashOfConfig": "274"}, {"size": 4872, "mtime": 1747223318349, "results": "390", "hashOfConfig": "274"}, {"size": 1392, "mtime": 1744957327338, "results": "391", "hashOfConfig": "274"}, {"size": 2297, "mtime": 1744531393945, "results": "392", "hashOfConfig": "274"}, {"size": 2193, "mtime": 1747223318349, "results": "393", "hashOfConfig": "274"}, {"size": 1359, "mtime": 1747223318349, "results": "394", "hashOfConfig": "274"}, {"size": 826, "mtime": 1748398318309, "results": "395", "hashOfConfig": "274"}, {"size": 1769, "mtime": 1748709335429, "results": "396", "hashOfConfig": "274"}, {"size": 888, "mtime": 1744551763492, "results": "397", "hashOfConfig": "274"}, {"size": 4921, "mtime": 1747223318325, "results": "398", "hashOfConfig": "274"}, {"size": 24089, "mtime": 1750161401768, "results": "399", "hashOfConfig": "274"}, {"size": 411, "mtime": 1744531393940, "results": "400", "hashOfConfig": "274"}, {"size": 2290, "mtime": 1744531393963, "results": "401", "hashOfConfig": "274"}, {"size": 1219, "mtime": 1747467640276, "results": "402", "hashOfConfig": "274"}, {"size": 2003, "mtime": 1744531393970, "results": "403", "hashOfConfig": "274"}, {"size": 2166, "mtime": 1744531393969, "results": "404", "hashOfConfig": "274"}, {"size": 18019, "mtime": 1748984865167, "results": "405", "hashOfConfig": "274"}, {"size": 6813, "mtime": 1747223318342, "results": "406", "hashOfConfig": "274"}, {"size": 3094, "mtime": 1744531393970, "results": "407", "hashOfConfig": "274"}, {"size": 6087, "mtime": 1748876218633, "results": "408", "hashOfConfig": "274"}, {"size": 503, "mtime": 1744531393949, "results": "409", "hashOfConfig": "274"}, {"size": 641, "mtime": 1746285622761, "results": "410", "hashOfConfig": "274"}, {"size": 7876, "mtime": 1747223318342, "results": "411", "hashOfConfig": "274"}, {"size": 4922, "mtime": 1748329867180, "results": "412", "hashOfConfig": "274"}, {"size": 19960, "mtime": 1750177325515, "results": "413", "hashOfConfig": "274"}, {"size": 20555, "mtime": 1748250288625, "results": "414", "hashOfConfig": "274"}, {"size": 1337, "mtime": 1744531393967, "results": "415", "hashOfConfig": "374"}, {"size": 5412, "mtime": 1747223318349, "results": "416", "hashOfConfig": "274"}, {"size": 2938, "mtime": 1747223318353, "results": "417", "hashOfConfig": "274"}, {"size": 3182, "mtime": 1747223318353, "results": "418", "hashOfConfig": "274"}, {"size": 2928, "mtime": 1747223318349, "results": "419", "hashOfConfig": "274"}, {"size": 1885, "mtime": 1747354661883, "results": "420", "hashOfConfig": "274"}, {"size": 1345, "mtime": 1749697625937, "results": "421", "hashOfConfig": "274"}, {"size": 4099, "mtime": 1749731407409, "results": "422", "hashOfConfig": "274"}, {"size": 1106, "mtime": 1744531393967, "results": "423", "hashOfConfig": "274"}, {"size": 642, "mtime": 1744531393966, "results": "424", "hashOfConfig": "274"}, {"size": 3540, "mtime": 1744531393966, "results": "425", "hashOfConfig": "274"}, {"size": 6380, "mtime": 1747361017417, "results": "426", "hashOfConfig": "274"}, {"size": 2600, "mtime": 1748876218633, "results": "427", "hashOfConfig": "274"}, {"size": 11010, "mtime": 1748278675058, "results": "428", "hashOfConfig": "274"}, {"size": 3297, "mtime": 1744531393957, "results": "429", "hashOfConfig": "274"}, {"size": 20934, "mtime": 1748987770234, "results": "430", "hashOfConfig": "274"}, {"size": 13435, "mtime": 1749695159063, "results": "431", "hashOfConfig": "274"}, {"size": 1205, "mtime": 1748984865161, "results": "432", "hashOfConfig": "274"}, {"size": 12148, "mtime": 1748250288636, "results": "433", "hashOfConfig": "274"}, {"size": 7688, "mtime": 1747223318312, "results": "434", "hashOfConfig": "274"}, {"size": 8344, "mtime": 1745507778499, "results": "435", "hashOfConfig": "274"}, {"size": 8025, "mtime": 1745507836095, "results": "436", "hashOfConfig": "274"}, {"size": 6988, "mtime": 1747223318344, "results": "437", "hashOfConfig": "274"}, {"size": 7719, "mtime": 1745499803667, "results": "438", "hashOfConfig": "274"}, {"size": 8378, "mtime": 1747223318344, "results": "439", "hashOfConfig": "274"}, {"size": 7254, "mtime": 1747223318344, "results": "440", "hashOfConfig": "274"}, {"size": 1721, "mtime": 1749553291593, "results": "441", "hashOfConfig": "274"}, {"size": 11464, "mtime": 1745500164258, "results": "442", "hashOfConfig": "274"}, {"size": 4650, "mtime": 1745508333678, "results": "443", "hashOfConfig": "274"}, {"size": 13822, "mtime": 1748250288625, "results": "444", "hashOfConfig": "274"}, {"size": 8599, "mtime": 1747223318326, "results": "445", "hashOfConfig": "274"}, {"size": 9774, "mtime": 1747223318326, "results": "446", "hashOfConfig": "274"}, {"size": 7914, "mtime": 1747223318326, "results": "447", "hashOfConfig": "274"}, {"size": 10728, "mtime": 1749548057374, "results": "448", "hashOfConfig": "274"}, {"size": 18582, "mtime": 1749747601919, "results": "449", "hashOfConfig": "274"}, {"size": 8954, "mtime": 1749810779685, "results": "450", "hashOfConfig": "274"}, {"size": 3429, "mtime": 1745682027607, "results": "451", "hashOfConfig": "274"}, {"size": 2298, "mtime": 1749802834779, "results": "452", "hashOfConfig": "274"}, {"size": 823, "mtime": 1748779533260, "results": "453", "hashOfConfig": "274"}, {"size": 1380, "mtime": 1745682047729, "results": "454", "hashOfConfig": "274"}, {"size": 1145, "mtime": 1747902858133, "results": "455", "hashOfConfig": "274"}, {"size": 1118, "mtime": 1745682069045, "results": "456", "hashOfConfig": "274"}, {"size": 978, "mtime": 1747902858130, "results": "457", "hashOfConfig": "274"}, {"size": 1781, "mtime": 1745682059432, "results": "458", "hashOfConfig": "374"}, {"size": 10563, "mtime": 1748585335221, "results": "459", "hashOfConfig": "274"}, {"size": 3574, "mtime": 1746378664904, "results": "460", "hashOfConfig": "274"}, {"size": 4389, "mtime": 1749980511142, "results": "461", "hashOfConfig": "274"}, {"size": 4863, "mtime": 1749815418258, "results": "462", "hashOfConfig": "274"}, {"size": 6432, "mtime": 1749815434335, "results": "463", "hashOfConfig": "274"}, {"size": 1020, "mtime": 1745682406063, "results": "464", "hashOfConfig": "274"}, {"size": 2723, "mtime": 1749810234131, "results": "465", "hashOfConfig": "274"}, {"size": 6116, "mtime": 1746378664905, "results": "466", "hashOfConfig": "274"}, {"size": 1565, "mtime": 1747223318344, "results": "467", "hashOfConfig": "274"}, {"size": 1624, "mtime": 1745689590880, "results": "468", "hashOfConfig": "274"}, {"size": 42976, "mtime": 1749980501187, "results": "469", "hashOfConfig": "274"}, {"size": 70430, "mtime": 1748984865165, "results": "470", "hashOfConfig": "274"}, {"size": 9519, "mtime": 1747354195259, "results": "471", "hashOfConfig": "274"}, {"size": 7408, "mtime": 1749894301204, "results": "472", "hashOfConfig": "274"}, {"size": 4905, "mtime": 1747354192845, "results": "473", "hashOfConfig": "274"}, {"size": 59290, "mtime": 1749616133330, "results": "474", "hashOfConfig": "274"}, {"size": 26487, "mtime": 1748278828957, "results": "475", "hashOfConfig": "274"}, {"size": 24147, "mtime": 1747354834297, "results": "476", "hashOfConfig": "274"}, {"size": 23053, "mtime": 1749730024729, "results": "477", "hashOfConfig": "274"}, {"size": 37417, "mtime": 1748984865162, "results": "478", "hashOfConfig": "274"}, {"size": 16976, "mtime": 1749732457976, "results": "479", "hashOfConfig": "274"}, {"size": 8060, "mtime": 1747223318310, "results": "480", "hashOfConfig": "274"}, {"size": 16767, "mtime": 1748876218633, "results": "481", "hashOfConfig": "274"}, {"size": 3160, "mtime": 1745731138150, "results": "482", "hashOfConfig": "274"}, {"size": 7136, "mtime": 1747223318325, "results": "483", "hashOfConfig": "274"}, {"size": 20185, "mtime": 1747223318312, "results": "484", "hashOfConfig": "274"}, {"size": 2129, "mtime": 1746378664905, "results": "485", "hashOfConfig": "274"}, {"size": 955, "mtime": 1746378664898, "results": "486", "hashOfConfig": "274"}, {"size": 1184, "mtime": 1746378664905, "results": "487", "hashOfConfig": "274"}, {"size": 13378, "mtime": 1748984865159, "results": "488", "hashOfConfig": "274"}, {"size": 1099, "mtime": 1748326442261, "results": "489", "hashOfConfig": "274"}, {"size": 15897, "mtime": 1749894770618, "results": "490", "hashOfConfig": "274"}, {"size": 11960, "mtime": 1749815846575, "results": "491", "hashOfConfig": "274"}, {"size": 12224, "mtime": 1748220515100, "results": "492", "hashOfConfig": "274"}, {"size": 10534, "mtime": 1748220627343, "results": "493", "hashOfConfig": "274"}, {"size": 4031, "mtime": 1747278525096, "results": "494", "hashOfConfig": "274"}, {"size": 2036, "mtime": 1747283717643, "results": "495", "hashOfConfig": "274"}, {"size": 72414, "mtime": 1748330146983, "results": "496", "hashOfConfig": "274"}, {"size": 3589, "mtime": 1747355350828, "results": "497", "hashOfConfig": "274"}, {"size": 7509, "mtime": 1747353152130, "results": "498", "hashOfConfig": "274"}, {"size": 6153, "mtime": 1747354063004, "results": "499", "hashOfConfig": "274"}, {"size": 16791, "mtime": 1748473697636, "results": "500", "hashOfConfig": "274"}, {"size": 12075, "mtime": 1750240418372, "results": "501", "hashOfConfig": "274"}, {"size": 13612, "mtime": 1750175198005, "results": "502", "hashOfConfig": "274"}, {"size": 69521, "mtime": 1750173734119, "results": "503", "hashOfConfig": "274"}, {"size": 5589, "mtime": 1750239106405, "results": "504", "hashOfConfig": "274"}, {"size": 9836, "mtime": 1750175833314, "results": "505", "hashOfConfig": "274"}, {"size": 8098, "mtime": 1750174978082, "results": "506", "hashOfConfig": "274"}, {"size": 4152, "mtime": 1749202010110, "results": "507", "hashOfConfig": "274"}, {"size": 4762, "mtime": 1748513292659, "results": "508", "hashOfConfig": "274"}, {"size": 2443, "mtime": 1747719362467, "results": "509", "hashOfConfig": "274"}, {"size": 15359, "mtime": 1749549797204, "results": "510", "hashOfConfig": "274"}, {"size": 1863, "mtime": 1748356706231, "results": "511", "hashOfConfig": "274"}, {"size": 50801, "mtime": 1749552838208, "results": "512", "hashOfConfig": "274"}, {"size": 16122, "mtime": 1748221650546, "results": "513", "hashOfConfig": "274"}, {"size": 8222, "mtime": 1748250288630, "results": "514", "hashOfConfig": "274"}, {"size": 11210, "mtime": 1748223444732, "results": "515", "hashOfConfig": "274"}, {"size": 41966, "mtime": 1748250288654, "results": "516", "hashOfConfig": "274"}, {"size": 7612, "mtime": 1748250288649, "results": "517", "hashOfConfig": "274"}, {"size": 28083, "mtime": 1748250288657, "results": "518", "hashOfConfig": "274"}, {"size": 29543, "mtime": 1748984865164, "results": "519", "hashOfConfig": "274"}, {"size": 36682, "mtime": 1748250768337, "results": "520", "hashOfConfig": "274"}, {"size": 1840, "mtime": 1748250288662, "results": "521", "hashOfConfig": "274"}, {"size": 9569, "mtime": 1749694485776, "results": "522", "hashOfConfig": "274"}, {"size": 13102, "mtime": 1748250288647, "results": "523", "hashOfConfig": "274"}, {"size": 16077, "mtime": 1748365756504, "results": "524", "hashOfConfig": "274"}, {"size": 3987, "mtime": 1748709335425, "results": "525", "hashOfConfig": "274"}, {"size": 3539, "mtime": 1748800991826, "results": "526", "hashOfConfig": "274"}, {"size": 1712, "mtime": 1748800656400, "results": "527", "hashOfConfig": "274"}, {"size": 1983, "mtime": 1749871596913, "results": "528", "hashOfConfig": "274"}, {"size": 3908, "mtime": 1748801325319, "results": "529", "hashOfConfig": "274"}, {"size": 839, "mtime": 1748801505979, "results": "530", "hashOfConfig": "274"}, {"size": 365, "mtime": 1748984865153, "results": "531", "hashOfConfig": "274"}, {"size": 1199, "mtime": 1748984865156, "results": "532", "hashOfConfig": "274"}, {"size": 8860, "mtime": 1749202010110, "results": "533", "hashOfConfig": "274"}, {"size": 2130, "mtime": 1749521895529, "results": "534", "hashOfConfig": "274"}, {"size": 7780, "mtime": 1749202010110, "results": "535", "hashOfConfig": "274"}, {"size": 3832, "mtime": 1749202010110, "results": "536", "hashOfConfig": "274"}, {"size": 243, "mtime": 1749521895533, "results": "537", "hashOfConfig": "274"}, {"size": 2836, "mtime": 1749722547776, "results": "538", "hashOfConfig": "274"}, {"size": 734, "mtime": 1749721859047, "results": "539", "hashOfConfig": "274"}, {"size": 388, "mtime": 1749720429395, "results": "540", "hashOfConfig": "274"}, {"size": 579, "mtime": 1749731593347, "results": "541", "hashOfConfig": "274"}, {"size": 1530, "mtime": 1749732424739, "results": "542", "hashOfConfig": "274"}, {"size": 10261, "mtime": 1749813601878, "results": "543", "hashOfConfig": "274"}, {"size": 1664, "mtime": 1749808930419, "results": "544", "hashOfConfig": "274"}, {"size": 2228, "mtime": 1750173663568, "results": "545", "hashOfConfig": "274"}, {"size": 7029, "mtime": 1750240718364, "results": "546", "hashOfConfig": "274"}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1363", "1364"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\RegisterPage.jsx", ["1365"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1366", "1367"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1368", "1369"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1370"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1371"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1372", "1373", "1374", "1375", "1376"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1377", "1378"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1379", "1380"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1381"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1382", "1383", "1384", "1385", "1386", "1387"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1388", "1389", "1390", "1391", "1392"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1393", "1394", "1395", "1396"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1397", "1398"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1399", "1400", "1401", "1402"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1403"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1404"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1405"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1406"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1407", "1408", "1409", "1410", "1411", "1412", "1413"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1414"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1415", "1416", "1417"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1418"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1419"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1420", "1421", "1422"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1423", "1424", "1425"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1426", "1427"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1428", "1429", "1430", "1431", "1432", "1433"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\socket.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1442", "1443"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1444", "1445", "1446", "1447", "1448"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1449", "1450", "1451", "1452"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1453", "1454", "1455"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1456"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1457", "1458", "1459", "1460", "1461"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1462", "1463", "1464", "1465", "1466", "1467"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1468", "1469"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1470"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1471", "1472", "1473", "1474", "1475", "1476", "1477"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1478", "1479", "1480", "1481", "1482"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1483", "1484", "1485"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1486", "1487"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1488"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1499"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1500"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1515"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1516"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1517"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1518"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1519"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1520", "1521", "1522", "1523"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1524"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MenuSidebar.jsx", ["1525"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\Choice.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1526"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1527"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1528", "1529", "1530", "1531"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1532", "1533"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1534", "1535", "1536"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1537", "1538", "1539", "1540"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1541"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1542", "1543"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1544"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1545", "1546"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1547"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1548"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1549", "1550", "1551", "1552", "1553"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1554", "1555"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1556", "1557", "1558"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1583", "1584", "1585", "1586"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1587"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1588", "1589", "1590", "1591"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1592", "1593", "1594", "1595", "1596", "1597", "1598"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1609", "1610"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1611", "1612", "1613"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1614", "1615", "1616", "1617", "1618", "1619", "1620"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1621", "1622", "1623", "1624", "1625", "1626", "1627"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1628", "1629"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1630"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1631", "1632", "1633"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1634", "1635", "1636", "1637", "1638"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1639"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1640", "1641", "1642"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1643", "1644", "1645", "1646", "1647", "1648", "1649"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1650"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1651", "1652", "1653", "1654"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1686", "1687", "1688", "1689", "1690", "1691"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1692"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1693"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1694", "1695"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1696", "1697", "1698", "1699", "1700", "1701", "1702"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1703", "1704", "1705", "1706", "1707"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["1708", "1709", "1710"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["1711", "1712"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["1713", "1714"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["1715", "1716", "1717"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["1718"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\CustomSearchInput.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UnpaidTuitionModal.jsx", [], [], {"ruleId": "1719", "severity": 1, "message": "1720", "line": 4, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 17}, {"ruleId": "1719", "severity": 1, "message": "1723", "line": 50, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 50, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1724", "line": 1, "column": 20, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1725", "line": 4, "column": 23, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1726", "line": 8, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1727", "line": 16, "column": 41, "nodeType": "1721", "messageId": "1722", "endLine": 16, "endColumn": 51}, {"ruleId": "1719", "severity": 1, "message": "1728", "line": 17, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 36}, {"ruleId": "1729", "severity": 1, "message": "1730", "line": 26, "column": 8, "nodeType": "1731", "endLine": 26, "endColumn": 19, "suggestions": "1732"}, {"ruleId": "1733", "severity": 1, "message": "1734", "line": 393, "column": 45, "nodeType": "1735", "endLine": 398, "endColumn": 47}, {"ruleId": "1719", "severity": 1, "message": "1736", "line": 6, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 11}, {"ruleId": "1719", "severity": 1, "message": "1737", "line": 6, "column": 27, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 31}, {"ruleId": "1719", "severity": 1, "message": "1738", "line": 7, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 8, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 32}, {"ruleId": "1719", "severity": 1, "message": "1740", "line": 88, "column": 23, "nodeType": "1721", "messageId": "1722", "endLine": 88, "endColumn": 31}, {"ruleId": "1719", "severity": 1, "message": "1741", "line": 14, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1728", "line": 15, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 36}, {"ruleId": "1719", "severity": 1, "message": "1742", "line": 2, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1743", "line": 3, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1744", "line": 5, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 39}, {"ruleId": "1719", "severity": 1, "message": "1745", "line": 15, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 16, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 16, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1747", "line": 22, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 22, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1748", "line": 28, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 28, "endColumn": 25}, {"ruleId": "1729", "severity": 1, "message": "1749", "line": 71, "column": 31, "nodeType": "1721", "endLine": 71, "endColumn": 42}, {"ruleId": "1729", "severity": 1, "message": "1750", "line": 266, "column": 8, "nodeType": "1731", "endLine": 266, "endColumn": 18, "suggestions": "1751"}, {"ruleId": "1719", "severity": 1, "message": "1745", "line": 6, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 9, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 9, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1747", "line": 18, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1748", "line": 20, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 20, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1752", "line": 26, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 26}, {"ruleId": "1719", "severity": 1, "message": "1753", "line": 11, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 11, "endColumn": 30}, {"ruleId": "1719", "severity": 1, "message": "1754", "line": 26, "column": 21, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 26}, {"ruleId": "1719", "severity": 1, "message": "1727", "line": 26, "column": 41, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 51}, {"ruleId": "1729", "severity": 1, "message": "1755", "line": 201, "column": 8, "nodeType": "1731", "endLine": 201, "endColumn": 19, "suggestions": "1756"}, {"ruleId": "1719", "severity": 1, "message": "1744", "line": 6, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 39}, {"ruleId": "1719", "severity": 1, "message": "1728", "line": 14, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 36}, {"ruleId": "1719", "severity": 1, "message": "1742", "line": 2, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1757", "line": 6, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1758", "line": 7, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1759", "line": 8, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1760", "line": 1, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1761", "line": 37, "column": 19, "nodeType": "1721", "messageId": "1722", "endLine": 37, "endColumn": 27}, {"ruleId": "1762", "severity": 1, "message": "1763", "line": 140, "column": 80, "nodeType": "1764", "messageId": "1765", "endLine": 140, "endColumn": 82}, {"ruleId": "1729", "severity": 1, "message": "1766", "line": 18, "column": 8, "nodeType": "1731", "endLine": 18, "endColumn": 10, "suggestions": "1767"}, {"ruleId": "1719", "severity": 1, "message": "1724", "line": 1, "column": 20, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1768", "line": 4, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 23}, {"ruleId": "1719", "severity": 1, "message": "1769", "line": 11, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 11, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1770", "line": 11, "column": 53, "nodeType": "1721", "messageId": "1722", "endLine": 11, "endColumn": 62}, {"ruleId": "1719", "severity": 1, "message": "1771", "line": 12, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1772", "line": 12, "column": 16, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1773", "line": 27, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 27, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1774", "line": 5, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1775", "line": 11, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 11, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 18, "column": 36, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 46}, {"ruleId": "1729", "severity": 1, "message": "1777", "line": 43, "column": 8, "nodeType": "1731", "endLine": 43, "endColumn": 26, "suggestions": "1778"}, {"ruleId": "1719", "severity": 1, "message": "1724", "line": 2, "column": 20, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1779", "line": 7, "column": 47, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 68}, {"ruleId": "1719", "severity": 1, "message": "1780", "line": 17, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1781", "line": 29, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 29, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1782", "line": 36, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 36, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1775", "line": 7, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 15, "column": 36, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 46}, {"ruleId": "1719", "severity": 1, "message": "1783", "line": 19, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 19, "endColumn": 37}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 15, "column": 36, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 46}, {"ruleId": "1729", "severity": 1, "message": "1784", "line": 34, "column": 8, "nodeType": "1731", "endLine": 34, "endColumn": 53, "suggestions": "1785"}, {"ruleId": "1719", "severity": 1, "message": "1786", "line": 12, "column": 55, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 61}, {"ruleId": "1719", "severity": 1, "message": "1787", "line": 12, "column": 77, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 82}, {"ruleId": "1719", "severity": 1, "message": "1788", "line": 12, "column": 84, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 94}, {"ruleId": "1719", "severity": 1, "message": "1747", "line": 17, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 19}, {"ruleId": "1729", "severity": 1, "message": "1749", "line": 38, "column": 31, "nodeType": "1721", "endLine": 38, "endColumn": 42}, {"ruleId": "1719", "severity": 1, "message": "1789", "line": 93, "column": 17, "nodeType": "1721", "messageId": "1722", "endLine": 93, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1743", "line": 2, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1744", "line": 7, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 39}, {"ruleId": "1719", "severity": 1, "message": "1790", "line": 24, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 24, "endColumn": 26}, {"ruleId": "1719", "severity": 1, "message": "1791", "line": 25, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 25, "endColumn": 30}, {"ruleId": "1719", "severity": 1, "message": "1792", "line": 47, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 47, "endColumn": 22}, {"ruleId": "1729", "severity": 1, "message": "1793", "line": 68, "column": 8, "nodeType": "1731", "endLine": 68, "endColumn": 30, "suggestions": "1794"}, {"ruleId": "1719", "severity": 1, "message": "1795", "line": 78, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 78, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1796", "line": 86, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 86, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1797", "line": 8, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 28}, {"ruleId": "1729", "severity": 1, "message": "1798", "line": 45, "column": 8, "nodeType": "1731", "endLine": 45, "endColumn": 32, "suggestions": "1799"}, {"ruleId": "1719", "severity": 1, "message": "1800", "line": 6, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1801", "line": 7, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1745", "line": 13, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 14, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1748", "line": 23, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 23, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1802", "line": 5, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1801", "line": 6, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1797", "line": 7, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 28}, {"ruleId": "1729", "severity": 1, "message": "1803", "line": 51, "column": 8, "nodeType": "1731", "endLine": 51, "endColumn": 28, "suggestions": "1804"}, {"ruleId": "1719", "severity": 1, "message": "1805", "line": 1, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1806", "line": 10, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 10, "endColumn": 17}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 286, "column": 59, "nodeType": "1809", "messageId": "1810", "endLine": 286, "endColumn": 60, "suggestions": "1811"}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 11, "column": 41, "nodeType": "1721", "messageId": "1722", "endLine": 11, "endColumn": 51}, {"ruleId": "1719", "severity": 1, "message": "1812", "line": 1, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 28}, {"ruleId": "1719", "severity": 1, "message": "1813", "line": 2, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1724", "line": 3, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1814", "line": 7, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1815", "line": 12, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 31}, {"ruleId": "1719", "severity": 1, "message": "1816", "line": 2, "column": 37, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 45}, {"ruleId": "1719", "severity": 1, "message": "1817", "line": 2, "column": 47, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 60}, {"ruleId": "1719", "severity": 1, "message": "1818", "line": 2, "column": 62, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 67}, {"ruleId": "1719", "severity": 1, "message": "1819", "line": 2, "column": 69, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 77}, {"ruleId": "1719", "severity": 1, "message": "1820", "line": 3, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 26}, {"ruleId": "1729", "severity": 1, "message": "1821", "line": 36, "column": 8, "nodeType": "1731", "endLine": 36, "endColumn": 43, "suggestions": "1822"}, {"ruleId": "1719", "severity": 1, "message": "1805", "line": 1, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1823", "line": 7, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 18}, {"ruleId": "1729", "severity": 1, "message": "1824", "line": 25, "column": 8, "nodeType": "1731", "endLine": 25, "endColumn": 20, "suggestions": "1825"}, {"ruleId": "1719", "severity": 1, "message": "1826", "line": 1, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1827", "line": 32, "column": 83, "nodeType": "1721", "messageId": "1722", "endLine": 32, "endColumn": 91}, {"ruleId": "1719", "severity": 1, "message": "1828", "line": 121, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 121, "endColumn": 23}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 159, "column": 22, "nodeType": "1831", "messageId": "1832", "endLine": 159, "endColumn": 24}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 159, "column": 91, "nodeType": "1831", "messageId": "1832", "endLine": 159, "endColumn": 93}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 304, "column": 40, "nodeType": "1831", "messageId": "1832", "endLine": 304, "endColumn": 42}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 304, "column": 109, "nodeType": "1831", "messageId": "1832", "endLine": 304, "endColumn": 111}, {"ruleId": "1719", "severity": 1, "message": "1833", "line": 14, "column": 38, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 47}, {"ruleId": "1719", "severity": 1, "message": "1834", "line": 14, "column": 49, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 56}, {"ruleId": "1719", "severity": 1, "message": "1835", "line": 14, "column": 58, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 70}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 34, "column": 40, "nodeType": "1831", "messageId": "1832", "endLine": 34, "endColumn": 42}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 34, "column": 109, "nodeType": "1831", "messageId": "1832", "endLine": 34, "endColumn": 111}, {"ruleId": "1719", "severity": 1, "message": "1754", "line": 15, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1836", "line": 15, "column": 44, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 52}, {"ruleId": "1729", "severity": 1, "message": "1837", "line": 42, "column": 8, "nodeType": "1731", "endLine": 42, "endColumn": 40, "suggestions": "1838"}, {"ruleId": "1719", "severity": 1, "message": "1823", "line": 1, "column": 25, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 33}, {"ruleId": "1719", "severity": 1, "message": "1839", "line": 4, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1840", "line": 4, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1841", "line": 1, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1725", "line": 2, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1842", "line": 2, "column": 16, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1843", "line": 4, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 28}, {"ruleId": "1719", "severity": 1, "message": "1844", "line": 4, "column": 46, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 67}, {"ruleId": "1719", "severity": 1, "message": "1845", "line": 13, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 26}, {"ruleId": "1719", "severity": 1, "message": "1846", "line": 47, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 47, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1847", "line": 54, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 54, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1848", "line": 61, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 61, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1849", "line": 77, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 77, "endColumn": 23}, {"ruleId": "1729", "severity": 1, "message": "1850", "line": 29, "column": 8, "nodeType": "1731", "endLine": 29, "endColumn": 10, "suggestions": "1851"}, {"ruleId": "1733", "severity": 1, "message": "1734", "line": 44, "column": 29, "nodeType": "1735", "endLine": 44, "endColumn": 98}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 229, "column": 30, "nodeType": "1809", "messageId": "1810", "endLine": 229, "endColumn": 31, "suggestions": "1852"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 242, "column": 61, "nodeType": "1809", "messageId": "1810", "endLine": 242, "endColumn": 62, "suggestions": "1853"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 248, "column": 29, "nodeType": "1809", "messageId": "1810", "endLine": 248, "endColumn": 30, "suggestions": "1854"}, {"ruleId": "1807", "severity": 1, "message": "1855", "line": 248, "column": 31, "nodeType": "1809", "messageId": "1810", "endLine": 248, "endColumn": 32, "suggestions": "1856"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 249, "column": 51, "nodeType": "1809", "messageId": "1810", "endLine": 249, "endColumn": 52, "suggestions": "1857"}, {"ruleId": "1807", "severity": 1, "message": "1855", "line": 249, "column": 53, "nodeType": "1809", "messageId": "1810", "endLine": 249, "endColumn": 54, "suggestions": "1858"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 330, "column": 30, "nodeType": "1809", "messageId": "1810", "endLine": 330, "endColumn": 31, "suggestions": "1859"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 352, "column": 61, "nodeType": "1809", "messageId": "1810", "endLine": 352, "endColumn": 62, "suggestions": "1860"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 358, "column": 29, "nodeType": "1809", "messageId": "1810", "endLine": 358, "endColumn": 30, "suggestions": "1861"}, {"ruleId": "1807", "severity": 1, "message": "1855", "line": 358, "column": 31, "nodeType": "1809", "messageId": "1810", "endLine": 358, "endColumn": 32, "suggestions": "1862"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 359, "column": 51, "nodeType": "1809", "messageId": "1810", "endLine": 359, "endColumn": 52, "suggestions": "1863"}, {"ruleId": "1807", "severity": 1, "message": "1855", "line": 359, "column": 53, "nodeType": "1809", "messageId": "1810", "endLine": 359, "endColumn": 54, "suggestions": "1864"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 433, "column": 30, "nodeType": "1809", "messageId": "1810", "endLine": 433, "endColumn": 31, "suggestions": "1865"}, {"ruleId": "1807", "severity": 1, "message": "1808", "line": 452, "column": 61, "nodeType": "1809", "messageId": "1810", "endLine": 452, "endColumn": 62, "suggestions": "1866"}, {"ruleId": "1729", "severity": 1, "message": "1867", "line": 80, "column": 8, "nodeType": "1731", "endLine": 80, "endColumn": 10, "suggestions": "1868"}, {"ruleId": "1733", "severity": 1, "message": "1734", "line": 56, "column": 37, "nodeType": "1735", "endLine": 56, "endColumn": 107}, {"ruleId": "1719", "severity": 1, "message": "1869", "line": 3, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 27}, {"ruleId": "1729", "severity": 1, "message": "1870", "line": 73, "column": 8, "nodeType": "1731", "endLine": 73, "endColumn": 25, "suggestions": "1871"}, {"ruleId": "1719", "severity": 1, "message": "1724", "line": 3, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1872", "line": 5, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1873", "line": 13, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1874", "line": 14, "column": 28, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 45}, {"ruleId": "1719", "severity": 1, "message": "1875", "line": 34, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 34, "endColumn": 20}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 116, "column": 56, "nodeType": "1764", "messageId": "1765", "endLine": 116, "endColumn": 58}, {"ruleId": "1719", "severity": 1, "message": "1725", "line": 1, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1877", "line": 7, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 21}, {"ruleId": "1878", "severity": 1, "message": "1879", "line": 12, "column": 25, "nodeType": "1735", "endLine": 12, "endColumn": 614}, {"ruleId": "1719", "severity": 1, "message": "1805", "line": 1, "column": 46, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 49}, {"ruleId": "1719", "severity": 1, "message": "1880", "line": 3, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1881", "line": 14, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1882", "line": 14, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 37}, {"ruleId": "1729", "severity": 1, "message": "1883", "line": 21, "column": 8, "nodeType": "1731", "endLine": 21, "endColumn": 26, "suggestions": "1884"}, {"ruleId": "1729", "severity": 1, "message": "1885", "line": 40, "column": 8, "nodeType": "1731", "endLine": 40, "endColumn": 10, "suggestions": "1886"}, {"ruleId": "1719", "severity": 1, "message": "1736", "line": 2, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 11}, {"ruleId": "1719", "severity": 1, "message": "1743", "line": 3, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 3, "endColumn": 21}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 140, "column": 56, "nodeType": "1764", "messageId": "1765", "endLine": 140, "endColumn": 58}, {"ruleId": "1719", "severity": 1, "message": "1872", "line": 4, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1887", "line": 5, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1888", "line": 100, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 100, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1889", "line": 111, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 111, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1769", "line": 15, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1759", "line": 7, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 36}, {"ruleId": "1719", "severity": 1, "message": "1769", "line": 13, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1769", "line": 14, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1759", "line": 7, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 36}, {"ruleId": "1719", "severity": 1, "message": "1769", "line": 13, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1759", "line": 7, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 36}, {"ruleId": "1890", "severity": 1, "message": "1891", "line": 28, "column": 74, "nodeType": "1764", "messageId": "1892", "endLine": 28, "endColumn": 75}, {"ruleId": "1719", "severity": 1, "message": "1893", "line": 6, "column": 25, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1894", "line": 6, "column": 31, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 37}, {"ruleId": "1719", "severity": 1, "message": "1788", "line": 6, "column": 39, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 49}, {"ruleId": "1719", "severity": 1, "message": "1818", "line": 6, "column": 51, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 56}, {"ruleId": "1719", "severity": 1, "message": "1895", "line": 6, "column": 65, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 70}, {"ruleId": "1729", "severity": 1, "message": "1896", "line": 50, "column": 8, "nodeType": "1731", "endLine": 50, "endColumn": 28, "suggestions": "1897"}, {"ruleId": "1729", "severity": 1, "message": "1896", "line": 54, "column": 8, "nodeType": "1731", "endLine": 54, "endColumn": 21, "suggestions": "1898"}, {"ruleId": "1719", "severity": 1, "message": "1899", "line": 37, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 37, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1900", "line": 44, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 44, "endColumn": 29}, {"ruleId": "1729", "severity": 1, "message": "1901", "line": 47, "column": 9, "nodeType": "1902", "endLine": 51, "endColumn": 4}, {"ruleId": "1719", "severity": 1, "message": "1903", "line": 16, "column": 45, "nodeType": "1721", "messageId": "1722", "endLine": 16, "endColumn": 57}, {"ruleId": "1729", "severity": 1, "message": "1904", "line": 54, "column": 8, "nodeType": "1731", "endLine": 54, "endColumn": 14, "suggestions": "1905"}, {"ruleId": "1719", "severity": 1, "message": "1906", "line": 106, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 106, "endColumn": 22}, {"ruleId": "1729", "severity": 1, "message": "1907", "line": 221, "column": 8, "nodeType": "1731", "endLine": 221, "endColumn": 23, "suggestions": "1908"}, {"ruleId": "1719", "severity": 1, "message": "1909", "line": 272, "column": 19, "nodeType": "1721", "messageId": "1722", "endLine": 272, "endColumn": 25}, {"ruleId": "1729", "severity": 1, "message": "1749", "line": 360, "column": 37, "nodeType": "1721", "endLine": 360, "endColumn": 48}, {"ruleId": "1729", "severity": 1, "message": "1749", "line": 387, "column": 37, "nodeType": "1721", "endLine": 387, "endColumn": 48}, {"ruleId": "1729", "severity": 1, "message": "1910", "line": 730, "column": 8, "nodeType": "1731", "endLine": 730, "endColumn": 99, "suggestions": "1911"}, {"ruleId": "1729", "severity": 1, "message": "1912", "line": 838, "column": 8, "nodeType": "1731", "endLine": 838, "endColumn": 36, "suggestions": "1913"}, {"ruleId": "1719", "severity": 1, "message": "1914", "line": 2, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1915", "line": 4, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1916", "line": 18, "column": 25, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1917", "line": 18, "column": 137, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 146}, {"ruleId": "1719", "severity": 1, "message": "1918", "line": 18, "column": 148, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 161}, {"ruleId": "1719", "severity": 1, "message": "1919", "line": 18, "column": 163, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 173}, {"ruleId": "1719", "severity": 1, "message": "1920", "line": 18, "column": 175, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 182}, {"ruleId": "1719", "severity": 1, "message": "1921", "line": 18, "column": 184, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 189}, {"ruleId": "1719", "severity": 1, "message": "1922", "line": 18, "column": 191, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 205}, {"ruleId": "1719", "severity": 1, "message": "1923", "line": 34, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 34, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1924", "line": 39, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 39, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1925", "line": 40, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 40, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1926", "line": 64, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 64, "endColumn": 17}, {"ruleId": "1719", "severity": 1, "message": "1927", "line": 65, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 65, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1928", "line": 67, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 67, "endColumn": 17}, {"ruleId": "1719", "severity": 1, "message": "1805", "line": 4, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1929", "line": 6, "column": 35, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 46}, {"ruleId": "1719", "severity": 1, "message": "1930", "line": 10, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 10, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 26, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 21}, {"ruleId": "1729", "severity": 1, "message": "1904", "line": 37, "column": 8, "nodeType": "1731", "endLine": 37, "endColumn": 14, "suggestions": "1931"}, {"ruleId": "1719", "severity": 1, "message": "1839", "line": 15, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1932", "line": 18, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 18, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1736", "line": 21, "column": 98, "nodeType": "1721", "messageId": "1722", "endLine": 21, "endColumn": 99}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 262, "column": 38, "nodeType": "1764", "messageId": "1765", "endLine": 262, "endColumn": 40}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 7, "column": 111, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 115}, {"ruleId": "1719", "severity": 1, "message": "1933", "line": 7, "column": 117, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 127}, {"ruleId": "1719", "severity": 1, "message": "1826", "line": 10, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 10, "endColumn": 24}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 178, "column": 123, "nodeType": "1831", "messageId": "1832", "endLine": 178, "endColumn": 125}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 178, "column": 197, "nodeType": "1831", "messageId": "1832", "endLine": 178, "endColumn": 199}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 187, "column": 120, "nodeType": "1831", "messageId": "1832", "endLine": 187, "endColumn": 122}, {"ruleId": "1829", "severity": 1, "message": "1830", "line": 187, "column": 189, "nodeType": "1831", "messageId": "1832", "endLine": 187, "endColumn": 191}, {"ruleId": "1719", "severity": 1, "message": "1816", "line": 15, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1786", "line": 17, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 11}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 20, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 20, "endColumn": 9}, {"ruleId": "1719", "severity": 1, "message": "1934", "line": 21, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 21, "endColumn": 17}, {"ruleId": "1719", "severity": 1, "message": "1787", "line": 24, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 24, "endColumn": 10}, {"ruleId": "1719", "severity": 1, "message": "1839", "line": 28, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 28, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1935", "line": 30, "column": 7, "nodeType": "1721", "messageId": "1722", "endLine": 30, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1936", "line": 159, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 159, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1937", "line": 160, "column": 21, "nodeType": "1721", "messageId": "1722", "endLine": 160, "endColumn": 31}, {"ruleId": "1729", "severity": 1, "message": "1938", "line": 166, "column": 8, "nodeType": "1731", "endLine": 166, "endColumn": 18, "suggestions": "1939"}, {"ruleId": "1719", "severity": 1, "message": "1940", "line": 26, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 18}, {"ruleId": "1729", "severity": 1, "message": "1941", "line": 199, "column": 8, "nodeType": "1731", "endLine": 199, "endColumn": 21, "suggestions": "1942"}, {"ruleId": "1719", "severity": 1, "message": "1943", "line": 34, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 34, "endColumn": 20}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 49, "column": 37, "nodeType": "1764", "messageId": "1765", "endLine": 49, "endColumn": 39}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 51, "column": 44, "nodeType": "1764", "messageId": "1765", "endLine": 51, "endColumn": 46}, {"ruleId": "1719", "severity": 1, "message": "1944", "line": 8, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1736", "line": 8, "column": 18, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1819", "line": 8, "column": 21, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1945", "line": 15, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 15, "endColumn": 17}, {"ruleId": "1719", "severity": 1, "message": "1946", "line": 16, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 16, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1947", "line": 26, "column": 24, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 37}, {"ruleId": "1719", "severity": 1, "message": "1948", "line": 111, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 111, "endColumn": 23}, {"ruleId": "1719", "severity": 1, "message": "1839", "line": 7, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 13, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 9}, {"ruleId": "1719", "severity": 1, "message": "1816", "line": 17, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1949", "line": 19, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 19, "endColumn": 13}, {"ruleId": "1729", "severity": 1, "message": "1950", "line": 129, "column": 8, "nodeType": "1731", "endLine": 129, "endColumn": 18, "suggestions": "1951"}, {"ruleId": "1719", "severity": 1, "message": "1952", "line": 155, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 155, "endColumn": 32}, {"ruleId": "1719", "severity": 1, "message": "1953", "line": 232, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 232, "endColumn": 29}, {"ruleId": "1729", "severity": 1, "message": "1954", "line": 39, "column": 8, "nodeType": "1731", "endLine": 39, "endColumn": 30, "suggestions": "1955"}, {"ruleId": "1733", "severity": 1, "message": "1734", "line": 226, "column": 69, "nodeType": "1735", "endLine": 230, "endColumn": 71}, {"ruleId": "1762", "severity": 1, "message": "1763", "line": 49, "column": 108, "nodeType": "1764", "messageId": "1765", "endLine": 49, "endColumn": 110}, {"ruleId": "1719", "severity": 1, "message": "1754", "line": 55, "column": 42, "nodeType": "1721", "messageId": "1722", "endLine": 55, "endColumn": 47}, {"ruleId": "1729", "severity": 1, "message": "1896", "line": 88, "column": 8, "nodeType": "1731", "endLine": 88, "endColumn": 28, "suggestions": "1956"}, {"ruleId": "1729", "severity": 1, "message": "1896", "line": 92, "column": 8, "nodeType": "1731", "endLine": 92, "endColumn": 21, "suggestions": "1957"}, {"ruleId": "1719", "severity": 1, "message": "1873", "line": 5, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 16}, {"ruleId": "1719", "severity": 1, "message": "1958", "line": 7, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 7, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1959", "line": 8, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 12}, {"ruleId": "1719", "severity": 1, "message": "1960", "line": 13, "column": 3, "nodeType": "1721", "messageId": "1722", "endLine": 13, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1874", "line": 14, "column": 3, "nodeType": "1721", "messageId": "1722", "endLine": 14, "endColumn": 20}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 285, "column": 65, "nodeType": "1764", "messageId": "1765", "endLine": 285, "endColumn": 67}, {"ruleId": "1719", "severity": 1, "message": "1961", "line": 28, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 28, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1788", "line": 29, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 29, "endColumn": 15}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 406, "column": 101, "nodeType": "1764", "messageId": "1765", "endLine": 406, "endColumn": 103}, {"ruleId": "1719", "severity": 1, "message": "1962", "line": 55, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 55, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1963", "line": 55, "column": 32, "nodeType": "1721", "messageId": "1722", "endLine": 55, "endColumn": 42}, {"ruleId": "1719", "severity": 1, "message": "1964", "line": 59, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 59, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1965", "line": 70, "column": 12, "nodeType": "1721", "messageId": "1722", "endLine": 70, "endColumn": 23}, {"ruleId": "1719", "severity": 1, "message": "1966", "line": 82, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 82, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1967", "line": 88, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 88, "endColumn": 30}, {"ruleId": "1719", "severity": 1, "message": "1952", "line": 94, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 94, "endColumn": 32}, {"ruleId": "1762", "severity": 1, "message": "1763", "line": 332, "column": 35, "nodeType": "1764", "messageId": "1765", "endLine": 332, "endColumn": 37}, {"ruleId": "1719", "severity": 1, "message": "1775", "line": 9, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 9, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1968", "line": 11, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 11, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1786", "line": 19, "column": 3, "nodeType": "1721", "messageId": "1722", "endLine": 19, "endColumn": 9}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 31, "column": 30, "nodeType": "1721", "messageId": "1722", "endLine": 31, "endColumn": 40}, {"ruleId": "1719", "severity": 1, "message": "1969", "line": 4, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1970", "line": 5, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 14}, {"ruleId": "1719", "severity": 1, "message": "1971", "line": 17, "column": 26, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 46}, {"ruleId": "1719", "severity": 1, "message": "1972", "line": 24, "column": 88, "nodeType": "1721", "messageId": "1722", "endLine": 24, "endColumn": 92}, {"ruleId": "1719", "severity": 1, "message": "1786", "line": 24, "column": 94, "nodeType": "1721", "messageId": "1722", "endLine": 24, "endColumn": 100}, {"ruleId": "1719", "severity": 1, "message": "1742", "line": 26, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 26, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1973", "line": 27, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 27, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1747", "line": 33, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 33, "endColumn": 17}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 36, "column": 17, "nodeType": "1721", "messageId": "1722", "endLine": 36, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1974", "line": 88, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 88, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1975", "line": 89, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 89, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1976", "line": 95, "column": 20, "nodeType": "1721", "messageId": "1722", "endLine": 95, "endColumn": 31}, {"ruleId": "1719", "severity": 1, "message": "1977", "line": 96, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 96, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1978", "line": 96, "column": 22, "nodeType": "1721", "messageId": "1722", "endLine": 96, "endColumn": 35}, {"ruleId": "1719", "severity": 1, "message": "1979", "line": 97, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 97, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1980", "line": 97, "column": 20, "nodeType": "1721", "messageId": "1722", "endLine": 97, "endColumn": 31}, {"ruleId": "1719", "severity": 1, "message": "1981", "line": 100, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 100, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1982", "line": 101, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 101, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1983", "line": 102, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 102, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1984", "line": 103, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 103, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1985", "line": 132, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 132, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1986", "line": 133, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 133, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1987", "line": 135, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 135, "endColumn": 23}, {"ruleId": "1719", "severity": 1, "message": "1988", "line": 135, "column": 25, "nodeType": "1721", "messageId": "1722", "endLine": 135, "endColumn": 41}, {"ruleId": "1719", "severity": 1, "message": "1989", "line": 163, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 163, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1990", "line": 169, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 169, "endColumn": 33}, {"ruleId": "1729", "severity": 1, "message": "1991", "line": 280, "column": 6, "nodeType": "1731", "endLine": 280, "endColumn": 32, "suggestions": "1992"}, {"ruleId": "1719", "severity": 1, "message": "1993", "line": 358, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 358, "endColumn": 23}, {"ruleId": "1729", "severity": 1, "message": "1994", "line": 725, "column": 6, "nodeType": "1731", "endLine": 725, "endColumn": 25, "suggestions": "1995"}, {"ruleId": "1719", "severity": 1, "message": "1996", "line": 751, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 751, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1997", "line": 763, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 763, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1968", "line": 8, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1787", "line": 19, "column": 3, "nodeType": "1721", "messageId": "1722", "endLine": 19, "endColumn": 8}, {"ruleId": "1719", "severity": 1, "message": "1998", "line": 20, "column": 3, "nodeType": "1721", "messageId": "1722", "endLine": 20, "endColumn": 10}, {"ruleId": "1719", "severity": 1, "message": "1999", "line": 30, "column": 36, "nodeType": "1721", "messageId": "1722", "endLine": 30, "endColumn": 56}, {"ruleId": "1719", "severity": 1, "message": "2000", "line": 32, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 32, "endColumn": 30}, {"ruleId": "1719", "severity": 1, "message": "2001", "line": 32, "column": 32, "nodeType": "1721", "messageId": "1722", "endLine": 32, "endColumn": 55}, {"ruleId": "1729", "severity": 1, "message": "1749", "line": 35, "column": 29, "nodeType": "1721", "endLine": 35, "endColumn": 40}, {"ruleId": "1729", "severity": 1, "message": "1749", "line": 35, "column": 30, "nodeType": "1721", "endLine": 35, "endColumn": 41}, {"ruleId": "1762", "severity": 1, "message": "1876", "line": 330, "column": 68, "nodeType": "1764", "messageId": "1765", "endLine": 330, "endColumn": 70}, {"ruleId": "1762", "severity": 1, "message": "1763", "line": 343, "column": 64, "nodeType": "1764", "messageId": "1765", "endLine": 343, "endColumn": 66}, {"ruleId": "1719", "severity": 1, "message": "1805", "line": 1, "column": 10, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "2002", "line": 12, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 12, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1745", "line": 23, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 23, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1746", "line": 25, "column": 119, "nodeType": "1721", "messageId": "1722", "endLine": 25, "endColumn": 123}, {"ruleId": "1719", "severity": 1, "message": "2003", "line": 25, "column": 135, "nodeType": "1721", "messageId": "1722", "endLine": 25, "endColumn": 140}, {"ruleId": "1719", "severity": 1, "message": "1747", "line": 32, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 32, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1776", "line": 40, "column": 9, "nodeType": "1721", "messageId": "1722", "endLine": 40, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1760", "line": 4, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 4, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1786", "line": 17, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 11}, {"ruleId": "1719", "severity": 1, "message": "1737", "line": 20, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 20, "endColumn": 9}, {"ruleId": "1719", "severity": 1, "message": "2004", "line": 27, "column": 5, "nodeType": "1721", "messageId": "1722", "endLine": 27, "endColumn": 32}, {"ruleId": "1729", "severity": 1, "message": "2005", "line": 122, "column": 8, "nodeType": "1731", "endLine": 122, "endColumn": 10, "suggestions": "2006"}, {"ruleId": "1719", "severity": 1, "message": "1819", "line": 6, "column": 56, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 64}, {"ruleId": "1719", "severity": 1, "message": "1894", "line": 6, "column": 73, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 79}, {"ruleId": "1719", "severity": 1, "message": "1788", "line": 6, "column": 81, "nodeType": "1721", "messageId": "1722", "endLine": 6, "endColumn": 91}, {"ruleId": "1719", "severity": 1, "message": "2007", "line": 5, "column": 60, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 66}, {"ruleId": "1719", "severity": 1, "message": "1944", "line": 5, "column": 68, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 74}, {"ruleId": "1719", "severity": 1, "message": "2008", "line": 2, "column": 42, "nodeType": "1721", "messageId": "1722", "endLine": 2, "endColumn": 51}, {"ruleId": "1719", "severity": 1, "message": "2009", "line": 5, "column": 11, "nodeType": "1721", "messageId": "1722", "endLine": 5, "endColumn": 24}, {"ruleId": "1719", "severity": 1, "message": "1839", "line": 8, "column": 8, "nodeType": "1721", "messageId": "1722", "endLine": 8, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1943", "line": 16, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 16, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "2010", "line": 17, "column": 13, "nodeType": "1721", "messageId": "1722", "endLine": 17, "endColumn": 20}, {"ruleId": "1719", "severity": 1, "message": "1823", "line": 1, "column": 21, "nodeType": "1721", "messageId": "1722", "endLine": 1, "endColumn": 29}, "no-unused-vars", "'Dashboard' is defined but never used.", "Identifier", "unusedVar", "'AdminUserSearchPage' is defined but never used.", "'useEffect' is defined but never used.", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'totalItems' is assigned a value but never used.", "'isFilterVIew' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["2011"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2012"], "'showAddStudent' is assigned a value but never used.", "'ScoreDistributionChart' is defined but never used.", "'limit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'prevRanks'. Either include them or remove the dependency array.", ["2013"], "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'AdminLayout' is defined but never used.", "'errorMsg' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2014"], "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2015"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2016"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2017"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2018"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2019"], "'use' is defined but never used.", "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2020", "2021"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2022"], "'useState' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2023"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2024"], "'LoadingSpinner' is defined but never used.", "'ChevronRight' is defined but never used.", "'BeeMathLogo' is defined but never used.", "'useLocation' is defined but never used.", "'toggleCloseSidebar' is defined but never used.", "'toggleTuitionDropdown' is defined but never used.", "'tuitionDropdown' is assigned a value but never used.", "'icon3' is assigned a value but never used.", "'icon4' is assigned a value but never used.", "'iconAttendance' is assigned a value but never used.", "'notification' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2025"], ["2026", "2027"], ["2028", "2029"], ["2030", "2031"], "Unnecessary escape character: \\).", ["2032", "2033"], ["2034", "2035"], ["2036", "2037"], ["2038", "2039"], ["2040", "2041"], ["2042", "2043"], ["2044", "2045"], ["2046", "2047"], ["2048", "2049"], ["2050", "2051"], ["2052", "2053"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2054"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2055"], "'InputSearch' is defined but never used.", "'socket' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2056"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2057"], "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2058"], ["2059"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2060"], "'addQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2061"], "'result' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2062"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2063"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'setAttempts' is defined but never used.", "'ScoreBarChart' is defined but never used.", ["2064"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2065"], "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2066"], "'loading' is assigned a value but never used.", "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2067"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2068"], ["2069"], ["2070"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'formatCurrency' is defined but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'formatNumberWithDots' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2071"], "'handleBatchAdd' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tuitionPayments'. Either include it or remove the dependency array.", ["2072"], "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'Receipt' is defined but never used.", "'studentClassTuitions' is assigned a value but never used.", "'classTuitionsLoading' is assigned a value but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2073"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", {"desc": "2074", "fix": "2075"}, {"desc": "2076", "fix": "2077"}, {"desc": "2078", "fix": "2079"}, {"desc": "2080", "fix": "2081"}, {"desc": "2082", "fix": "2083"}, {"desc": "2084", "fix": "2085"}, {"desc": "2086", "fix": "2087"}, {"desc": "2088", "fix": "2089"}, {"desc": "2090", "fix": "2091"}, {"messageId": "2092", "fix": "2093", "desc": "2094"}, {"messageId": "2095", "fix": "2096", "desc": "2097"}, {"desc": "2098", "fix": "2099"}, {"desc": "2100", "fix": "2101"}, {"desc": "2102", "fix": "2103"}, {"desc": "2104", "fix": "2105"}, {"messageId": "2092", "fix": "2106", "desc": "2094"}, {"messageId": "2095", "fix": "2107", "desc": "2097"}, {"messageId": "2092", "fix": "2108", "desc": "2094"}, {"messageId": "2095", "fix": "2109", "desc": "2097"}, {"messageId": "2092", "fix": "2110", "desc": "2094"}, {"messageId": "2095", "fix": "2111", "desc": "2097"}, {"messageId": "2092", "fix": "2112", "desc": "2094"}, {"messageId": "2095", "fix": "2113", "desc": "2097"}, {"messageId": "2092", "fix": "2114", "desc": "2094"}, {"messageId": "2095", "fix": "2115", "desc": "2097"}, {"messageId": "2092", "fix": "2116", "desc": "2094"}, {"messageId": "2095", "fix": "2117", "desc": "2097"}, {"messageId": "2092", "fix": "2118", "desc": "2094"}, {"messageId": "2095", "fix": "2119", "desc": "2097"}, {"messageId": "2092", "fix": "2120", "desc": "2094"}, {"messageId": "2095", "fix": "2121", "desc": "2097"}, {"messageId": "2092", "fix": "2122", "desc": "2094"}, {"messageId": "2095", "fix": "2123", "desc": "2097"}, {"messageId": "2092", "fix": "2124", "desc": "2094"}, {"messageId": "2095", "fix": "2125", "desc": "2097"}, {"messageId": "2092", "fix": "2126", "desc": "2094"}, {"messageId": "2095", "fix": "2127", "desc": "2097"}, {"messageId": "2092", "fix": "2128", "desc": "2094"}, {"messageId": "2095", "fix": "2129", "desc": "2097"}, {"messageId": "2092", "fix": "2130", "desc": "2094"}, {"messageId": "2095", "fix": "2131", "desc": "2097"}, {"messageId": "2092", "fix": "2132", "desc": "2094"}, {"messageId": "2095", "fix": "2133", "desc": "2097"}, {"desc": "2134", "fix": "2135"}, {"desc": "2136", "fix": "2137"}, {"desc": "2138", "fix": "2139"}, {"desc": "2140", "fix": "2141"}, {"desc": "2142", "fix": "2143"}, {"desc": "2144", "fix": "2145"}, {"desc": "2146", "fix": "2147"}, {"desc": "2148", "fix": "2149"}, {"desc": "2150", "fix": "2151"}, {"desc": "2152", "fix": "2153"}, {"desc": "2146", "fix": "2154"}, {"desc": "2155", "fix": "2156"}, {"desc": "2157", "fix": "2158"}, {"desc": "2159", "fix": "2160"}, {"desc": "2161", "fix": "2162"}, {"desc": "2142", "fix": "2163"}, {"desc": "2144", "fix": "2164"}, {"desc": "2165", "fix": "2166"}, {"desc": "2167", "fix": "2168"}, {"desc": "2169", "fix": "2170"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2171", "text": "2172"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2173", "text": "2174"}, "Update the dependencies array to be: [attempts1, dispatch, prevRanks]", {"range": "2175", "text": "2176"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2177", "text": "2178"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2179", "text": "2180"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2181", "text": "2182"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2183", "text": "2184"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2185", "text": "2186"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2187", "text": "2188"}, "removeEscape", {"range": "2189", "text": "2190"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2191", "text": "2192"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2193", "text": "2194"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2195", "text": "2196"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2197", "text": "2198"}, "Update the dependencies array to be: [maxLength]", {"range": "2199", "text": "2200"}, {"range": "2201", "text": "2190"}, {"range": "2202", "text": "2192"}, {"range": "2203", "text": "2190"}, {"range": "2204", "text": "2192"}, {"range": "2205", "text": "2190"}, {"range": "2206", "text": "2192"}, {"range": "2207", "text": "2190"}, {"range": "2208", "text": "2192"}, {"range": "2209", "text": "2190"}, {"range": "2210", "text": "2192"}, {"range": "2211", "text": "2190"}, {"range": "2212", "text": "2192"}, {"range": "2213", "text": "2190"}, {"range": "2214", "text": "2192"}, {"range": "2215", "text": "2190"}, {"range": "2216", "text": "2192"}, {"range": "2217", "text": "2190"}, {"range": "2218", "text": "2192"}, {"range": "2219", "text": "2190"}, {"range": "2220", "text": "2192"}, {"range": "2221", "text": "2190"}, {"range": "2222", "text": "2192"}, {"range": "2223", "text": "2190"}, {"range": "2224", "text": "2192"}, {"range": "2225", "text": "2190"}, {"range": "2226", "text": "2192"}, {"range": "2227", "text": "2190"}, {"range": "2228", "text": "2192"}, "Update the dependencies array to be: [handlePaste]", {"range": "2229", "text": "2230"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2231", "text": "2232"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2233", "text": "2234"}, "Update the dependencies array to be: [handleFile]", {"range": "2235", "text": "2236"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2237", "text": "2238"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2239", "text": "2240"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2241", "text": "2242"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2243", "text": "2244"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2245", "text": "2246"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2247", "text": "2248"}, {"range": "2249", "text": "2242"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2250", "text": "2251"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2252", "text": "2253"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2254", "text": "2255"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2256", "text": "2257"}, {"range": "2258", "text": "2238"}, {"range": "2259", "text": "2240"}, "Update the dependencies array to be: [dispatch, filterClass, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2260", "text": "2261"}, "Update the dependencies array to be: [tuitionPayments, tuitionStatistics]", {"range": "2262", "text": "2263"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2264", "text": "2265"}, [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9489, 9499], "[dispatch, fetchPdfFiles]", [7946, 7957], "[attempts1, dispatch, prevRanks]", [669, 671], "[options, selected, type]", [1909, 1927], "[dispatch, fetchQuestions, params]", [1518, 1563], "[dispatch, search, page, pageSize, sortOrder, classId]", [2625, 2647], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2128, 2148], "[codes, exam, exam?.class]", [10595, 10596], "", [10595, 10595], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [8598, 8599], [8598, 8598], [9063, 9064], [9063, 9063], [9259, 9260], [9259, 9259], [9261, 9262], [9261, 9261], [9331, 9332], [9331, 9331], [9333, 9334], [9333, 9333], [11606, 11607], [11606, 11606], [12526, 12527], [12526, 12526], [12693, 12694], [12693, 12693], [12695, 12696], [12695, 12695], [12765, 12766], [12765, 12765], [12767, 12768], [12767, 12767], [15061, 15062], [15061, 15061], [15755, 15756], [15755, 15755], [2448, 2450], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2444, 2450], "[exam, examId, navigate]", [8671, 8686], "[flag, remainingTime]", [29299, 29390], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [33497, 33525], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1591, 1613], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [9986, 10012], "[dispatch, filterClass, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [23456, 23475], "[tuitionPayments, tuitionStatistics]", [3986, 3988], "[dispatch, userId]"]