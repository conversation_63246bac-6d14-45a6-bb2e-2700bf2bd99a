import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    dropdownOpen: false,
    tuitionDropdownOpen: false,
    closeSidebar: false
}

const sidebarSlice = createSlice({
    name: 'sidebar',
    initialState,
    reducers: {
        toggleDropdown: (state) => {
            state.dropdownOpen = !state.dropdownOpen;
            // Đóng dropdown khác khi mở dropdown này
            if (state.dropdownOpen) {
                state.tuitionDropdownOpen = false;
            }
        },
        toggleTuitionDropdown: (state) => {
            state.tuitionDropdownOpen = !state.tuitionDropdownOpen;
            // Đóng dropdown khác khi mở dropdown này
            if (state.tuitionDropdownOpen) {
                state.dropdownOpen = false;
            }
        },
        toggleCloseSidebar: (state) => {
            state.closeSidebar = !state.closeSidebar;
        }
    }
})

export const { toggleDropdown, toggleTuitionDropdown, toggleCloseSidebar } = sidebarSlice.actions;
export default sidebarSlice.reducer;