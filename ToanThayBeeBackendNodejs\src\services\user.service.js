// services/user.service.js
import bcrypt from 'bcryptjs';
import db from '../models/index.js';
import UserType from '../constants/UserType.js';
import UserResponse from '../dtos/responses/user/UserResponse.js';
import { cleanupUploadedFiles } from '../utils/imageUpload.js';
import { deleteUserFromAllSheets } from './googleSheets.service.js';
const { Op, where, fn, col } = db.Sequelize;
import ResponseDataPagination from '../dtos/responses/pagination/PaginationResponse.js';

export const checkUnique = async (field, value) => {
    const existingUser = await db.User.findOne({ where: { [field]: value } });
    return !existingUser;
};

export const createNewUser = async (user, type, transaction = null) => {
    const { username, phone, password } = user;

    try {
        // Check bắt buộc
        if (!username) {
            throw new Error('Thiếu username');
        }

        if (!phone) {
            throw new Error('Thiếu số điện thoại');
        }

        if (!password) {
            throw new Error('Thiếu mật khẩu');
        }

        if (!type) {
            throw new Error('Thiếu loại người dùng');
        }

        // Kiểm tra trùng lặp
        const isUsernameUnique = await checkUnique('username', username);
        if (!isUsernameUnique) {
            throw new Error('Username đã tồn tại');
        }

        // Tạo options cho transaction nếu có
        const options = transaction ? { transaction } : {};

        // Tạo người dùng
        const newUser = await db.User.create({
            ...user,
            username,
            password,
            userType: type,
            isActive: true
        }, options);
        // console.log('New user created:', newUser);

        return newUser;
    } catch (err) {
        throw err;
    }
};

export const createUserBulk = async (users) => {
    const results = {
        success: [],
        failed: [],
    };
    let index = 0;

    for (const user of users) {
        const { username, phone } = user;

        try {
            const newUser = await createNewUser(user);

            console.log(++index, newUser.toJSON());

            results.success.push(new UserResponse(newUser));
        } catch (err) {
            results.failed.push({
                user: username || phone || 'unknown',
                error: err.message,
            });
        }
    }

    return results;
};

/**
 * Tìm kiếm người dùng theo nhiều tiêu chí
 * @param {Object} options - Các tùy chọn tìm kiếm
 * @param {string} options.id - ID của người dùng
 * @param {string} options.username - Tên đăng nhập
 * @param {string} options.phone - Số điện thoại
 * @param {string} options.fullName - Họ và tên (tìm kiếm mờ)
 * @param {string} options.userType - Loại người dùng (STUDENT, TEACHER, ADMIN)
 * @param {string} options.status - Trạng thái người dùng
 * @param {number} options.page - Trang hiện tại (mặc định: 1)
 * @param {number} options.limit - Số lượng kết quả trên mỗi trang (mặc định: 10)
 * @param {string} options.sortBy - Trường để sắp xếp (mặc định: 'createdAt')
 * @param {string} options.sortOrder - Thứ tự sắp xếp: 'ASC' hoặc 'DESC' (mặc định: 'DESC')
 * @param {boolean} options.includeDeleted - Có bao gồm người dùng đã xóa không (mặc định: false)
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Kết quả tìm kiếm với phân trang
 */
export const findUser = async (options = {}, transaction = null) => {
    const {
        id,
        username,
        phone,
        fullName,
        userType,
        status,
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'DESC',
        includeDeleted = false
    } = options;

    // Xây dựng điều kiện tìm kiếm
    const whereClause = {};
    const { Op } = db.Sequelize;

    // Tìm theo ID
    if (id) {
        whereClause.id = id;
    }

    // Tìm theo username (chính xác)
    if (username) {
        whereClause.username = username;
    }

    // Tìm theo số điện thoại (chính xác)
    if (phone) {
        whereClause.phone = phone;
    }

    // Tìm theo họ tên (mờ)
    if (fullName) {
        whereClause.fullName = { [Op.like]: `%${fullName}%` };
    }

    // Tìm theo loại người dùng
    if (userType) {
        whereClause.userType = userType;
    }

    // Tìm theo trạng thái
    if (status) {
        whereClause.status = status;
    }

    // Xử lý paranoid (soft delete)
    const paranoid = !includeDeleted;

    // Tính toán offset cho phân trang
    const offset = (page - 1) * limit;

    // Tạo options cho truy vấn
    const queryOptions = {
        where: whereClause,
        order: [[sortBy, sortOrder]],
        limit,
        offset,
        paranoid
    };

    // Thêm transaction nếu có
    if (transaction) {
        queryOptions.transaction = transaction;
    }

    try {
        // Thực hiện truy vấn với phân trang
        const { count, rows } = await db.User.findAndCountAll(queryOptions);

        // Tính toán thông tin phân trang
        const totalPages = Math.ceil(count / limit);
        const hasNextPage = page < totalPages;
        const hasPrevPage = page > 1;

        // Trả về kết quả
        return {
            users: rows,
            pagination: {
                total: count,
                totalPages,
                currentPage: page,
                limit,
                hasNextPage,
                hasPrevPage
            }
        };
    } catch (error) {
        console.error('Lỗi khi tìm kiếm người dùng:', error);
        throw error;
    }
};

/**
 * Tìm người dùng theo ID
 * @param {string} id - ID của người dùng
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Thông tin người dùng
 */
export const findUserById = async (id, transaction = null) => {
    try {
        const options = transaction ? { transaction } : {};
        const user = await db.User.findByPk(id, options);
        return user;
    } catch (error) {
        console.error(`Lỗi khi tìm người dùng với ID ${id}:`, error);
        throw error;
    }
};

/**
 * Tìm người dùng theo username
 * @param {string} username - Tên đăng nhập
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Thông tin người dùng
 */
export const findUserByUsername = async (username, transaction = null) => {
    try {
        const options = {
            where: { username }
        };

        if (transaction) {
            options.transaction = transaction;
        }

        const user = await db.User.findOne(options);
        return user;
    } catch (error) {
        console.error(`Lỗi khi tìm người dùng với username ${username}:`, error);
        throw error;
    }
};

/**
 * Tìm người dùng theo số điện thoại
 * @param {string} phone - Số điện thoại
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Thông tin người dùng
 */
export const findUserByPhone = async (phone, transaction = null) => {
    try {
        const options = {
            where: { phone }
        };

        if (transaction) {
            options.transaction = transaction;
        }

        const user = await db.User.findOne(options);
        return user;
    } catch (error) {
        console.error(`Lỗi khi tìm người dùng với số điện thoại ${phone}:`, error);
        throw error;
    }
};

/**
 * Cập nhật thông tin người dùng
 * @param {number} id - ID của người dùng
 * @param {Object} data - Dữ liệu cần cập nhật
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<boolean>} Kết quả cập nhật
 */
export const updateUser = async (id, data, transaction = null) => {
    try {
        const options = { where: { id } };

        if (transaction) {
            options.transaction = transaction;
        }

        const [updated] = await db.User.update(data, options);
        return updated > 0;
    } catch (error) {
        console.error(`Lỗi khi cập nhật người dùng với ID ${id}:`, error);
        throw error;
    }
};

/**
 * Lấy danh sách người dùng là học sinh với các tùy chọn tìm kiếm và phân trang
 * @param {Object} options - Các tùy chọn
 * @param {string} options.search - Từ khóa tìm kiếm
 * @param {number} options.page - Trang hiện tại (mặc định: 1)
 * @param {number} options.limit - Số lượng kết quả trên mỗi trang (mặc định: 10)
 * @param {string} options.sortOrder - Thứ tự sắp xếp: 'ASC' hoặc 'DESC' (mặc định: 'DESC')
 * @param {number|null} options.graduationYear - Lọc theo năm tốt nghiệp (VD: 2024, 2025)
 * @param {string|null} options.classFilter - Lọc theo khối lớp (VD: '10', '11', '12')
 * @param {Object} transaction - Transaction (nếu có)
 * @returns {Promise<Object>} Kết quả tìm kiếm với phân trang
 */
export const getAllStudents = async (options = {}, transaction = null) => {
    const {
        search = '',
        page = 1,
        limit = 10,
        sortOrder = 'DESC',
        graduationYear = null,
        classFilter = null
    } = options;

    

    const offset = (page - 1) * limit;

    const andConditions = [{ userType: UserType.STUDENT }];
    if (graduationYear !== null && !isNaN(graduationYear)) {
        andConditions.push({ graduationYear });
    }
    if (classFilter && classFilter.trim() !== '') {
        andConditions.push({ class: classFilter.trim() });
    }
    if (search.trim() !== '') {
        andConditions.push({
            [Op.or]: [
                where(fn('CONCAT', col('lastName'), ' ', col('firstName')), {
                    [Op.like]: `%${search}%`
                }),
                { highSchool: { [Op.like]: `%${search}%` } },
                { phone: { [Op.like]: `%${search}%` } },
                { id: { [Op.like]: `%${search}%` } },
                { username: { [Op.like]: `%${search}%` } }
            ]
        });
    }

    const whereClause = { [Op.and]: andConditions };

    const queryOptions = {
        where: whereClause,
        offset,
        limit,
        order: [['createdAt', sortOrder]],
        include: [
            {
                model: db.StudentClassStatus,
                as: 'classStatuses',
                where: {
                    status: 'JS'
                },
                required: false,
                include: [
                    {
                        model: db.Class,
                        as: 'class',
                        attributes: ['id', 'name', 'grade', 'academicYear', 'status']
                    }
                ]
            }
        ]
    };

    if (transaction) {
        queryOptions.transaction = transaction;
    }

    try {
        const [users, total] = await Promise.all([
            db.User.findAll(queryOptions),
            db.User.count({ where: whereClause, transaction })
        ]);

        return new ResponseDataPagination(users, {
            total,
            page,
            pageSize: limit,
            totalPages: Math.ceil(total / limit),
            sortOrder,
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách học sinh:', error);
        throw error;
    }
};

export const getAllStudentsByClass = async (classId, options = {}, transaction = null) => {
    const {
        search = '',
        page = 1,
        limit = 10,
        sortOrder = 'DESC',
    } = options;

    const offset = (page - 1) * limit;

    if (!classId) {
        throw new Error('Thiếu classId!');
    }

    const trimmedSearch = search.trim();

    const whereClause = trimmedSearch
        ? {
            [Op.and]: [
                { userType: 'HS1' },
                {
                    [Op.or]: [
                        !isNaN(trimmedSearch) && { id: parseInt(trimmedSearch, 10) },
                        { username: { [Op.like]: `%${trimmedSearch}%` } },
                        { phone: { [Op.like]: `%${trimmedSearch}%` } },
                        where(
                            fn('CONCAT', col('student.lastName'), ' ', col('student.firstName')),
                            {
                                [Op.like]: `%${trimmedSearch}%`,
                            }
                        ),
                    ].filter(Boolean),
                },
            ],
        }
        : { userType: 'HS1' };

        // console.log('whereClause:', whereClause);
        // console.log('classId:', classId);
    const { rows: users, count: total } = await db.StudentClassStatus.findAndCountAll({
        where: { classId },
        include: [
            {
                model: db.User,
                as: 'student',
                where: whereClause,
            },
        ],
        limit,
        offset,
        order: [['status', sortOrder]],
        transaction,
    });

    const formattedUsers = users.map((userRecord) => {
        const user = userRecord.student;
        const status = userRecord.status;
        return new UserResponse(user, status);
    });

    return new ResponseDataPagination(formattedUsers, {
        total,
        page,
        pageSize: limit,
        totalPages: Math.ceil(total / limit),
        sortOrder,
    });
};


/**
 * Kiểm tra quyền xóa người dùng
 * @param {Object} userToDelete - Người dùng cần xóa
 * @param {Object} currentUser - Người dùng hiện tại
 * @returns {Object} Kết quả kiểm tra { isValid: boolean, message?: string }
 */
export const validateDeletePermission = (userToDelete, currentUser) => {
    // Không cho phép xóa chính mình
    if (userToDelete.id === currentUser.id) {
        return {
            isValid: false,
            message: 'Không thể xóa chính mình'
        };
    }

    // Chỉ admin mới có thể xóa admin hoặc teacher
    if ((userToDelete.userType === UserType.ADMIN || userToDelete.userType === UserType.TEACHER)
        && currentUser.userType !== UserType.ADMIN) {
        return {
            isValid: false,
            message: 'Chỉ admin mới có thể xóa admin hoặc giáo viên'
        };
    }

    return { isValid: true };
};

/**
 * Xóa các bản ghi liên quan đến người dùng
 * @param {number} userId - ID của người dùng
 * @param {Object} transaction - Transaction
 */
export const deleteUserRelatedRecords = async (userId, transaction) => {
    try {
        // 1. Xóa attendance records
        await db.Attendance.destroy({
            where: { userId },
            transaction
        });

        // 2. Xóa student class status records
        await db.StudentClassStatus.destroy({
            where: { studentId: userId },
            transaction
        });

        // 3. Xóa student exam attempts
        await db.StudentExamAttempt.destroy({
            where: { studentId: userId },
            transaction
        });

        // 4. Xóa student exam status
        await db.StudentExamStatus.destroy({
            where: { studentId: userId },
            transaction
        });

        // 5. Xóa student study status
        await db.StudentStudyStatus.destroy({
            where: { studentId: userId },
            transaction
        });

        return true;
    } catch (error) {
        console.error('Lỗi khi xóa các bản ghi liên quan:', error);
        throw error;
    }
};

/**
 * Xóa người dùng và tất cả dữ liệu liên quan
 * @param {number} userId - ID của người dùng cần xóa
 * @param {Object} currentUser - Người dùng hiện tại thực hiện thao tác xóa
 * @param {Object} transaction - Transaction (tùy chọn)
 * @returns {Promise<Object>} Kết quả xóa
 */
export const deleteUserById = async (userId, currentUser, transaction = null) => {
    const isExternalTransaction = !!transaction;
    const t = transaction || await db.sequelize.transaction();

    try {
        // 1. Kiểm tra user có tồn tại không
        const userToDelete = await findUserById(userId, t);
        if (!userToDelete) {
            throw new Error('Người dùng không tồn tại');
        }

        // 2. Kiểm tra quyền xóa
        const permissionCheck = validateDeletePermission(userToDelete, currentUser);
        if (!permissionCheck.isValid) {
            throw new Error(permissionCheck.message);
        }

        // 3. Xóa các bản ghi liên quan
        await deleteUserRelatedRecords(userId, t);

        // 4. Xóa avatar file nếu có
        if (userToDelete.avatarUrl) {
            try {
                await cleanupUploadedFiles([userToDelete.avatarUrl]);
            } catch (error) {
                console.error('Lỗi khi xóa avatar:', error);
                // Không throw lỗi vì đây chỉ là lỗi cleanup file
            }
        }

        // 5. Xóa user khỏi Google Sheets (nếu có)
        try {
            const sheetsResult = await deleteUserFromAllSheets(userId);
            console.log('Kết quả xóa user khỏi Google Sheets:', sheetsResult);
        } catch (error) {
            console.error('Lỗi khi xóa user khỏi Google Sheets:', error);
            // Không throw lỗi vì đây chỉ là lỗi cleanup Excel, không ảnh hưởng đến việc xóa user
        }

        // 6. Cuối cùng xóa user
        await db.User.destroy({
            where: { id: userId },
            transaction: t
        });

        // Commit transaction nếu không phải external transaction
        if (!isExternalTransaction) {
            await t.commit();
        }

        return {
            success: true,
            deletedUserId: userId,
            deletedUserName: `${userToDelete.lastName} ${userToDelete.firstName}`
        };

    } catch (error) {
        // Rollback transaction nếu không phải external transaction
        if (!isExternalTransaction) {
            await t.rollback();
        }
        throw error;
    }
};

