{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\tuition\\\\TuitionPaymentList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport html2canvas from \"html2canvas\";\nimport { find } from \"lodash\";\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\nimport UserSearchInput from \"../../../components/UserSearchInput\";\nimport { fetchTuitionPayments, deleteTuitionPayment, createTuitionPayment, createBatchTuitionPayments, fetchTuitionStatistics, updateTuitionPayment, fetchTuitionPaymentByIdAdmin } from \"src/features/tuition/tuitionSlice\";\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\nimport { findUsers } from \"src/features/user/userSlice\";\nimport { findClasses } from \"src/features/class/classSlice\";\nimport { setCurrentPage, setSearch, resetFilters } from \"src/features/tuition/tuitionSlice\";\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\nimport Pagination from \"src/components/Pagination\";\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\nimport AdminLayout from \"src/layouts/AdminLayout\";\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\nimport Chart from 'chart.js/auto';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TuitionPaymentList = () => {\n  _s();\n  var _tuitionPayment$user, _tuitionPayment$user2, _tuitionPayment$user3, _tuitionPayment$user4, _tuitionPayment$user5;\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    tuitionPayments,\n    tuitionStatistics,\n    loading,\n    tuitionPayment,\n    studentClassTuitionsAdmin\n  } = useSelector(state => state.tuition);\n  const {\n    page,\n    totalPages,\n    total,\n    pageSize\n  } = useSelector(state => state.tuition.pagination);\n  const [inputValue, setInputValue] = useState(\"\");\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\n  const [showRightPanel, setShowRightPanel] = useState(false);\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\n\n  // State cho bộ lọc\n  const [filterMonth, setFilterMonth] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"\");\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\n  const [filterClass, setFilterClass] = useState(\"\");\n  const [filterClassId, setFilterClassId] = useState(\"\");\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\n\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\n\n  // State cho chế độ xem\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\n  const [startMonth, setStartMonth] = useState(\"\");\n  const [endMonth, setEndMonth] = useState(\"\");\n\n  // Refs cho biểu đồ\n  const monthlyChartRef = useRef(null);\n  const classChartRef = useRef(null);\n  const monthlyChartInstance = useRef(null);\n  const classChartInstance = useRef(null);\n\n  // State cho form tạo học phí hàng loạt\n  const [batchMonth, setBatchMonth] = useState(\"\");\n  const [batchAmount, setBatchAmount] = useState(\"\");\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\n  const [batchClass, setBatchClass] = useState(\"\");\n  const [batchNote, setBatchNote] = useState(\"\");\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // State cho form thêm học phí\n  const [addUserId, setAddUserId] = useState(\"\");\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\n  const [addMonth, setAddMonth] = useState(\"\");\n  const [addExpectedAmount, setAddExpectedAmount] = useState(\"\");\n  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState(\"\");\n  const [addPaidAmount, setAddPaidAmount] = useState(\"\");\n  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState(\"\");\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\n  const [addDueDate, setAddDueDate] = useState(\"\");\n  const [addStatus, setAddStatus] = useState(\"\");\n  const [addNote, setAddNote] = useState(\"\");\n  const [addCalculateExpected, setAddCalculateExpected] = useState(false);\n\n  // State cho form chỉnh sửa học phí\n  const [editId, setEditId] = useState(null);\n  const [editExpectedAmount, setEditExpectedAmount] = useState(\"\");\n  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState(\"\");\n  const [editPaidAmount, setEditPaidAmount] = useState(\"\");\n  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState(\"\");\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\n  const [editStatus, setEditStatus] = useState(\"\");\n  const [editNote, setEditNote] = useState(\"\");\n  const [calculateExpected, setCalculateExpected] = useState(false);\n\n  // State cho xem chi tiết học phí\n  const [viewPayment, setViewPayment] = useState(null);\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\n  const [viewLoading, setViewLoading] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const detailsContainerRef = useRef(null);\n\n  // useEffect(() => {\n  //   if (!didInit) {\n  //     dispatch(resetFilters());\n  //     setDidInit(true);\n  //   }\n  // }, [dispatch, didInit]);\n\n  // Hiệu ứng để vẽ biểu đồ khi có dữ liệu thống kê\n  useEffect(() => {\n    if (viewMode === 'statistics' && tuitionStatistics && monthlyChartRef.current && classChartRef.current) {\n      var _tuitionStatistics$mo, _tuitionStatistics$mo2, _tuitionStatistics$mo3, _tuitionStatistics$cl, _tuitionStatistics$cl2, _tuitionStatistics$cl3;\n      // Hủy biểu đồ cũ nếu có\n      if (monthlyChartInstance.current) {\n        monthlyChartInstance.current.destroy();\n      }\n      if (classChartInstance.current) {\n        classChartInstance.current.destroy();\n      }\n\n      // Chuẩn bị dữ liệu cho biểu đồ theo tháng\n      const monthlyData = {\n        labels: ((_tuitionStatistics$mo = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo === void 0 ? void 0 : _tuitionStatistics$mo.map(stat => stat.monthFormatted)) || [],\n        datasets: [{\n          label: 'Số tiền cần thu',\n          data: ((_tuitionStatistics$mo2 = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo2 === void 0 ? void 0 : _tuitionStatistics$mo2.map(stat => stat.totalExpectedAmount)) || [],\n          backgroundColor: 'rgba(54, 162, 235, 0.5)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }, {\n          label: 'Số tiền đã thu',\n          data: ((_tuitionStatistics$mo3 = tuitionStatistics.monthlyStatistics) === null || _tuitionStatistics$mo3 === void 0 ? void 0 : _tuitionStatistics$mo3.map(stat => stat.totalPaidAmount)) || [],\n          backgroundColor: 'rgba(75, 192, 192, 0.5)',\n          borderColor: 'rgba(75, 192, 192, 1)',\n          borderWidth: 1\n        }]\n      };\n\n      // Chuẩn bị dữ liệu cho biểu đồ theo lớp\n      const classData = {\n        labels: ((_tuitionStatistics$cl = tuitionStatistics.classStatistics) === null || _tuitionStatistics$cl === void 0 ? void 0 : _tuitionStatistics$cl.map(stat => \"L\\u1EDBp \".concat(stat.userClass))) || [],\n        datasets: [{\n          label: 'Số tiền cần thu',\n          data: ((_tuitionStatistics$cl2 = tuitionStatistics.classStatistics) === null || _tuitionStatistics$cl2 === void 0 ? void 0 : _tuitionStatistics$cl2.map(stat => stat.totalExpectedAmount)) || [],\n          backgroundColor: 'rgba(153, 102, 255, 0.5)',\n          borderColor: 'rgba(153, 102, 255, 1)',\n          borderWidth: 1\n        }, {\n          label: 'Số tiền đã thu',\n          data: ((_tuitionStatistics$cl3 = tuitionStatistics.classStatistics) === null || _tuitionStatistics$cl3 === void 0 ? void 0 : _tuitionStatistics$cl3.map(stat => stat.totalPaidAmount)) || [],\n          backgroundColor: 'rgba(255, 159, 64, 0.5)',\n          borderColor: 'rgba(255, 159, 64, 1)',\n          borderWidth: 1\n        }]\n      };\n\n      // Vẽ biểu đồ theo tháng\n      const monthlyCtx = monthlyChartRef.current.getContext('2d');\n      monthlyChartInstance.current = new Chart(monthlyCtx, {\n        type: 'bar',\n        data: monthlyData,\n        options: {\n          responsive: true,\n          scales: {\n            y: {\n              beginAtZero: true,\n              ticks: {\n                callback: function (value) {\n                  return formatCurrency(value);\n                }\n              }\n            }\n          },\n          plugins: {\n            tooltip: {\n              callbacks: {\n                label: function (context) {\n                  return \"\".concat(context.dataset.label, \": \").concat(formatCurrency(context.raw));\n                }\n              }\n            }\n          }\n        }\n      });\n\n      // Vẽ biểu đồ theo lớp\n      const classCtx = classChartRef.current.getContext('2d');\n      classChartInstance.current = new Chart(classCtx, {\n        type: 'bar',\n        data: classData,\n        options: {\n          responsive: true,\n          scales: {\n            y: {\n              beginAtZero: true,\n              ticks: {\n                callback: function (value) {\n                  return formatCurrency(value);\n                }\n              }\n            }\n          },\n          plugins: {\n            tooltip: {\n              callbacks: {\n                label: function (context) {\n                  return \"\".concat(context.dataset.label, \": \").concat(formatCurrency(context.raw));\n                }\n              }\n            }\n          }\n        }\n      });\n    }\n  }, [viewMode, tuitionStatistics]);\n  useEffect(() => {\n    dispatch(findClasses(\"\"));\n    dispatch(findUsers(\"\"));\n  }, [dispatch]);\n\n  // Xử lý khi người dùng chọn một lớp\n  const handleSelectClass = classItem => {\n    setFilterClassId(classItem.id);\n    setClassSearchTerm(classItem.name);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn lớp\n  const handleClearClassSelection = () => {\n    setFilterClassId(\"\");\n    setClassSearchTerm(\"\");\n  };\n\n  // Xử lý khi người dùng chọn một người dùng\n  const handleSelectUser = userItem => {\n    setSelectedUserId(userItem.id);\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\n  };\n\n  // Xử lý khi người dùng xóa lựa chọn người dùng\n  const handleClearUserSelection = () => {\n    setSelectedUserId(\"\");\n    setUserSearchTerm(\"\");\n  };\n\n  // Hàm xử lý tìm kiếm\n  const handleSearch = () => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: 1,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass,\n      classId: filterClassId\n    }));\n  };\n\n  // Hàm xử lý thêm học phí mới\n  const handleAddTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const errors = {};\n\n    // Validate userId (required)\n    if (!addUserId) {\n      errors.userId = \"Vui lòng chọn học sinh\";\n    }\n\n    // Validate month (required and format YYYY-MM)\n    if (!addMonth) {\n      errors.month = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\n      errors.month = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate expected amount (required if not calculating automatically)\n    if (!addCalculateExpected && !addExpectedAmount) {\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\n      errors.expectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate paid amount (must be a positive number if provided)\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\n      errors.paidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate due date (required)\n    if (!addDueDate) {\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\n    }\n\n    // Validate status (required)\n    if (!addStatus) {\n      errors.status = \"Vui lòng chọn trạng thái\";\n    }\n\n    // If there are errors, display them and stop submission\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        userId: addUserId,\n        month: addMonth,\n        status: addStatus,\n        note: addNote,\n        dueDate: addDueDate\n      };\n\n      // Only include expectedAmount if not calculating automatically\n      if (!addCalculateExpected && addExpectedAmount) {\n        paymentData.expectedAmount = Number(addExpectedAmount);\n      }\n\n      // Include paidAmount if provided\n      if (addPaidAmount) {\n        paymentData.paidAmount = Number(addPaidAmount);\n      }\n\n      // Include paymentDate if provided\n      if (addPaymentDate) {\n        paymentData.paymentDate = addPaymentDate;\n      }\n\n      // Call API to create tuition payment\n      await dispatch(createTuitionPayment(paymentData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n\n      // Reset form\n      setAddUserId(\"\");\n      setAddUserSearchTerm(\"\");\n      setAddMonth(\"\");\n      setAddExpectedAmount(\"\");\n      setAddExpectedAmountFormatted(\"\");\n      setAddPaidAmount(\"\");\n      setAddPaidAmountFormatted(\"\");\n      setAddPaymentDate(\"\");\n      setAddDueDate(\"\");\n      setAddStatus(\"\");\n      setAddNote(\"\");\n      setAddCalculateExpected(false);\n    } catch (error) {\n      console.error(\"Error creating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Hàm xử lý khi thay đổi chế độ xem\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n\n    // Nếu chuyển sang chế độ thống kê, tải dữ liệu thống kê\n    if (mode === 'statistics') {\n      // Khởi tạo giá trị mặc định cho startMonth và endMonth nếu chưa có\n      const currentDate = new Date();\n      const currentYear = currentDate.getFullYear();\n      const currentMonth = currentDate.getMonth() + 1;\n\n      // Nếu chưa có startMonth, đặt là tháng hiện tại của năm trước\n      if (!startMonth) {\n        const formattedMonth = currentMonth < 10 ? \"0\".concat(currentMonth) : \"\".concat(currentMonth);\n        setStartMonth(\"\".concat(currentYear - 1, \"-\").concat(formattedMonth));\n      }\n\n      // Nếu chưa có endMonth, đặt là tháng hiện tại\n      if (!endMonth) {\n        const formattedMonth = currentMonth < 10 ? \"0\".concat(currentMonth) : \"\".concat(currentMonth);\n        setEndMonth(\"\".concat(currentYear, \"-\").concat(formattedMonth));\n      }\n\n      // Tải dữ liệu thống kê\n      dispatch(fetchTuitionStatistics({\n        startMonth: startMonth || \"\".concat(currentYear - 1, \"-\").concat(currentMonth < 10 ? \"0\".concat(currentMonth) : currentMonth),\n        endMonth: endMonth || \"\".concat(currentYear, \"-\").concat(currentMonth < 10 ? \"0\".concat(currentMonth) : currentMonth),\n        userClass: filterClass\n      }));\n    }\n  };\n  useEffect(() => {\n    dispatch(fetchTuitionPayments({\n      search: inputValue,\n      page: page,\n      // Reset về trang 1 khi tìm kiếm\n      pageSize,\n      sortOrder: \"DESC\",\n      status: filterStatus,\n      month: filterMonth,\n      overdue: filterOverdue,\n      userClass: filterClass\n    }));\n  }, [dispatch, page, pageSize]);\n  const handleEdit = async id => {\n    setIsSubmitting(true);\n    try {\n      // Fetch the payment details\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\n      const payment = response.data;\n\n      // Set the edit form state\n      setEditId(id);\n      setEditExpectedAmount(payment.expectedAmount || \"\");\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\n      setEditPaidAmount(payment.paidAmount || \"\");\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\n      setEditStatus(payment.status || \"\");\n      setEditNote(payment.note || \"\");\n      setCalculateExpected(false);\n\n      // Show the right panel\n      setRightPanelType(\"edit\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleView = async (id, userId, month) => {\n    setViewLoading(true);\n    try {\n      // Fetch the payment details\n      await dispatch(fetchTuitionPaymentByIdAdmin(id));\n\n      // Fetch the class tuitions for the student in the payment month\n\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\n      //   userId,\n      //   month\n      // }))\n\n      // Show the right panel\n      setRightPanelType(\"view\");\n      setShowRightPanel(true);\n    } catch (error) {\n      console.error(\"Error fetching payment details:\", error);\n    } finally {\n      setViewLoading(false);\n    }\n  };\n\n  // Hàm xuất chi tiết thanh toán thành ảnh\n  const handleExportImage = async () => {\n    if (!detailsContainerRef.current) return;\n    setExportLoading(true);\n    try {\n      // Lưu trữ style hiện tại\n      const originalStyle = detailsContainerRef.current.getAttribute('style') || '';\n\n      // Thêm style tạm thời cho việc xuất ảnh\n      detailsContainerRef.current.setAttribute('style', \"\".concat(originalStyle, \";\\n         background-color: white;\\n         border-radius: 8px;\\n         box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\"));\n      const element = detailsContainerRef.current;\n      const canvas = await html2canvas(element, {\n        scale: 2,\n        // Tăng độ phân giải\n        useCORS: true,\n        // Cho phép tải hình ảnh từ các domain khác\n        logging: false,\n        backgroundColor: '#ffffff',\n        margin: {\n          top: 20,\n          right: 20,\n          bottom: 20,\n          left: 20\n        }\n      });\n\n      // Khôi phục style ban đầu\n      detailsContainerRef.current.setAttribute('style', originalStyle);\n\n      // Chuyển canvas thành URL\n      const imageUrl = canvas.toDataURL('image/png');\n\n      // Tạo link tải xuống\n      const link = document.createElement('a');\n      const fileName = tuitionPayment !== null && tuitionPayment !== void 0 && tuitionPayment.user ? \"hoc-phi-\".concat(tuitionPayment.user.lastName, \"-\").concat(tuitionPayment.user.firstName, \"-\").concat(tuitionPayment.monthFormatted, \".png\") : \"hoc-phi-\".concat(new Date().toISOString(), \".png\");\n      link.download = fileName;\n      link.href = imageUrl;\n      link.click();\n    } catch (error) {\n      console.error(\"Error exporting image:\", error);\n    } finally {\n      setExportLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    // Reset form state\n    setAddUserId(\"\");\n    setAddUserSearchTerm(\"\");\n    setAddMonth(\"\");\n    setAddExpectedAmount(\"\");\n    setAddExpectedAmountFormatted(\"\");\n    setAddPaidAmount(\"\");\n    setAddPaidAmountFormatted(\"\");\n    setAddPaymentDate(\"\");\n    setAddDueDate(\"\");\n    setAddStatus(\"\");\n    setAddNote(\"\");\n    setAddCalculateExpected(false);\n    setFormErrors({});\n\n    // Set current date as default for dueDate\n    const today = new Date();\n    const formattedDate = today.toISOString().split('T')[0];\n    setAddDueDate(formattedDate);\n\n    // Set current month as default for month\n    const year = today.getFullYear();\n    const month = today.getMonth() + 1;\n    const formattedMonth = \"\".concat(year, \"-\").concat(month < 10 ? \"0\".concat(month) : month);\n    setAddMonth(formattedMonth);\n\n    // Open panel\n    setRightPanelType(\"add\");\n    setShowRightPanel(true);\n  };\n  const handleBatchAdd = () => {\n    setRightPanelType(\"batch\");\n    setShowRightPanel(true);\n  };\n  const handleCreateBatchTuition = () => {\n    setRightPanelType(\"batchByMonth\");\n    setShowRightPanel(true);\n  };\n  const handleCreateByClass = () => {\n    setRightPanelType(\"batchByClass\");\n    setShowRightPanel(true);\n  };\n  const closeRightPanel = () => {\n    setShowRightPanel(false);\n    setRightPanelType(\"\");\n    // Reset form state\n    setBatchMonth(\"\");\n    setBatchAmount(\"\");\n    setBatchAmountFormatted(\"\");\n    setBatchDueDate(\"\");\n    setBatchClass(\"\");\n    setBatchNote(\"\");\n    setEditId(null);\n    setEditExpectedAmount(\"\");\n    setEditExpectedAmountFormatted(\"\");\n    setEditPaidAmount(\"\");\n    setEditPaidAmountFormatted(\"\");\n    setEditPaymentDate(\"\");\n    setEditStatus(\"\");\n    setEditNote(\"\");\n    setCalculateExpected(false);\n    // Reset view state\n    setViewPayment(null);\n    setViewClassTuitions(null);\n    setFormErrors({});\n    setIsSubmitting(false);\n    setViewLoading(false);\n  };\n\n  // Validate edit form data\n  const validateEditForm = () => {\n    const errors = {};\n\n    // Validate paid amount (must be a positive number)\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate expected amount (must be a positive number if provided)\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate status (required)\n    if (!editStatus) {\n      errors.editStatus = \"Trạng thái không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle edit form submission\n  const handleUpdateTuitionPayment = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\n\n    // Update the actual values with parsed values\n    if (editPaidAmountFormatted) {\n      setEditPaidAmount(parsedPaidAmount);\n    }\n    if (editExpectedAmountFormatted && !calculateExpected) {\n      setEditExpectedAmount(parsedExpectedAmount);\n    }\n\n    // Validate form\n    const errors = validateEditForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const paymentData = {\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\n        status: editStatus,\n        note: editNote,\n        calculateExpected: calculateExpected\n      };\n\n      // Only include expectedAmount if it's provided and not calculating automatically\n      if (parsedExpectedAmount && !calculateExpected) {\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\n      }\n\n      // Only include paymentDate if it's provided\n      if (editPaymentDate) {\n        paymentData.paymentDate = editPaymentDate;\n      }\n\n      // Call API to update tuition payment\n      await dispatch(updateTuitionPayment({\n        id: editId,\n        paymentData\n      }));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\",\n        status: filterStatus,\n        month: filterMonth,\n        overdue: filterOverdue,\n        userClass: filterClass\n      }));\n    } catch (error) {\n      console.error(\"Error updating tuition payment:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi cập nhật học phí\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Validate form data\n  const validateBatchTuitionForm = () => {\n    const errors = {};\n\n    // Validate month (required and format YYYY-MM)\n    if (!batchMonth) {\n      errors.batchMonth = \"Tháng không được để trống\";\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\n    }\n\n    // Validate amount (optional but must be positive if provided)\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\n      errors.batchAmount = \"Số tiền phải là số dương\";\n    }\n\n    // Validate class selection (required)\n    if (!batchClass) {\n      errors.batchClass = \"Vui lòng chọn lớp\";\n    }\n\n    // Validate due date (required)\n    if (!batchDueDate) {\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\n    }\n    return errors;\n  };\n\n  // Handle batch tuition form submission\n  const handleBatchTuitionSubmit = async e => {\n    e.preventDefault();\n\n    // Parse formatted values to numbers\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\n\n    // Update the actual value with parsed value\n    if (batchAmountFormatted) {\n      setBatchAmount(parsedBatchAmount);\n    }\n\n    // Validate form\n    const errors = validateBatchTuitionForm();\n    if (Object.keys(errors).length > 0) {\n      setFormErrors(errors);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      // Prepare data for API call\n      const batchData = {\n        month: batchMonth,\n        dueDate: batchDueDate,\n        batchClass,\n        note: batchNote\n      };\n\n      // Only include expectedAmount if it's provided\n      if (parsedBatchAmount) {\n        batchData.expectedAmount = Number(parsedBatchAmount);\n      }\n\n      // Call API to create batch tuition payments\n      await dispatch(createBatchTuitionPayments(batchData));\n\n      // Close panel and refresh data\n      closeRightPanel();\n      dispatch(fetchTuitionPayments({\n        page: page,\n        pageSize,\n        search: inputValue,\n        sortOrder: \"DESC\"\n      }));\n    } catch (error) {\n      console.error(\"Error creating batch tuition payments:\", error);\n      setFormErrors({\n        submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\"\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = id => {\n    setPaymentToDelete(id);\n    setShowConfirmModal(true);\n  };\n  const confirmDelete = async () => {\n    dispatch(deleteTuitionPayment(paymentToDelete));\n    setShowConfirmModal(false);\n  };\n  const cancelDelete = () => {\n    setShowConfirmModal(false);\n    setPaymentToDelete(null);\n  };\n  const getStatusBadge = status => {\n    switch (status) {\n      case \"PAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n          children: \"\\u0110\\xE3 thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 11\n        }, this);\n      case \"UNPAID\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\",\n          children: \"Ch\\u01B0a thanh to\\xE1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this);\n      case \"OVERDUE\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\",\n          children: \"Qu\\xE1 h\\u1EA1n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this);\n      case \"PARTIAL\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\",\n          children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\",\n          children: status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 842,\n      columnNumber: 12\n    }, this);\n  }\n  const ButtonFunctionBarAdmin = _ref => {\n    let {\n      icon,\n      text,\n      onClick\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClick,\n      className: \"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\",\n      children: [icon, /*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 852,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 7\n    }, this);\n  };\n  const iconAdd = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Plus, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 859,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 858,\n    columnNumber: 5\n  }, this);\n  const iconBatch = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 864,\n    columnNumber: 5\n  }, this);\n  const iconCalendar = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 871,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 870,\n    columnNumber: 5\n  }, this);\n  const iconUsers = /*#__PURE__*/_jsxDEV(\"div\", {\n    \"data-svg-wrapper\": true,\n    className: \"relative\",\n    children: /*#__PURE__*/_jsxDEV(Users, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 877,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 876,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\",\n      children: \"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1ECDc ph\\xED\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 883,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[300px] relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 16 16\",\n              fill: \"none\",\n              className: \"absolute left-[1rem] top-1/2 transform -translate-y-1/2\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\",\n                stroke: \"#131214\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"T\\xECm ki\\u1EBFm theo t\\xEAn h\\u1ECDc sinh, l\\u1EDBp h\\u1ECDc...\",\n              value: inputValue,\n              onChange: e => setInputValue(e.target.value),\n              className: \"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterMonth,\n              onChange: e => setFilterMonth(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this), Array.from({\n                length: 12\n              }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                const monthStr = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n                return /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\".concat(year, \"-\").concat(monthStr),\n                  children: \"Th\\xE1ng \".concat(month, \"/\").concat(year)\n                }, month, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterStatus,\n              onChange: e => setFilterStatus(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tr\\u1EA1ng th\\xE1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PAID\",\n                children: \"\\u0110\\xE3 thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"UNPAID\",\n                children: \"Ch\\u01B0a thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PARTIAL\",\n                children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterOverdue,\n              onChange: e => setFilterOverdue(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"T\\xECnh tr\\u1EA1ng h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"\\u0110\\xE3 qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Ch\\u01B0a qu\\xE1 h\\u1EA1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[150px]\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n              value: filterClass,\n              onChange: e => setFilterClass(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Kh\\u1ED1i\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 967,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"10\",\n                children: \"Kh\\u1ED1i 10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"11\",\n                children: \"Kh\\u1ED1i 11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"Kh\\u1ED1i 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-[250px]\",\n            children: /*#__PURE__*/_jsxDEV(ClassSearchInput, {\n              value: classSearchTerm,\n              selectedClassId: filterClassId,\n              onChange: setClassSearchTerm,\n              onSelect: handleSelectClass,\n              onClear: handleClearClassSelection,\n              placeholder: \"T\\xECm ki\\u1EBFm l\\u1EDBp h\\u1ECDc...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: \"2\",\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 991,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 15\n            }, this), \"T\\xECm ki\\u1EBFm\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex border border-gray-300 rounded-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 flex items-center \".concat(viewMode === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-100'),\n              onClick: () => handleViewModeChange('table'),\n              children: [/*#__PURE__*/_jsxDEV(List, {\n                size: 16,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 17\n              }, this), \"Danh s\\xE1ch\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-4 py-2 flex items-center \".concat(viewMode === 'statistics' ? 'bg-blue-500 text-white' : 'bg-gray-100'),\n              onClick: () => handleViewModeChange('statistics'),\n              children: [/*#__PURE__*/_jsxDEV(BarChart2, {\n                size: 16,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 17\n              }, this), \"Th\\u1ED1ng k\\xEA\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconAdd,\n            text: 'Thêm thanh toán',\n            onClick: handleAdd\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconCalendar,\n            text: 'Tạo học phí hàng loạt',\n            onClick: handleCreateBatchTuition\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1017,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ButtonFunctionBarAdmin, {\n            icon: iconUsers,\n            text: 'Tạo học phí theo lớp',\n            onClick: handleCreateByClass\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 887,\n      columnNumber: 7\n    }, this), \"tuitionPayments.length === 0 ? (\", /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Kh\\xF4ng c\\xF3 d\\u1EEF li\\u1EC7u thanh to\\xE1n h\\u1ECDc ph\\xED n\\xE0o.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1024,\n      columnNumber: 11\n    }, this), \") : (\", /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"min-w-full bg-white shadow-md rounded-lg overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"STT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1ECDc sinh\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"L\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u01B0\\u1EDDng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xF3ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Th\\xE1ng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Ng\\xE0y thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"H\\u1EA1n thanh to\\xE1n\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Tr\\u1EA1ng th\\xE1i\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"py-3 px-4 text-left\",\n              children: \"Thao t\\xE1c\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1031,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1030,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: tuitionPayments.map((payment, index) => {\n            var _payment$user, _payment$user2, _payment$user3, _payment$user4;\n            return /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: (page - 1) * pageSize + index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user = payment.user) === null || _payment$user === void 0 ? void 0 : _payment$user.lastName) + \" \" + ((_payment$user2 = payment.user) === null || _payment$user2 === void 0 ? void 0 : _payment$user2.firstName) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user3 = payment.user) === null || _payment$user3 === void 0 ? void 0 : _payment$user3.class) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: ((_payment$user4 = payment.user) === null || _payment$user4 === void 0 ? void 0 : _payment$user4.highSchool) || \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1058,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.expectedAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: formatCurrency(payment.paidAmount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.monthFormatted\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\") : \"Chưa thanh toán\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1074,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"py-3 px-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleView(payment.id, payment.userId, payment.month),\n                    className: \"text-blue-500 hover:text-blue-700\",\n                    title: \"Xem chi ti\\u1EBFt\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1084,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(payment.id),\n                    className: \"text-yellow-500 hover:text-yellow-700\",\n                    title: \"Ch\\u1EC9nh s\\u1EEDa\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1091,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1086,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(payment.id),\n                    className: \"text-red-500 hover:text-red-700\",\n                    title: \"X\\xF3a\",\n                    children: /*#__PURE__*/_jsxDEV(Trash, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 21\n              }, this)]\n            }, payment.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1029,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1028,\n      columnNumber: 11\n    }, this), \")\", viewMode === \"table\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        currentPage: page,\n        onPageChange: page => dispatch(setCurrentPage(page)),\n        totalItems: total,\n        limit: pageSize\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1110,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1109,\n      columnNumber: 9\n    }, this), showRightPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-gray-200 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          children: [rightPanelType === \"add\" && \"Thêm thanh toán học phí\", rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\", rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\", rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\", rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: closeRightPanel,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [rightPanelType === \"add\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Th\\xEAm kho\\u1EA3n thanh to\\xE1n h\\u1ECDc ph\\xED m\\u1EDBi cho h\\u1ECDc sinh.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: handleAddTuitionSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1ECDc sinh \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 94\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(UserSearchInput, {\n                value: addUserSearchTerm,\n                selectedUserId: addUserId,\n                onChange: setAddUserSearchTerm,\n                onSelect: user => {\n                  setAddUserId(user.id);\n                  setAddUserSearchTerm(\"\".concat(user.lastName, \" \").concat(user.firstName));\n                },\n                onClear: () => {\n                  setAddUserId(\"\");\n                  setAddUserSearchTerm(\"\");\n                },\n                placeholder: \"T\\xECm ki\\u1EBFm h\\u1ECDc sinh...\",\n                role: \"STUDENT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 21\n              }, this), formErrors.userId && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 25\n                }, this), \" \", formErrors.userId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1146,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1170,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.month ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addMonth,\n                onChange: e => setAddMonth(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 21\n              }, this), formErrors.month && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1179,\n                  columnNumber: 25\n                }, this), \" \", formErrors.month]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"addCalculateExpected\",\n                    checked: addCalculateExpected,\n                    onChange: e => setAddCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1187,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"addCalculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1184,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(addCalculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: addExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddExpectedAmount(value);\n                    setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: addCalculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 21\n              }, this), formErrors.expectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1215,\n                  columnNumber: 25\n                }, this), \" \", formErrors.expectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.paidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: addPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setAddPaidAmount(value);\n                    setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 21\n              }, this), formErrors.paidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1236,\n                  columnNumber: 25\n                }, this), \" \", formErrors.paidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1235,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1219,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1241,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: addPaymentDate,\n                onChange: e => setAddPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1242,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1240,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1250,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1250,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.dueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addDueDate,\n                onChange: e => setAddDueDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1251,\n                columnNumber: 21\n              }, this), formErrors.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1259,\n                  columnNumber: 25\n                }, this), \" \", formErrors.dueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1258,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1249,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1264,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.status ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: addStatus,\n                onChange: e => setAddStatus(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1270,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1271,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 21\n              }, this), formErrors.status && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1277,\n                  columnNumber: 25\n                }, this), \" \", formErrors.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1263,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: addNote,\n                onChange: e => setAddNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1283,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1281,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 20,\n                className: \"mr-2 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: formErrors.submit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1294,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1145,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByMonth\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED h\\xE0ng lo\\u1EA1t cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t th\\xE1ng.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleBatchTuitionSubmit(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Th\\xE1ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1313,\n                  columnNumber: 91\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchMonth ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchMonth,\n                onChange: e => setBatchMonth(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1314,\n                columnNumber: 21\n              }, this), formErrors.batchMonth && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1323,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchMonth]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500 text-xs font-normal\",\n                  children: \"(kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1329,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n ho\\u1EB7c \\u0111\\u1EC3 tr\\u1ED1ng \\u0111\\u1EC3 t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh\",\n                value: batchAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                    setBatchAmount(value ? parseInt(value, 10) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1331,\n                columnNumber: 21\n              }, this), formErrors.batchAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-xs text-gray-500\",\n                children: \"N\\u1EBFu \\u0111\\u1EC3 tr\\u1ED1ng, h\\u1EC7 th\\u1ED1ng s\\u1EBD t\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1327,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"L\\u1EDBp \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1354,\n                  columnNumber: 89\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchClass ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchClass,\n                onChange: e => setBatchClass(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"10\",\n                  children: \"L\\u1EDBp 10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1362,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"L\\u1EDBp 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1363,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"L\\u1EDBp 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1364,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 21\n              }, this), formErrors.batchClass && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchClass]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"H\\u1EA1n thanh to\\xE1n \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1373,\n                  columnNumber: 100\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: batchDueDate,\n                onChange: e => setBatchDueDate(e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1374,\n                columnNumber: 21\n              }, this), formErrors.batchDueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 25\n                }, this), \" \", formErrors.batchDueDate]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: batchNote,\n                onChange: e => setBatchNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1389,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1387,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1397,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1311,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1308,\n          columnNumber: 15\n        }, this), rightPanelType === \"batchByClass\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"T\\u1EA1o h\\u1ECDc ph\\xED cho t\\u1EA5t c\\u1EA3 h\\u1ECDc sinh trong m\\u1ED9t l\\u1EDBp h\\u1ECDc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1410,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"L\\u1EDBp h\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1414,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn l\\u1EDBp h\\u1ECDc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1416,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1\",\n                  children: \"L\\u1EDBp 10A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1417,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2\",\n                  children: \"L\\u1EDBp 11A2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1418,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3\",\n                  children: \"L\\u1EDBp 12A3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1419,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1415,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1413,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Th\\xE1ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1423,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"month\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1424,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1422,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1431,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1429,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"H\\u1EA1n thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1438,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1439,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1437,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1445,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1446,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1444,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n              children: \"T\\u1EA1o h\\u1ECDc ph\\xED theo l\\u1EDBp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1452,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1412,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1409,\n          columnNumber: 15\n        }, this), rightPanelType === \"edit\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Ch\\u1EC9nh s\\u1EEDa th\\xF4ng tin thanh to\\xE1n h\\u1ECDc ph\\xED.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1464,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-4\",\n            onSubmit: e => handleUpdateTuitionPayment(e),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\", /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: \"calculateExpected\",\n                    checked: calculateExpected,\n                    onChange: e => setCalculateExpected(e.target.checked),\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1471,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"calculateExpected\",\n                    className: \"text-xs text-gray-500\",\n                    children: \"T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh l\\u1EA1i d\\u1EF1a tr\\xEAn c\\xE1c l\\u1EDBp h\\u1ECDc sinh \\u0111\\xE3 tham gia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1478,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1468,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md \").concat(calculateExpected ? 'bg-gray-100' : ''),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng\",\n                value: editExpectedAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                },\n                disabled: calculateExpected\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1483,\n                columnNumber: 21\n              }, this), formErrors.editExpectedAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1498,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editExpectedAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1497,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1467,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1503,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                placeholder: \"Nh\\u1EADp s\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng\",\n                value: editPaidAmountFormatted,\n                onChange: e => {\n                  const value = e.target.value.replace(/\\./g, \"\");\n                  if (!isNaN(value) || value === \"\") {\n                    setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1504,\n                columnNumber: 21\n              }, this), formErrors.editPaidAmount && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1518,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editPaidAmount]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1517,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1502,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ng\\xE0y thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                value: editPaymentDate,\n                onChange: e => setEditPaymentDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1524,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1522,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: [\"Tr\\u1EA1ng th\\xE1i \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1532,\n                  columnNumber: 96\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1532,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"w-full px-3 py-2 border \".concat(formErrors.editStatus ? 'border-red-500' : 'border-gray-300', \" rounded-md\"),\n                value: editStatus,\n                onChange: e => setEditStatus(e.target.value),\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Ch\\u1ECDn tr\\u1EA1ng th\\xE1i\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PAID\",\n                  children: \"\\u0110\\xE3 thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1540,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"UNPAID\",\n                  children: \"Ch\\u01B0a thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"PARTIAL\",\n                  children: \"Thanh to\\xE1n m\\u1ED9t ph\\u1EA7n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1542,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1533,\n                columnNumber: 21\n              }, this), formErrors.editStatus && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-500 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  size: 14,\n                  className: \"mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 25\n                }, this), \" \", formErrors.editStatus]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1545,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1531,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Ghi ch\\xFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1551,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                rows: \"3\",\n                placeholder: \"Nh\\u1EADp ghi ch\\xFA (n\\u1EBFu c\\xF3)\",\n                value: editNote,\n                onChange: e => setEditNote(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1552,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1550,\n              columnNumber: 19\n            }, this), formErrors.submit && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-500 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                size: 14,\n                className: \"mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1562,\n                columnNumber: 23\n              }, this), \" \", formErrors.submit]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1561,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\",\n              disabled: isSubmitting,\n              children: isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1565,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1466,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1463,\n          columnNumber: 15\n        }, this), rightPanelType === \"view\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: viewLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1580,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1579,\n            columnNumber: 19\n          }, this) : tuitionPayment ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              ref: detailsContainerRef,\n              className: \"space-y-6 p-6 bg-white rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border-b pb-4 mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\",\n                    children: \"TTB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1588,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl font-bold text-gray-800\",\n                      children: \"To\\xE1n Th\\u1EA7y Bee\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1592,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Chi ti\\u1EBFt thanh to\\xE1n h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1593,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Ng\\xE0y xu\\u1EA5t: \", new Date().toLocaleDateString('vi-VN')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1594,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1591,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1587,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1586,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin h\\u1ECDc sinh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1602,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1ECD t\\xEAn:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1604,\n                      columnNumber: 30\n                    }, this), \" \", (_tuitionPayment$user = tuitionPayment.user) === null || _tuitionPayment$user === void 0 ? void 0 : _tuitionPayment$user.lastName, \" \", (_tuitionPayment$user2 = tuitionPayment.user) === null || _tuitionPayment$user2 === void 0 ? void 0 : _tuitionPayment$user2.firstName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1604,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 \\u0111i\\u1EC7n tho\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1605,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user3 = tuitionPayment.user) === null || _tuitionPayment$user3 === void 0 ? void 0 : _tuitionPayment$user3.phone) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1605,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"L\\u1EDBp:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1606,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user4 = tuitionPayment.user) === null || _tuitionPayment$user4 === void 0 ? void 0 : _tuitionPayment$user4.class) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1606,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u01B0\\u1EDDng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1607,\n                      columnNumber: 30\n                    }, this), \" \", ((_tuitionPayment$user5 = tuitionPayment.user) === null || _tuitionPayment$user5 === void 0 ? void 0 : _tuitionPayment$user5.highSchool) || 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1607,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1603,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1601,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Th\\xF4ng tin thanh to\\xE1n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1613,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-lg p-4 space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Th\\xE1ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1615,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.monthFormatted]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1615,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1616,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1616,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"S\\u1ED1 ti\\u1EC1n \\u0111\\xE3 \\u0111\\xF3ng:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1617,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency(tuitionPayment.paidAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1617,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"C\\xF2n l\\u1EA1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1618,\n                      columnNumber: 30\n                    }, this), \" \", formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1618,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Tr\\u1EA1ng th\\xE1i:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1620,\n                      columnNumber: 29\n                    }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-1 rounded-full text-xs \".concat(tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' : tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' : tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'),\n                      children: tuitionPayment.status === 'PAID' ? 'Đã thanh toán' : tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' : tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' : 'Quá hạn'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1621,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1619,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ng\\xE0y thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1632,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1632,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"H\\u1EA1n thanh to\\xE1n:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1633,\n                      columnNumber: 30\n                    }, this), \" \", tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1633,\n                    columnNumber: 27\n                  }, this), tuitionPayment.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium\",\n                      children: \"Ghi ch\\xFA:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1634,\n                      columnNumber: 54\n                    }, this), \" \", tuitionPayment.note]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1634,\n                    columnNumber: 51\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1614,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1612,\n                columnNumber: 23\n              }, this), studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-2\",\n                  children: \"Danh s\\xE1ch h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1641,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: studentClassTuitionsAdmin.classTuitions.map(tuition => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white border border-gray-200 rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-medium\",\n                        children: tuition.className\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1646,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\",\n                        children: [\"L\\u1EDBp \", tuition.classGrade]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1647,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1645,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"H\\u1ECDc ph\\xED:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1651,\n                        columnNumber: 36\n                      }, this), \" \", formatCurrency(tuition.amount)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1651,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ng\\xE0y tham gia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1652,\n                        columnNumber: 36\n                      }, this), \" \", new Date(tuition.joinDate).toLocaleDateString()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1652,\n                      columnNumber: 33\n                    }, this), tuition.note && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Ghi ch\\xFA:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1653,\n                        columnNumber: 53\n                      }, this), \" \", tuition.note]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1653,\n                      columnNumber: 50\n                    }, this)]\n                  }, tuition.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1644,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1642,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"T\\u1ED5ng h\\u1ECDc ph\\xED c\\xE1c l\\u1EDBp: \", formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1658,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium\",\n                    children: [\"S\\u1ED1 ti\\u1EC1n c\\u1EA7n \\u0111\\xF3ng: \", formatCurrency(tuitionPayment.expectedAmount || 0)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1659,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1657,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1640,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-4 mt-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"M\\u1ECDi th\\u1EAFc m\\u1EAFc xin li\\xEAn h\\u1EC7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1668,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Hotline: 0333726202\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1669,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1667,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"Ch\\u1EEF k\\xFD x\\xE1c nh\\u1EADn\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1672,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1673,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Ng\\u01B0\\u1EDDi thu h\\u1ECDc ph\\xED\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1674,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium\",\n                      children: \"Tri\\u1EC7u Minh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1675,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1671,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1666,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1665,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1584,\n              columnNumber: 21\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2 flex-col mx-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(tuitionPayment.id),\n                className: \"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\",\n                children: \"Ch\\u1EC9nh s\\u1EEDa thanh to\\xE1n\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1683,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleExportImage,\n                className: \"flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 flex items-center justify-center\",\n                children: exportLoading ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      className: \"opacity-25\",\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      className: \"opacity-75\",\n                      fill: \"currentColor\",\n                      d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1697,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 29\n                  }, this), \"\\u0110ang xu\\u1EA5t...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1694,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Xu\\u1EA5t \\u1EA3nh\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1702,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1689,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1682,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1583,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"Kh\\xF4ng t\\xECm th\\u1EA5y th\\xF4ng tin thanh to\\xE1n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1708,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1577,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1123,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showConfirmModal,\n      onConfirm: confirmDelete,\n      text: \"B\\u1EA1n c\\xF3 ch\\u1EAFc ch\\u1EAFn mu\\u1ED1n x\\xF3a kho\\u1EA3n \\u0111\\xF3ng h\\u1ECDc ph\\xED n\\xE0y?\",\n      onClose: cancelDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1718,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 882,\n    columnNumber: 5\n  }, this);\n};\n_s(TuitionPaymentList, \"gAsDfZf6eZE6iC/fKGtq1WaxBxo=\", false, function () {\n  return [useDispatch, useNavigate, useSelector, useSelector];\n});\n_c = TuitionPaymentList;\nexport default TuitionPaymentList;\nvar _c;\n$RefreshReg$(_c, \"TuitionPaymentList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useDispatch", "useSelector", "useNavigate", "html2canvas", "find", "ClassSearchInput", "UserSearchInput", "fetchTuitionPayments", "deleteTuitionPayment", "createTuitionPayment", "createBatchTuitionPayments", "fetchTuitionStatistics", "updateTuitionPayment", "fetchTuitionPaymentByIdAdmin", "formatCurrency", "formatNumberWithDots", "parseCurrencyInput", "findUsers", "findClasses", "setCurrentPage", "setSearch", "resetFilters", "LoadingSpinner", "Pagination", "ConfirmModal", "Edit", "Trash", "Plus", "Eye", "FileText", "Calendar", "Users", "X", "AlertCircle", "BarChart2", "List", "Search", "AdminLayout", "FunctionBarAdmin", "Chart", "jsxDEV", "_jsxDEV", "TuitionPaymentList", "_s", "_tuitionPayment$user", "_tuitionPayment$user2", "_tuitionPayment$user3", "_tuitionPayment$user4", "_tuitionPayment$user5", "dispatch", "navigate", "tuitionPayments", "tuitionStatistics", "loading", "tuitionPayment", "studentClassTuitionsAdmin", "state", "tuition", "page", "totalPages", "total", "pageSize", "pagination", "inputValue", "setInputValue", "showConfirmModal", "setShowConfirmModal", "paymentToDelete", "setPaymentToDelete", "showRightPanel", "setShowRightPanel", "rightPanelType", "setRightPanelType", "filterMonth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterStatus", "setFilterStatus", "filterOverdue", "setFilterOverdue", "filterClass", "setFilterClass", "filterClassId", "setFilterClassId", "classSearchTerm", "setClassSearchTerm", "selectedUserId", "setSelectedUserId", "userSearchTerm", "setUserSearchTerm", "viewMode", "setViewMode", "startMonth", "setStartMonth", "endMonth", "setEndMonth", "monthlyChartRef", "classChartRef", "monthlyChartInstance", "classChartInstance", "batchMonth", "setBatchMonth", "batchAmount", "setBatchAmount", "batchAmountFormatted", "setBatchAmountFormatted", "batchDueDate", "setBatchDueDate", "batchClass", "setBatchClass", "batchNote", "setBatchNote", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "addUserId", "setAddUserId", "addUserSearchTerm", "setAddUserSearchTerm", "addMonth", "setAddMonth", "addExpectedAmount", "setAddExpectedAmount", "addExpectedAmountFormatted", "setAddExpectedAmountFormatted", "addPaidAmount", "setAddPaidAmount", "addPaidAmountFormatted", "setAddPaidAmountFormatted", "addPaymentDate", "setAddPaymentDate", "addDueDate", "setAddDueDate", "addStatus", "setAddStatus", "addNote", "setAddNote", "addCalculateExpected", "setAddCalculateExpected", "editId", "setEditId", "editExpectedAmount", "setEditExpectedAmount", "editExpectedAmountFormatted", "setEditExpectedAmountFormatted", "editPaidAmount", "setEditPaidAmount", "editPaidAmountFormatted", "setEditPaidAmountFormatted", "editPaymentDate", "setEditPaymentDate", "editStatus", "setEditStatus", "editNote", "setEditNote", "calculateExpected", "setCalculateExpected", "viewPayment", "setViewPayment", "viewClassTuitions", "setViewClassTuitions", "viewLoading", "setViewLoading", "exportLoading", "setExportLoading", "detailsContainerRef", "current", "_tuitionStatistics$mo", "_tuitionStatistics$mo2", "_tuitionStatistics$mo3", "_tuitionStatistics$cl", "_tuitionStatistics$cl2", "_tuitionStatistics$cl3", "destroy", "monthlyData", "labels", "monthlyStatistics", "map", "stat", "monthFormatted", "datasets", "label", "data", "totalExpectedAmount", "backgroundColor", "borderColor", "borderWidth", "totalPaidAmount", "classData", "classStatistics", "concat", "userClass", "monthlyCtx", "getContext", "type", "options", "responsive", "scales", "y", "beginAtZero", "ticks", "callback", "value", "plugins", "tooltip", "callbacks", "context", "dataset", "raw", "classCtx", "handleSelectClass", "classItem", "id", "name", "handleClearClassSelection", "handleSelectUser", "userItem", "firstName", "lastName", "handleClearUserSelection", "handleSearch", "search", "sortOrder", "status", "month", "overdue", "classId", "handleAddTuitionSubmit", "e", "preventDefault", "errors", "userId", "test", "expectedAmount", "isNaN", "Number", "paidAmount", "dueDate", "Object", "keys", "length", "paymentData", "note", "paymentDate", "closeRightPanel", "error", "console", "submit", "handleViewModeChange", "mode", "currentDate", "Date", "currentYear", "getFullYear", "currentMonth", "getMonth", "formattedMonth", "handleEdit", "response", "unwrap", "payment", "toISOString", "split", "handleView", "handleExportImage", "originalStyle", "getAttribute", "setAttribute", "element", "canvas", "scale", "useCORS", "logging", "margin", "top", "right", "bottom", "left", "imageUrl", "toDataURL", "link", "document", "createElement", "fileName", "user", "download", "href", "click", "handleAdd", "today", "formattedDate", "year", "handleBatchAdd", "handleCreateBatchTuition", "handleCreateByClass", "validateEditForm", "handleUpdateTuitionPayment", "parsedPaidAmount", "parsedExpectedAmount", "validateBatchTuitionForm", "handleBatchTuitionSubmit", "parsedBatchAmount", "batchData", "handleDelete", "confirmDelete", "cancelDelete", "getStatusBadge", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "ButtonFunctionBarAdmin", "_ref", "icon", "text", "onClick", "iconAdd", "size", "iconBatch", "iconCalendar", "iconUsers", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "placeholder", "onChange", "target", "Array", "from", "_", "i", "monthStr", "selectedClassId", "onSelect", "onClear", "strokeWidth", "index", "_payment$user", "_payment$user2", "_payment$user3", "_payment$user4", "class", "highSchool", "toLocaleDateString", "isOverdue", "title", "currentPage", "onPageChange", "totalItems", "limit", "onSubmit", "role", "checked", "htmlFor", "replace", "parseInt", "disabled", "rows", "required", "ref", "phone", "classTuitions", "classGrade", "amount", "joinDate", "totalAmount", "cx", "cy", "r", "isOpen", "onConfirm", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/tuition/TuitionPaymentList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport html2canvas from \"html2canvas\";\r\nimport { find } from \"lodash\";\r\nimport ClassSearchInput from \"../../../components/ClassSearchInput\";\r\nimport UserSearchInput from \"../../../components/UserSearchInput\";\r\nimport {\r\n  fetchTuitionPayments,\r\n  deleteTuitionPayment,\r\n  createTuitionPayment,\r\n  createBatchTuitionPayments,\r\n  fetchTuitionStatistics,\r\n  updateTuitionPayment,\r\n  fetchTuitionPaymentByIdAdmin,\r\n} from \"src/features/tuition/tuitionSlice\";\r\nimport { formatCurrency, formatNumberWithDots, parseCurrencyInput } from \"src/utils/formatters\";\r\nimport { findUsers } from \"src/features/user/userSlice\";\r\nimport { findClasses } from \"src/features/class/classSlice\";\r\nimport { setCurrentPage, setSearch, resetFilters } from \"src/features/tuition/tuitionSlice\";\r\nimport LoadingSpinner from \"src/components/loading/LoadingSpinner\";\r\nimport Pagination from \"src/components/Pagination\";\r\nimport ConfirmModal from \"src/components/modal/ConfirmDeleteModal\";\r\nimport { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from \"lucide-react\";\r\nimport AdminLayout from \"src/layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"src/components/bar/FunctionBarAdmin\";\r\nimport Chart from 'chart.js/auto';\r\n\r\nconst TuitionPaymentList = () => {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);\r\n\r\n  const { page, totalPages, total, pageSize } = useSelector(\r\n    (state) => state.tuition.pagination\r\n  );\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n  const [showConfirmModal, setShowConfirmModal] = useState(false);\r\n  const [paymentToDelete, setPaymentToDelete] = useState(null);\r\n  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên\r\n  const [showRightPanel, setShowRightPanel] = useState(false);\r\n  const [rightPanelType, setRightPanelType] = useState(\"\"); // \"batch\", \"batchByClass\", \"batchByMonth\"\r\n\r\n  // State cho bộ lọc\r\n  const [filterMonth, setFilterMonth] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"\");\r\n  const [filterOverdue, setFilterOverdue] = useState(\"\");\r\n  const [filterClass, setFilterClass] = useState(\"\");\r\n  const [filterClassId, setFilterClassId] = useState(\"\");\r\n  const [classSearchTerm, setClassSearchTerm] = useState(\"\");\r\n\r\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\r\n  const [userSearchTerm, setUserSearchTerm] = useState(\"\");\r\n\r\n  // const [selectedClassId, setSelectedClassId] = useState(\"\");\r\n  // const [classSearchTerm1, setClassSearchTerm1] = useState(\"\");\r\n\r\n  // State cho chế độ xem\r\n  const [viewMode, setViewMode] = useState(\"table\"); // \"table\" hoặc \"statistics\"\r\n  const [startMonth, setStartMonth] = useState(\"\");\r\n  const [endMonth, setEndMonth] = useState(\"\");\r\n\r\n  // Refs cho biểu đồ\r\n  const monthlyChartRef = useRef(null);\r\n  const classChartRef = useRef(null);\r\n  const monthlyChartInstance = useRef(null);\r\n  const classChartInstance = useRef(null);\r\n\r\n  // State cho form tạo học phí hàng loạt\r\n  const [batchMonth, setBatchMonth] = useState(\"\");\r\n  const [batchAmount, setBatchAmount] = useState(\"\");\r\n  const [batchAmountFormatted, setBatchAmountFormatted] = useState(\"\");\r\n  const [batchDueDate, setBatchDueDate] = useState(\"\");\r\n  const [batchClass, setBatchClass] = useState(\"\");\r\n  const [batchNote, setBatchNote] = useState(\"\");\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // State cho form thêm học phí\r\n  const [addUserId, setAddUserId] = useState(\"\");\r\n  const [addUserSearchTerm, setAddUserSearchTerm] = useState(\"\");\r\n  const [addMonth, setAddMonth] = useState(\"\");\r\n  const [addExpectedAmount, setAddExpectedAmount] = useState(\"\");\r\n  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState(\"\");\r\n  const [addPaidAmount, setAddPaidAmount] = useState(\"\");\r\n  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState(\"\");\r\n  const [addPaymentDate, setAddPaymentDate] = useState(\"\");\r\n  const [addDueDate, setAddDueDate] = useState(\"\");\r\n  const [addStatus, setAddStatus] = useState(\"\");\r\n  const [addNote, setAddNote] = useState(\"\");\r\n  const [addCalculateExpected, setAddCalculateExpected] = useState(false);\r\n\r\n  // State cho form chỉnh sửa học phí\r\n  const [editId, setEditId] = useState(null);\r\n  const [editExpectedAmount, setEditExpectedAmount] = useState(\"\");\r\n  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState(\"\");\r\n  const [editPaidAmount, setEditPaidAmount] = useState(\"\");\r\n  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState(\"\");\r\n  const [editPaymentDate, setEditPaymentDate] = useState(\"\");\r\n  const [editStatus, setEditStatus] = useState(\"\");\r\n  const [editNote, setEditNote] = useState(\"\");\r\n  const [calculateExpected, setCalculateExpected] = useState(false);\r\n\r\n  // State cho xem chi tiết học phí\r\n  const [viewPayment, setViewPayment] = useState(null);\r\n  const [viewClassTuitions, setViewClassTuitions] = useState(null);\r\n  const [viewLoading, setViewLoading] = useState(false);\r\n  const [exportLoading, setExportLoading] = useState(false);\r\n  const detailsContainerRef = useRef(null);\r\n\r\n  // useEffect(() => {\r\n  //   if (!didInit) {\r\n  //     dispatch(resetFilters());\r\n  //     setDidInit(true);\r\n  //   }\r\n  // }, [dispatch, didInit]);\r\n\r\n  // Hiệu ứng để vẽ biểu đồ khi có dữ liệu thống kê\r\n  useEffect(() => {\r\n    if (viewMode === 'statistics' && tuitionStatistics && monthlyChartRef.current && classChartRef.current) {\r\n      // Hủy biểu đồ cũ nếu có\r\n      if (monthlyChartInstance.current) {\r\n        monthlyChartInstance.current.destroy();\r\n      }\r\n      if (classChartInstance.current) {\r\n        classChartInstance.current.destroy();\r\n      }\r\n\r\n      // Chuẩn bị dữ liệu cho biểu đồ theo tháng\r\n      const monthlyData = {\r\n        labels: tuitionStatistics.monthlyStatistics?.map(stat => stat.monthFormatted) || [],\r\n        datasets: [\r\n          {\r\n            label: 'Số tiền cần thu',\r\n            data: tuitionStatistics.monthlyStatistics?.map(stat => stat.totalExpectedAmount) || [],\r\n            backgroundColor: 'rgba(54, 162, 235, 0.5)',\r\n            borderColor: 'rgba(54, 162, 235, 1)',\r\n            borderWidth: 1\r\n          },\r\n          {\r\n            label: 'Số tiền đã thu',\r\n            data: tuitionStatistics.monthlyStatistics?.map(stat => stat.totalPaidAmount) || [],\r\n            backgroundColor: 'rgba(75, 192, 192, 0.5)',\r\n            borderColor: 'rgba(75, 192, 192, 1)',\r\n            borderWidth: 1\r\n          }\r\n        ]\r\n      };\r\n\r\n      // Chuẩn bị dữ liệu cho biểu đồ theo lớp\r\n      const classData = {\r\n        labels: tuitionStatistics.classStatistics?.map(stat => `Lớp ${stat.userClass}`) || [],\r\n        datasets: [\r\n          {\r\n            label: 'Số tiền cần thu',\r\n            data: tuitionStatistics.classStatistics?.map(stat => stat.totalExpectedAmount) || [],\r\n            backgroundColor: 'rgba(153, 102, 255, 0.5)',\r\n            borderColor: 'rgba(153, 102, 255, 1)',\r\n            borderWidth: 1\r\n          },\r\n          {\r\n            label: 'Số tiền đã thu',\r\n            data: tuitionStatistics.classStatistics?.map(stat => stat.totalPaidAmount) || [],\r\n            backgroundColor: 'rgba(255, 159, 64, 0.5)',\r\n            borderColor: 'rgba(255, 159, 64, 1)',\r\n            borderWidth: 1\r\n          }\r\n        ]\r\n      };\r\n\r\n      // Vẽ biểu đồ theo tháng\r\n      const monthlyCtx = monthlyChartRef.current.getContext('2d');\r\n      monthlyChartInstance.current = new Chart(monthlyCtx, {\r\n        type: 'bar',\r\n        data: monthlyData,\r\n        options: {\r\n          responsive: true,\r\n          scales: {\r\n            y: {\r\n              beginAtZero: true,\r\n              ticks: {\r\n                callback: function (value) {\r\n                  return formatCurrency(value);\r\n                }\r\n              }\r\n            }\r\n          },\r\n          plugins: {\r\n            tooltip: {\r\n              callbacks: {\r\n                label: function (context) {\r\n                  return `${context.dataset.label}: ${formatCurrency(context.raw)}`;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      // Vẽ biểu đồ theo lớp\r\n      const classCtx = classChartRef.current.getContext('2d');\r\n      classChartInstance.current = new Chart(classCtx, {\r\n        type: 'bar',\r\n        data: classData,\r\n        options: {\r\n          responsive: true,\r\n          scales: {\r\n            y: {\r\n              beginAtZero: true,\r\n              ticks: {\r\n                callback: function (value) {\r\n                  return formatCurrency(value);\r\n                }\r\n              }\r\n            }\r\n          },\r\n          plugins: {\r\n            tooltip: {\r\n              callbacks: {\r\n                label: function (context) {\r\n                  return `${context.dataset.label}: ${formatCurrency(context.raw)}`;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }, [viewMode, tuitionStatistics]);\r\n\r\n  useEffect(() => {\r\n    dispatch(findClasses(\"\"));\r\n    dispatch(findUsers(\"\"));\r\n  }, [dispatch]);\r\n\r\n  // Xử lý khi người dùng chọn một lớp\r\n  const handleSelectClass = (classItem) => {\r\n    setFilterClassId(classItem.id);\r\n    setClassSearchTerm(classItem.name);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn lớp\r\n  const handleClearClassSelection = () => {\r\n    setFilterClassId(\"\");\r\n    setClassSearchTerm(\"\");\r\n  };\r\n\r\n  // Xử lý khi người dùng chọn một người dùng\r\n  const handleSelectUser = (userItem) => {\r\n    setSelectedUserId(userItem.id);\r\n    setUserSearchTerm(userItem.firstName + \" \" + userItem.lastName);\r\n  };\r\n\r\n  // Xử lý khi người dùng xóa lựa chọn người dùng\r\n  const handleClearUserSelection = () => {\r\n    setSelectedUserId(\"\");\r\n    setUserSearchTerm(\"\");\r\n  };\r\n\r\n\r\n\r\n  // Hàm xử lý tìm kiếm\r\n  const handleSearch = () => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: 1, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass,\r\n        classId: filterClassId\r\n      })\r\n    );\r\n  };\r\n\r\n  // Hàm xử lý thêm học phí mới\r\n  const handleAddTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validate form\r\n    const errors = {};\r\n\r\n    // Validate userId (required)\r\n    if (!addUserId) {\r\n      errors.userId = \"Vui lòng chọn học sinh\";\r\n    }\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!addMonth) {\r\n      errors.month = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(addMonth)) {\r\n      errors.month = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate expected amount (required if not calculating automatically)\r\n    if (!addCalculateExpected && !addExpectedAmount) {\r\n      errors.expectedAmount = \"Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động\";\r\n    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {\r\n      errors.expectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate paid amount (must be a positive number if provided)\r\n    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {\r\n      errors.paidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!addDueDate) {\r\n      errors.dueDate = \"Vui lòng chọn hạn thanh toán\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!addStatus) {\r\n      errors.status = \"Vui lòng chọn trạng thái\";\r\n    }\r\n\r\n    // If there are errors, display them and stop submission\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        userId: addUserId,\r\n        month: addMonth,\r\n        status: addStatus,\r\n        note: addNote,\r\n        dueDate: addDueDate\r\n      };\r\n\r\n      // Only include expectedAmount if not calculating automatically\r\n      if (!addCalculateExpected && addExpectedAmount) {\r\n        paymentData.expectedAmount = Number(addExpectedAmount);\r\n      }\r\n\r\n      // Include paidAmount if provided\r\n      if (addPaidAmount) {\r\n        paymentData.paidAmount = Number(addPaidAmount);\r\n      }\r\n\r\n      // Include paymentDate if provided\r\n      if (addPaymentDate) {\r\n        paymentData.paymentDate = addPaymentDate;\r\n      }\r\n\r\n      // Call API to create tuition payment\r\n      await dispatch(createTuitionPayment(paymentData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n\r\n      // Reset form\r\n      setAddUserId(\"\");\r\n      setAddUserSearchTerm(\"\");\r\n      setAddMonth(\"\");\r\n      setAddExpectedAmount(\"\");\r\n      setAddExpectedAmountFormatted(\"\");\r\n      setAddPaidAmount(\"\");\r\n      setAddPaidAmountFormatted(\"\");\r\n      setAddPaymentDate(\"\");\r\n      setAddDueDate(\"\");\r\n      setAddStatus(\"\");\r\n      setAddNote(\"\");\r\n      setAddCalculateExpected(false);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error creating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xử lý khi thay đổi chế độ xem\r\n  const handleViewModeChange = (mode) => {\r\n    setViewMode(mode);\r\n\r\n    // Nếu chuyển sang chế độ thống kê, tải dữ liệu thống kê\r\n    if (mode === 'statistics') {\r\n      // Khởi tạo giá trị mặc định cho startMonth và endMonth nếu chưa có\r\n      const currentDate = new Date();\r\n      const currentYear = currentDate.getFullYear();\r\n      const currentMonth = currentDate.getMonth() + 1;\r\n\r\n      // Nếu chưa có startMonth, đặt là tháng hiện tại của năm trước\r\n      if (!startMonth) {\r\n        const formattedMonth = currentMonth < 10 ? `0${currentMonth}` : `${currentMonth}`;\r\n        setStartMonth(`${currentYear - 1}-${formattedMonth}`);\r\n      }\r\n\r\n      // Nếu chưa có endMonth, đặt là tháng hiện tại\r\n      if (!endMonth) {\r\n        const formattedMonth = currentMonth < 10 ? `0${currentMonth}` : `${currentMonth}`;\r\n        setEndMonth(`${currentYear}-${formattedMonth}`);\r\n      }\r\n\r\n      // Tải dữ liệu thống kê\r\n      dispatch(fetchTuitionStatistics({\r\n        startMonth: startMonth || `${currentYear - 1}-${currentMonth < 10 ? `0${currentMonth}` : currentMonth}`,\r\n        endMonth: endMonth || `${currentYear}-${currentMonth < 10 ? `0${currentMonth}` : currentMonth}`,\r\n        userClass: filterClass\r\n      }));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    dispatch(\r\n      fetchTuitionPayments({\r\n        search: inputValue,\r\n        page: page, // Reset về trang 1 khi tìm kiếm\r\n        pageSize,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      })\r\n    );\r\n  }, [dispatch, page, pageSize]);\r\n\r\n  const handleEdit = async (id) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Fetch the payment details\r\n      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();\r\n      const payment = response.data;\r\n\r\n      // Set the edit form state\r\n      setEditId(id);\r\n      setEditExpectedAmount(payment.expectedAmount || \"\");\r\n      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : \"\");\r\n      setEditPaidAmount(payment.paidAmount || \"\");\r\n      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : \"\");\r\n      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : \"\");\r\n      setEditStatus(payment.status || \"\");\r\n      setEditNote(payment.note || \"\");\r\n      setCalculateExpected(false);\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"edit\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleView = async (id, userId, month) => {\r\n    setViewLoading(true);\r\n    try {\r\n      // Fetch the payment details\r\n      await dispatch(fetchTuitionPaymentByIdAdmin(id))\r\n\r\n      // Fetch the class tuitions for the student in the payment month\r\n\r\n      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({\r\n      //   userId,\r\n      //   month\r\n      // }))\r\n\r\n      // Show the right panel\r\n      setRightPanelType(\"view\");\r\n      setShowRightPanel(true);\r\n    } catch (error) {\r\n      console.error(\"Error fetching payment details:\", error);\r\n    } finally {\r\n      setViewLoading(false);\r\n    }\r\n  };\r\n\r\n  // Hàm xuất chi tiết thanh toán thành ảnh\r\n  const handleExportImage = async () => {\r\n    if (!detailsContainerRef.current) return;\r\n\r\n    setExportLoading(true);\r\n    try {\r\n      // Lưu trữ style hiện tại\r\n      const originalStyle = detailsContainerRef.current.getAttribute('style') || '';\r\n\r\n      // Thêm style tạm thời cho việc xuất ảnh\r\n      detailsContainerRef.current.setAttribute(\r\n        'style',\r\n        `${originalStyle};\r\n         background-color: white;\r\n         border-radius: 8px;\r\n         box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);`\r\n      );\r\n\r\n      const element = detailsContainerRef.current;\r\n      const canvas = await html2canvas(element, {\r\n        scale: 2, // Tăng độ phân giải\r\n        useCORS: true, // Cho phép tải hình ảnh từ các domain khác\r\n        logging: false,\r\n        backgroundColor: '#ffffff',\r\n        margin: {\r\n          top: 20,\r\n          right: 20,\r\n          bottom: 20,\r\n          left: 20\r\n        }\r\n      });\r\n\r\n      // Khôi phục style ban đầu\r\n      detailsContainerRef.current.setAttribute('style', originalStyle);\r\n\r\n      // Chuyển canvas thành URL\r\n      const imageUrl = canvas.toDataURL('image/png');\r\n\r\n      // Tạo link tải xuống\r\n      const link = document.createElement('a');\r\n      const fileName = tuitionPayment?.user\r\n        ? `hoc-phi-${tuitionPayment.user.lastName}-${tuitionPayment.user.firstName}-${tuitionPayment.monthFormatted}.png`\r\n        : `hoc-phi-${new Date().toISOString()}.png`;\r\n\r\n      link.download = fileName;\r\n      link.href = imageUrl;\r\n      link.click();\r\n    } catch (error) {\r\n      console.error(\"Error exporting image:\", error);\r\n    } finally {\r\n      setExportLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAdd = () => {\r\n    // Reset form state\r\n    setAddUserId(\"\");\r\n    setAddUserSearchTerm(\"\");\r\n    setAddMonth(\"\");\r\n    setAddExpectedAmount(\"\");\r\n    setAddExpectedAmountFormatted(\"\");\r\n    setAddPaidAmount(\"\");\r\n    setAddPaidAmountFormatted(\"\");\r\n    setAddPaymentDate(\"\");\r\n    setAddDueDate(\"\");\r\n    setAddStatus(\"\");\r\n    setAddNote(\"\");\r\n    setAddCalculateExpected(false);\r\n    setFormErrors({});\r\n\r\n    // Set current date as default for dueDate\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split('T')[0];\r\n    setAddDueDate(formattedDate);\r\n\r\n    // Set current month as default for month\r\n    const year = today.getFullYear();\r\n    const month = today.getMonth() + 1;\r\n    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;\r\n    setAddMonth(formattedMonth);\r\n\r\n    // Open panel\r\n    setRightPanelType(\"add\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleBatchAdd = () => {\r\n    setRightPanelType(\"batch\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateBatchTuition = () => {\r\n    setRightPanelType(\"batchByMonth\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const handleCreateByClass = () => {\r\n    setRightPanelType(\"batchByClass\");\r\n    setShowRightPanel(true);\r\n  };\r\n\r\n  const closeRightPanel = () => {\r\n    setShowRightPanel(false);\r\n    setRightPanelType(\"\");\r\n    // Reset form state\r\n    setBatchMonth(\"\");\r\n    setBatchAmount(\"\");\r\n    setBatchAmountFormatted(\"\");\r\n    setBatchDueDate(\"\");\r\n    setBatchClass(\"\");\r\n    setBatchNote(\"\");\r\n    setEditId(null);\r\n    setEditExpectedAmount(\"\");\r\n    setEditExpectedAmountFormatted(\"\");\r\n    setEditPaidAmount(\"\");\r\n    setEditPaidAmountFormatted(\"\");\r\n    setEditPaymentDate(\"\");\r\n    setEditStatus(\"\");\r\n    setEditNote(\"\");\r\n    setCalculateExpected(false);\r\n    // Reset view state\r\n    setViewPayment(null);\r\n    setViewClassTuitions(null);\r\n    setFormErrors({});\r\n    setIsSubmitting(false);\r\n    setViewLoading(false);\r\n  };\r\n\r\n  // Validate edit form data\r\n  const validateEditForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate paid amount (must be a positive number)\r\n    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {\r\n      errors.editPaidAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate expected amount (must be a positive number if provided)\r\n    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {\r\n      errors.editExpectedAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate status (required)\r\n    if (!editStatus) {\r\n      errors.editStatus = \"Trạng thái không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle edit form submission\r\n  const handleUpdateTuitionPayment = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;\r\n    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;\r\n\r\n    // Update the actual values with parsed values\r\n    if (editPaidAmountFormatted) {\r\n      setEditPaidAmount(parsedPaidAmount);\r\n    }\r\n\r\n    if (editExpectedAmountFormatted && !calculateExpected) {\r\n      setEditExpectedAmount(parsedExpectedAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateEditForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const paymentData = {\r\n        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,\r\n        status: editStatus,\r\n        note: editNote,\r\n        calculateExpected: calculateExpected\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided and not calculating automatically\r\n      if (parsedExpectedAmount && !calculateExpected) {\r\n        paymentData.expectedAmount = Number(parsedExpectedAmount);\r\n      }\r\n\r\n      // Only include paymentDate if it's provided\r\n      if (editPaymentDate) {\r\n        paymentData.paymentDate = editPaymentDate;\r\n      }\r\n\r\n      // Call API to update tuition payment\r\n      await dispatch(updateTuitionPayment({\r\n        id: editId,\r\n        paymentData\r\n      }));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\",\r\n        status: filterStatus,\r\n        month: filterMonth,\r\n        overdue: filterOverdue,\r\n        userClass: filterClass\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error updating tuition payment:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi cập nhật học phí\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Validate form data\r\n  const validateBatchTuitionForm = () => {\r\n    const errors = {};\r\n\r\n    // Validate month (required and format YYYY-MM)\r\n    if (!batchMonth) {\r\n      errors.batchMonth = \"Tháng không được để trống\";\r\n    } else if (!/^\\d{4}-\\d{2}$/.test(batchMonth)) {\r\n      errors.batchMonth = \"Định dạng tháng không hợp lệ\";\r\n    }\r\n\r\n    // Validate amount (optional but must be positive if provided)\r\n    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {\r\n      errors.batchAmount = \"Số tiền phải là số dương\";\r\n    }\r\n\r\n    // Validate class selection (required)\r\n    if (!batchClass) {\r\n      errors.batchClass = \"Vui lòng chọn lớp\";\r\n    }\r\n\r\n    // Validate due date (required)\r\n    if (!batchDueDate) {\r\n      errors.batchDueDate = \"Hạn thanh toán không được để trống\";\r\n    }\r\n\r\n    return errors;\r\n  };\r\n\r\n  // Handle batch tuition form submission\r\n  const handleBatchTuitionSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Parse formatted values to numbers\r\n    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;\r\n\r\n    // Update the actual value with parsed value\r\n    if (batchAmountFormatted) {\r\n      setBatchAmount(parsedBatchAmount);\r\n    }\r\n\r\n    // Validate form\r\n    const errors = validateBatchTuitionForm();\r\n    if (Object.keys(errors).length > 0) {\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare data for API call\r\n      const batchData = {\r\n        month: batchMonth,\r\n        dueDate: batchDueDate,\r\n        batchClass,\r\n        note: batchNote\r\n      };\r\n\r\n      // Only include expectedAmount if it's provided\r\n      if (parsedBatchAmount) {\r\n        batchData.expectedAmount = Number(parsedBatchAmount);\r\n      }\r\n\r\n      // Call API to create batch tuition payments\r\n      await dispatch(createBatchTuitionPayments(batchData));\r\n\r\n      // Close panel and refresh data\r\n      closeRightPanel();\r\n      dispatch(fetchTuitionPayments({\r\n        page: page,\r\n        pageSize,\r\n        search: inputValue,\r\n        sortOrder: \"DESC\"\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Error creating batch tuition payments:\", error);\r\n      setFormErrors({ submit: \"Có lỗi xảy ra khi tạo học phí hàng loạt\" });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = (id) => {\r\n    setPaymentToDelete(id);\r\n    setShowConfirmModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    dispatch(deleteTuitionPayment(paymentToDelete));\r\n    setShowConfirmModal(false);\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setShowConfirmModal(false);\r\n    setPaymentToDelete(null);\r\n  };\r\n\r\n  const getStatusBadge = (status) => {\r\n    switch (status) {\r\n      case \"PAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-green-100 text-green-800\">\r\n            Đã thanh toán\r\n          </span>\r\n        );\r\n      case \"UNPAID\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-red-100 text-red-800\">\r\n            Chưa thanh toán\r\n          </span>\r\n        );\r\n      case \"OVERDUE\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800\">\r\n            Quá hạn\r\n          </span>\r\n        );\r\n      case \"PARTIAL\":\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800\">\r\n            Thanh toán một phần\r\n          </span>\r\n        );\r\n      default:\r\n        return (\r\n          <span className=\"px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800\">\r\n            {status}\r\n          </span>\r\n        );\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner />;\r\n  }\r\n\r\n  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {\r\n    return (\r\n      <button\r\n        onClick={onClick}\r\n        className=\"flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n      >\r\n        {icon}\r\n        <span>{text}</span>\r\n      </button>\r\n    );\r\n  };\r\n\r\n  const iconAdd = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Plus size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconBatch = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <FileText size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconCalendar = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Calendar size={16} />\r\n    </div>\r\n  );\r\n\r\n  const iconUsers = (\r\n    <div data-svg-wrapper className=\"relative\">\r\n      <Users size={16} />\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <AdminLayout>\r\n      <div className=\"text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4\">\r\n        Quản lý thanh toán học phí\r\n      </div>\r\n\r\n      <div className=\"flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex flex-wrap gap-2 items-center mb-4\">\r\n            <div className=\"w-[300px] relative\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 16 16\"\r\n                fill=\"none\"\r\n                className=\"absolute left-[1rem] top-1/2 transform -translate-y-1/2\"\r\n              >\r\n                <path\r\n                  d=\"M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z\"\r\n                  stroke=\"#131214\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Tìm kiếm theo tên học sinh, lớp học...\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                className=\"w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam\"\r\n              />\r\n            </div>\r\n\r\n            {/* Bộ lọc */}\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterMonth}\r\n                onChange={(e) => setFilterMonth(e.target.value)}\r\n              >\r\n                <option value=\"\">Tháng</option>\r\n                {Array.from({ length: 12 }, (_, i) => {\r\n                  const month = i + 1;\r\n                  const year = new Date().getFullYear();\r\n                  const monthStr = month < 10 ? `0${month}` : `${month}`;\r\n                  return (\r\n                    <option key={month} value={`${year}-${monthStr}`}>\r\n                      {`Tháng ${month}/${year}`}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterStatus}\r\n                onChange={(e) => setFilterStatus(e.target.value)}\r\n              >\r\n                <option value=\"\">Trạng thái</option>\r\n                <option value=\"PAID\">Đã thanh toán</option>\r\n                <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterOverdue}\r\n                onChange={(e) => setFilterOverdue(e.target.value)}\r\n              >\r\n                <option value=\"\">Tình trạng hạn</option>\r\n                <option value=\"true\">Đã quá hạn</option>\r\n                <option value=\"false\">Chưa quá hạn</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"w-[150px]\">\r\n              <select\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                value={filterClass}\r\n                onChange={(e) => setFilterClass(e.target.value)}\r\n              >\r\n                <option value=\"\">Khối</option>\r\n                <option value=\"10\">Khối 10</option>\r\n                <option value=\"11\">Khối 11</option>\r\n                <option value=\"12\">Khối 12</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Tìm kiếm lớp học */}\r\n            <div className=\"w-[250px]\">\r\n              <ClassSearchInput\r\n                value={classSearchTerm}\r\n                selectedClassId={filterClassId}\r\n                onChange={setClassSearchTerm}\r\n                onSelect={handleSelectClass}\r\n                onClear={handleClearClassSelection}\r\n                placeholder=\"Tìm kiếm lớp học...\"\r\n              />\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleSearch}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center\"\r\n            >\r\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              Tìm kiếm\r\n            </button>\r\n            <div className=\"flex border border-gray-300 rounded-md overflow-hidden\">\r\n              <button\r\n                className={`px-4 py-2 flex items-center ${viewMode === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}\r\n                onClick={() => handleViewModeChange('table')}\r\n              >\r\n                <List size={16} className=\"mr-2\" />\r\n                Danh sách\r\n              </button>\r\n              <button\r\n                className={`px-4 py-2 flex items-center ${viewMode === 'statistics' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}\r\n                onClick={() => handleViewModeChange('statistics')}\r\n              >\r\n                <BarChart2 size={16} className=\"mr-2\" />\r\n                Thống kê\r\n              </button>\r\n            </div>\r\n\r\n\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-2 items-center\">\r\n            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />\r\n            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />\r\n            <ButtonFunctionBarAdmin icon={iconUsers} text={'Tạo học phí theo lớp'} onClick={handleCreateByClass} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n        tuitionPayments.length === 0 ? (\r\n          <div className=\"bg-white shadow-md rounded-lg p-6 text-center\">\r\n            <p className=\"text-gray-500\">Không có dữ liệu thanh toán học phí nào.</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full bg-white shadow-md rounded-lg overflow-hidden\">\r\n              <thead className=\"bg-gray-100\">\r\n                <tr>\r\n                  <th className=\"py-3 px-4 text-left\">STT</th>\r\n                  <th className=\"py-3 px-4 text-left\">Học sinh</th>\r\n                  <th className=\"py-3 px-4 text-left\">Lớp</th>\r\n                  <th className=\"py-3 px-4 text-left\">Trường</th>\r\n                  <th className=\"py-3 px-4 text-left\">Số tiền cần</th>\r\n                  <th className=\"py-3 px-4 text-left\">Số tiền đóng</th>\r\n                  <th className=\"py-3 px-4 text-left\">Tháng</th>\r\n                  <th className=\"py-3 px-4 text-left\">Ngày thanh toán</th>\r\n                  <th className=\"py-3 px-4 text-left\">Hạn thanh toán</th>\r\n                  <th className=\"py-3 px-4 text-left\">Trạng thái</th>\r\n                  <th className=\"py-3 px-4 text-left\">Thao tác</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {tuitionPayments.map((payment, index) => (\r\n                  <tr\r\n                    key={payment.id}\r\n                    className={index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\"}\r\n                  >\r\n                    <td className=\"py-3 px-4\">\r\n                      {(page - 1) * pageSize + index + 1}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {payment.user?.lastName + \" \" + payment.user?.firstName || \"N/A\"}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">{payment.user?.class || \"N/A\"}</td>\r\n                    <td className=\"py-3 px-4\">{payment.user?.highSchool || \"N/A\"}</td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {formatCurrency(payment.expectedAmount)}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {formatCurrency(payment.paidAmount)}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">{payment.monthFormatted}</td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {payment.paymentDate\r\n                        ? new Date(payment.paymentDate).toLocaleDateString(\"vi-VN\")\r\n                        : \"Chưa thanh toán\"}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {new Date(payment.dueDate).toLocaleDateString(\"vi-VN\")}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      {getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)}\r\n                    </td>\r\n                    <td className=\"py-3 px-4\">\r\n                      <div className=\"flex space-x-2\">\r\n                        <button\r\n                          onClick={() => handleView(payment.id, payment.userId, payment.month)}\r\n                          className=\"text-blue-500 hover:text-blue-700\"\r\n                          title=\"Xem chi tiết\"\r\n                        >\r\n                          <Eye size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleEdit(payment.id)}\r\n                          className=\"text-yellow-500 hover:text-yellow-700\"\r\n                          title=\"Chỉnh sửa\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(payment.id)}\r\n                          className=\"text-red-500 hover:text-red-700\"\r\n                          title=\"Xóa\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )\r\n      {viewMode === \"table\" && (\r\n        <div className=\"mt-6\">\r\n          <Pagination\r\n            currentPage={page}\r\n            onPageChange={(page) => dispatch(setCurrentPage(page))}\r\n            totalItems={total}\r\n            limit={pageSize}\r\n          />\r\n\r\n        </div>\r\n      )}\r\n\r\n\r\n      {/* Right Panel for Batch Operations */}\r\n      {showRightPanel && (\r\n        <div className=\"fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto\">\r\n          <div className=\"p-4 border-b border-gray-200 flex justify-between items-center\">\r\n            <h2 className=\"text-lg font-semibold\">\r\n              {rightPanelType === \"add\" && \"Thêm thanh toán học phí\"}\r\n              {rightPanelType === \"batchByMonth\" && \"Tạo học phí hàng loạt\"}\r\n              {rightPanelType === \"batchByClass\" && \"Tạo học phí theo lớp\"}\r\n              {rightPanelType === \"edit\" && \"Chỉnh sửa thanh toán học phí\"}\r\n              {rightPanelType === \"view\" && \"Chi tiết thanh toán học phí\"}\r\n            </h2>\r\n            <button\r\n              onClick={closeRightPanel}\r\n              className=\"text-gray-500 hover:text-gray-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          <div>\r\n            {rightPanelType === \"add\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Thêm khoản thanh toán học phí mới cho học sinh.</p>\r\n                {/* Form content for adding new payment */}\r\n                <form className=\"space-y-4\" onSubmit={handleAddTuitionSubmit}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Học sinh <span className=\"text-red-500\">*</span></label>\r\n                    <UserSearchInput\r\n                      value={addUserSearchTerm}\r\n                      selectedUserId={addUserId}\r\n                      onChange={setAddUserSearchTerm}\r\n                      onSelect={(user) => {\r\n                        setAddUserId(user.id);\r\n                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);\r\n                      }}\r\n                      onClear={() => {\r\n                        setAddUserId(\"\");\r\n                        setAddUserSearchTerm(\"\");\r\n                      }}\r\n                      placeholder=\"Tìm kiếm học sinh...\"\r\n                      role=\"STUDENT\"\r\n                    />\r\n                    {formErrors.userId && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.userId}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addMonth}\r\n                      onChange={(e) => setAddMonth(e.target.value)}\r\n                    />\r\n                    {formErrors.month && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.month}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"addCalculateExpected\"\r\n                          checked={addCalculateExpected}\r\n                          onChange={(e) => setAddCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"addCalculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${addCalculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={addExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddExpectedAmount(value);\r\n                          setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={addCalculateExpected}\r\n                    />\r\n                    {formErrors.expectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.expectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.paidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={addPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setAddPaidAmount(value);\r\n                          setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.paidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.paidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={addPaymentDate}\r\n                      onChange={(e) => setAddPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addDueDate}\r\n                      onChange={(e) => setAddDueDate(e.target.value)}\r\n                    />\r\n                    {formErrors.dueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.dueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.status ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={addStatus}\r\n                      onChange={(e) => setAddStatus(e.target.value)}\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.status && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.status}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={addNote}\r\n                      onChange={(e) => setAddNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start\">\r\n                      <AlertCircle size={20} className=\"mr-2 mt-0.5 flex-shrink-0\" />\r\n                      <p>{formErrors.submit}</p>\r\n                    </div>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thanh toán\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n            {rightPanelType === \"batchByMonth\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>\r\n                {/* Form content for batch tuition by month */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleBatchTuitionSubmit(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchMonth}\r\n                      onChange={(e) => setBatchMonth(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchMonth && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchMonth}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng <span className=\"text-gray-500 text-xs font-normal\">(không bắt buộc)</span>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền hoặc để trống để tự động tính\"\r\n                      value={batchAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                          setBatchAmount(value ? parseInt(value, 10) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.batchAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchAmount}\r\n                      </p>\r\n                    )}\r\n                    <p className=\"mt-1 text-xs text-gray-500\">\r\n                      Nếu để trống, hệ thống sẽ tự động tính dựa trên các lớp học sinh đã tham gia\r\n                    </p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchClass}\r\n                      onChange={(e) => setBatchClass(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn lớp</option>\r\n                      <option value=\"10\">Lớp 10</option>\r\n                      <option value=\"11\">Lớp 11</option>\r\n                      <option value=\"12\">Lớp 12</option>\r\n                    </select>\r\n                    {formErrors.batchClass && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchClass}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán <span className=\"text-red-500\">*</span></label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={batchDueDate}\r\n                      onChange={(e) => setBatchDueDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    {formErrors.batchDueDate && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.batchDueDate}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={batchNote}\r\n                      onChange={(e) => setBatchNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Tạo học phí hàng loạt\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"batchByClass\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Tạo học phí cho tất cả học sinh trong một lớp học.</p>\r\n                {/* Form content for batch tuition by class */}\r\n                <form className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Lớp học</label>\r\n                    <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\r\n                      <option value=\"\">Chọn lớp học</option>\r\n                      <option value=\"1\">Lớp 10A1</option>\r\n                      <option value=\"2\">Lớp 11A2</option>\r\n                      <option value=\"3\">Lớp 12A3</option>\r\n                    </select>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Tháng</label>\r\n                    <input\r\n                      type=\"month\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền cần đóng</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      placeholder=\"Nhập số tiền\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Hạn thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                    ></textarea>\r\n                  </div>\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                  >\r\n                    Tạo học phí theo lớp\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"edit\" && (\r\n              <div className=\"p-4\">\r\n                <p className=\"mb-4\">Chỉnh sửa thông tin thanh toán học phí.</p>\r\n                {/* Form content for editing tuition payment */}\r\n                <form className=\"space-y-4\" onSubmit={(e) => handleUpdateTuitionPayment(e)}>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Số tiền cần đóng\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          id=\"calculateExpected\"\r\n                          checked={calculateExpected}\r\n                          onChange={(e) => setCalculateExpected(e.target.checked)}\r\n                          className=\"mr-2\"\r\n                        />\r\n                        <label htmlFor=\"calculateExpected\" className=\"text-xs text-gray-500\">\r\n                          Tự động tính lại dựa trên các lớp học sinh đã tham gia\r\n                        </label>\r\n                      </div>\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${calculateExpected ? 'bg-gray-100' : ''}`}\r\n                      placeholder=\"Nhập số tiền cần đóng\"\r\n                      value={editExpectedAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                      disabled={calculateExpected}\r\n                    />\r\n                    {formErrors.editExpectedAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editExpectedAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Số tiền đã đóng</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      className={`w-full px-3 py-2 border ${formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      placeholder=\"Nhập số tiền đã đóng\"\r\n                      value={editPaidAmountFormatted}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value.replace(/\\./g, \"\");\r\n                        if (!isNaN(value) || value === \"\") {\r\n                          setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : \"\");\r\n                        }\r\n                      }}\r\n                    />\r\n                    {formErrors.editPaidAmount && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editPaidAmount}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ngày thanh toán</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      value={editPaymentDate}\r\n                      onChange={(e) => setEditPaymentDate(e.target.value)}\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Trạng thái <span className=\"text-red-500\">*</span></label>\r\n                    <select\r\n                      className={`w-full px-3 py-2 border ${formErrors.editStatus ? 'border-red-500' : 'border-gray-300'} rounded-md`}\r\n                      value={editStatus}\r\n                      onChange={(e) => setEditStatus(e.target.value)}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Chọn trạng thái</option>\r\n                      <option value=\"PAID\">Đã thanh toán</option>\r\n                      <option value=\"UNPAID\">Chưa thanh toán</option>\r\n                      <option value=\"PARTIAL\">Thanh toán một phần</option>\r\n                    </select>\r\n                    {formErrors.editStatus && (\r\n                      <p className=\"mt-1 text-sm text-red-500 flex items-center\">\r\n                        <AlertCircle size={14} className=\"mr-1\" /> {formErrors.editStatus}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Ghi chú</label>\r\n                    <textarea\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md\"\r\n                      rows=\"3\"\r\n                      placeholder=\"Nhập ghi chú (nếu có)\"\r\n                      value={editNote}\r\n                      onChange={(e) => setEditNote(e.target.value)}\r\n                    ></textarea>\r\n                  </div>\r\n                  {formErrors.submit && (\r\n                    <p className=\"text-sm text-red-500 flex items-center\">\r\n                      <AlertCircle size={14} className=\"mr-1\" /> {formErrors.submit}\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? \"Đang xử lý...\" : \"Lưu thay đổi\"}\r\n                  </button>\r\n                </form>\r\n              </div>\r\n            )}\r\n\r\n            {rightPanelType === \"view\" && (\r\n              <div>\r\n                {viewLoading ? (\r\n                  <div className=\"flex justify-center items-center h-64\">\r\n                    <LoadingSpinner />\r\n                  </div>\r\n                ) : tuitionPayment ? (\r\n                  <div className=\"space-y-6 mb-4\">\r\n                    <div ref={detailsContainerRef} className=\"space-y-6 p-6 bg-white rounded-lg\">\r\n                      {/* Header cho bản in */}\r\n                      <div className=\"flex items-center justify-between border-b pb-4 mb-2\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3\">\r\n                            TTB\r\n                          </div>\r\n                          <div>\r\n                            <h2 className=\"text-xl font-bold text-gray-800\">Toán Thầy Bee</h2>\r\n                            <p className=\"text-sm text-gray-500\">Chi tiết thanh toán học phí</p>\r\n                            <p className=\"text-sm text-gray-500\">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                      </div>\r\n\r\n                      {/* Thông tin học sinh */}\r\n                      <div className=\"bg-blue-50 p-4 rounded-lg\">\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin học sinh</h3>\r\n                        <div className=\"space-y-2\">\r\n                          <p><span className=\"font-medium\">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>\r\n                          <p><span className=\"font-medium\">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>\r\n                          <p><span className=\"font-medium\">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Thông tin thanh toán */}\r\n                      <div>\r\n                        <h3 className=\"text-lg font-semibold mb-2\">Thông tin thanh toán</h3>\r\n                        <div className=\"bg-white border border-gray-200 rounded-lg p-4 space-y-2\">\r\n                          <p><span className=\"font-medium\">Tháng:</span> {tuitionPayment.monthFormatted}</p>\r\n                          <p><span className=\"font-medium\">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>\r\n                          <p><span className=\"font-medium\">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>\r\n                          <p>\r\n                            <span className=\"font-medium\">Trạng thái:</span> {' '}\r\n                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :\r\n                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :\r\n                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :\r\n                                  'bg-yellow-100 text-yellow-800'\r\n                              }`}>\r\n                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :\r\n                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :\r\n                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :\r\n                                    'Quá hạn'}\r\n                            </span>\r\n                          </p>\r\n                          <p><span className=\"font-medium\">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>\r\n                          <p><span className=\"font-medium\">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>\r\n                          {tuitionPayment.note && <p><span className=\"font-medium\">Ghi chú:</span> {tuitionPayment.note}</p>}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Danh sách học phí lớp */}\r\n                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (\r\n                        <div>\r\n                          <h3 className=\"text-lg font-semibold mb-2\">Danh sách học phí các lớp</h3>\r\n                          <div className=\"space-y-3\">\r\n                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (\r\n                              <div key={tuition.id} className=\"bg-white border border-gray-200 rounded-lg p-3\">\r\n                                <div className=\"flex justify-between items-center mb-2\">\r\n                                  <h4 className=\"font-medium\">{tuition.className}</h4>\r\n                                  <span className=\"bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full\">\r\n                                    Lớp {tuition.classGrade}\r\n                                  </span>\r\n                                </div>\r\n                                <p><span className=\"text-sm text-gray-500\">Học phí:</span> {formatCurrency(tuition.amount)}</p>\r\n                                <p><span className=\"text-sm text-gray-500\">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>\r\n                                {tuition.note && <p><span className=\"text-sm text-gray-500\">Ghi chú:</span> {tuition.note}</p>}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\r\n                            <p className=\"font-medium\">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>\r\n                            <p className=\"font-medium\">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Footer cho bản in */}\r\n                      <div className=\"border-t pt-4 mt-6\">\r\n                        <div className=\"flex justify-between items-center\">\r\n                          <div>\r\n                            <p className=\"text-sm text-gray-500\">Mọi thắc mắc xin liên hệ:</p>\r\n                            <p className=\"text-sm font-medium\">Hotline: 0333726202</p>\r\n                          </div>\r\n                          <div className=\"text-center\">\r\n                            <p className=\"text-sm text-gray-500\">Chữ ký xác nhận</p>\r\n                            <div className=\"h-16\"></div>\r\n                            <p className=\"text-sm font-medium\">Người thu học phí</p>\r\n                            <p className=\"text-sm font-medium\">Triệu Minh</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div> {/* End of detailsContainerRef div */}\r\n\r\n                    {/* Nút chỉnh sửa và xuất ảnh */}\r\n                    <div className=\"flex gap-2 flex-col mx-4\">\r\n                      <button\r\n                        onClick={() => handleEdit(tuitionPayment.id)}\r\n                        className=\"flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600\"\r\n                      >\r\n                        Chỉnh sửa thanh toán\r\n                      </button>\r\n                      <button\r\n                        onClick={handleExportImage}\r\n                        className=\"flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 flex items-center justify-center\"\r\n                      >\r\n                        {exportLoading ? (\r\n                          <span className=\"flex items-center\">\r\n                            <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                            </svg>\r\n                            Đang xuất...\r\n                          </span>\r\n                        ) : (\r\n                          <span>Xuất ảnh</span>\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-8 text-gray-500\">\r\n                    Không tìm thấy thông tin thanh toán\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <ConfirmModal\r\n        isOpen={showConfirmModal}\r\n        onConfirm={confirmDelete}\r\n        text=\"Bạn có chắc chắn muốn xóa khoản đóng học phí này?\"\r\n        onClose={cancelDelete}\r\n      />\r\n    </AdminLayout>\r\n  );\r\n};\r\n\r\nexport default TuitionPaymentList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,aAAa;AACrC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,QACvB,mCAAmC;AAC1C,SAASC,cAAc,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,sBAAsB;AAC/F,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,cAAc,EAAEC,SAAS,EAAEC,YAAY,QAAQ,mCAAmC;AAC3F,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,YAAY,MAAM,yCAAyC;AAClE,SAASC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,CAAC,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACzH,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,KAAK,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC/B,MAAMC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiD,eAAe;IAAEC,iBAAiB;IAAEC,OAAO;IAAEC,cAAc;IAAEC;EAA0B,CAAC,GAAGtD,WAAW,CAAEuD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAAC;EAExI,MAAM;IAAEC,IAAI;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAG5D,WAAW,CACtDuD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACK,UAC3B,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC5D;EACA,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyF,cAAc,EAAEC,iBAAiB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA;;EAEA;EACA,MAAM,CAAC2F,QAAQ,EAAEC,WAAW,CAAC,GAAG5F,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+F,QAAQ,EAAEC,WAAW,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMiG,eAAe,GAAGhG,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMiG,aAAa,GAAGjG,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkG,oBAAoB,GAAGlG,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMmG,kBAAkB,GAAGnG,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuG,WAAW,EAAEC,cAAc,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC2G,YAAY,EAAEC,eAAe,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6G,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+G,SAAS,EAAEC,YAAY,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiH,UAAU,EAAEC,aAAa,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmH,YAAY,EAAEC,eAAe,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACqH,SAAS,EAAEC,YAAY,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyH,QAAQ,EAAEC,WAAW,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6H,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAChF,MAAM,CAAC+H,aAAa,EAAEC,gBAAgB,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiI,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACmI,cAAc,EAAEC,iBAAiB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqI,UAAU,EAAEC,aAAa,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuI,SAAS,EAAEC,YAAY,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyI,OAAO,EAAEC,UAAU,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2I,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;;EAEvE;EACA,MAAM,CAAC6I,MAAM,EAAEC,SAAS,CAAC,GAAG9I,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC+I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACiJ,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAClF,MAAM,CAACmJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqJ,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAACuJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyJ,UAAU,EAAEC,aAAa,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2J,QAAQ,EAAEC,WAAW,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6J,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9J,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+J,WAAW,EAAEC,cAAc,CAAC,GAAGhK,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlK,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACmK,WAAW,EAAEC,cAAc,CAAC,GAAGpK,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqK,aAAa,EAAEC,gBAAgB,CAAC,GAAGtK,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMuK,mBAAmB,GAAGtK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA;EACA;EACA;EACA;EACA;;EAEA;EACAF,SAAS,CAAC,MAAM;IACd,IAAI4F,QAAQ,KAAK,YAAY,IAAIrC,iBAAiB,IAAI2C,eAAe,CAACuE,OAAO,IAAItE,aAAa,CAACsE,OAAO,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACtG;MACA,IAAI3E,oBAAoB,CAACqE,OAAO,EAAE;QAChCrE,oBAAoB,CAACqE,OAAO,CAACO,OAAO,CAAC,CAAC;MACxC;MACA,IAAI3E,kBAAkB,CAACoE,OAAO,EAAE;QAC9BpE,kBAAkB,CAACoE,OAAO,CAACO,OAAO,CAAC,CAAC;MACtC;;MAEA;MACA,MAAMC,WAAW,GAAG;QAClBC,MAAM,EAAE,EAAAR,qBAAA,GAAAnH,iBAAiB,CAAC4H,iBAAiB,cAAAT,qBAAA,uBAAnCA,qBAAA,CAAqCU,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,cAAc,CAAC,KAAI,EAAE;QACnFC,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,EAAAd,sBAAA,GAAApH,iBAAiB,CAAC4H,iBAAiB,cAAAR,sBAAA,uBAAnCA,sBAAA,CAAqCS,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,mBAAmB,CAAC,KAAI,EAAE;UACtFC,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC,EACD;UACEL,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,EAAAb,sBAAA,GAAArH,iBAAiB,CAAC4H,iBAAiB,cAAAP,sBAAA,uBAAnCA,sBAAA,CAAqCQ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACS,eAAe,CAAC,KAAI,EAAE;UAClFH,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;;MAED;MACA,MAAME,SAAS,GAAG;QAChBb,MAAM,EAAE,EAAAL,qBAAA,GAAAtH,iBAAiB,CAACyI,eAAe,cAAAnB,qBAAA,uBAAjCA,qBAAA,CAAmCO,GAAG,CAACC,IAAI,gBAAAY,MAAA,CAAWZ,IAAI,CAACa,SAAS,CAAE,CAAC,KAAI,EAAE;QACrFX,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE,EAAAX,sBAAA,GAAAvH,iBAAiB,CAACyI,eAAe,cAAAlB,sBAAA,uBAAjCA,sBAAA,CAAmCM,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,mBAAmB,CAAC,KAAI,EAAE;UACpFC,eAAe,EAAE,0BAA0B;UAC3CC,WAAW,EAAE,wBAAwB;UACrCC,WAAW,EAAE;QACf,CAAC,EACD;UACEL,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,EAAAV,sBAAA,GAAAxH,iBAAiB,CAACyI,eAAe,cAAAjB,sBAAA,uBAAjCA,sBAAA,CAAmCK,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACS,eAAe,CAAC,KAAI,EAAE;UAChFH,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;;MAED;MACA,MAAMM,UAAU,GAAGjG,eAAe,CAACuE,OAAO,CAAC2B,UAAU,CAAC,IAAI,CAAC;MAC3DhG,oBAAoB,CAACqE,OAAO,GAAG,IAAI/H,KAAK,CAACyJ,UAAU,EAAE;QACnDE,IAAI,EAAE,KAAK;QACXZ,IAAI,EAAER,WAAW;QACjBqB,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE,IAAI;cACjBC,KAAK,EAAE;gBACLC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAE;kBACzB,OAAO5L,cAAc,CAAC4L,KAAK,CAAC;gBAC9B;cACF;YACF;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE;cACPC,SAAS,EAAE;gBACTxB,KAAK,EAAE,SAAAA,CAAUyB,OAAO,EAAE;kBACxB,UAAAhB,MAAA,CAAUgB,OAAO,CAACC,OAAO,CAAC1B,KAAK,QAAAS,MAAA,CAAKhL,cAAc,CAACgM,OAAO,CAACE,GAAG,CAAC;gBACjE;cACF;YACF;UACF;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,QAAQ,GAAGjH,aAAa,CAACsE,OAAO,CAAC2B,UAAU,CAAC,IAAI,CAAC;MACvD/F,kBAAkB,CAACoE,OAAO,GAAG,IAAI/H,KAAK,CAAC0K,QAAQ,EAAE;QAC/Cf,IAAI,EAAE,KAAK;QACXZ,IAAI,EAAEM,SAAS;QACfO,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE,IAAI;cACjBC,KAAK,EAAE;gBACLC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAE;kBACzB,OAAO5L,cAAc,CAAC4L,KAAK,CAAC;gBAC9B;cACF;YACF;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE;cACPC,SAAS,EAAE;gBACTxB,KAAK,EAAE,SAAAA,CAAUyB,OAAO,EAAE;kBACxB,UAAAhB,MAAA,CAAUgB,OAAO,CAACC,OAAO,CAAC1B,KAAK,QAAAS,MAAA,CAAKhL,cAAc,CAACgM,OAAO,CAACE,GAAG,CAAC;gBACjE;cACF;YACF;UACF;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACvH,QAAQ,EAAErC,iBAAiB,CAAC,CAAC;EAEjCvD,SAAS,CAAC,MAAM;IACdoD,QAAQ,CAAC/B,WAAW,CAAC,EAAE,CAAC,CAAC;IACzB+B,QAAQ,CAAChC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC,EAAE,CAACgC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiK,iBAAiB,GAAIC,SAAS,IAAK;IACvCjI,gBAAgB,CAACiI,SAAS,CAACC,EAAE,CAAC;IAC9BhI,kBAAkB,CAAC+H,SAAS,CAACE,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtCpI,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACA,MAAMmI,gBAAgB,GAAIC,QAAQ,IAAK;IACrClI,iBAAiB,CAACkI,QAAQ,CAACJ,EAAE,CAAC;IAC9B5H,iBAAiB,CAACgI,QAAQ,CAACC,SAAS,GAAG,GAAG,GAAGD,QAAQ,CAACE,QAAQ,CAAC;EACjE,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCrI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAID;EACA,MAAMoI,YAAY,GAAGA,CAAA,KAAM;IACzB3K,QAAQ,CACN1C,oBAAoB,CAAC;MACnBsN,MAAM,EAAE9J,UAAU;MAClBL,IAAI,EAAE,CAAC;MAAE;MACTG,QAAQ;MACRiK,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAEpJ,YAAY;MACpBqJ,KAAK,EAAEvJ,WAAW;MAClBwJ,OAAO,EAAEpJ,aAAa;MACtBkH,SAAS,EAAEhH,WAAW;MACtBmJ,OAAO,EAAEjJ;IACX,CAAC,CACH,CAAC;EACH,CAAC;;EAED;EACA,MAAMkJ,sBAAsB,GAAG,MAAOC,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACnH,SAAS,EAAE;MACdmH,MAAM,CAACC,MAAM,GAAG,wBAAwB;IAC1C;;IAEA;IACA,IAAI,CAAChH,QAAQ,EAAE;MACb+G,MAAM,CAACN,KAAK,GAAG,2BAA2B;IAC5C,CAAC,MAAM,IAAI,CAAC,eAAe,CAACQ,IAAI,CAACjH,QAAQ,CAAC,EAAE;MAC1C+G,MAAM,CAACN,KAAK,GAAG,8BAA8B;IAC/C;;IAEA;IACA,IAAI,CAACvF,oBAAoB,IAAI,CAAChB,iBAAiB,EAAE;MAC/C6G,MAAM,CAACG,cAAc,GAAG,uDAAuD;IACjF,CAAC,MAAM,IAAIhH,iBAAiB,KAAKiH,KAAK,CAACjH,iBAAiB,CAAC,IAAIkH,MAAM,CAAClH,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3F6G,MAAM,CAACG,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAI5G,aAAa,KAAK6G,KAAK,CAAC7G,aAAa,CAAC,IAAI8G,MAAM,CAAC9G,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;MACxEyG,MAAM,CAACM,UAAU,GAAG,0BAA0B;IAChD;;IAEA;IACA,IAAI,CAACzG,UAAU,EAAE;MACfmG,MAAM,CAACO,OAAO,GAAG,8BAA8B;IACjD;;IAEA;IACA,IAAI,CAACxG,SAAS,EAAE;MACdiG,MAAM,CAACP,MAAM,GAAG,0BAA0B;IAC5C;;IAEA;IACA,IAAIe,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClChI,aAAa,CAACsH,MAAM,CAAC;MACrB;IACF;IAEApH,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM+H,WAAW,GAAG;QAClBV,MAAM,EAAEpH,SAAS;QACjB6G,KAAK,EAAEzG,QAAQ;QACfwG,MAAM,EAAE1F,SAAS;QACjB6G,IAAI,EAAE3G,OAAO;QACbsG,OAAO,EAAE1G;MACX,CAAC;;MAED;MACA,IAAI,CAACM,oBAAoB,IAAIhB,iBAAiB,EAAE;QAC9CwH,WAAW,CAACR,cAAc,GAAGE,MAAM,CAAClH,iBAAiB,CAAC;MACxD;;MAEA;MACA,IAAII,aAAa,EAAE;QACjBoH,WAAW,CAACL,UAAU,GAAGD,MAAM,CAAC9G,aAAa,CAAC;MAChD;;MAEA;MACA,IAAII,cAAc,EAAE;QAClBgH,WAAW,CAACE,WAAW,GAAGlH,cAAc;MAC1C;;MAEA;MACA,MAAMhF,QAAQ,CAACxC,oBAAoB,CAACwO,WAAW,CAAC,CAAC;;MAEjD;MACAG,eAAe,CAAC,CAAC;MACjBnM,QAAQ,CAAC1C,oBAAoB,CAAC;QAC5BmD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRgK,MAAM,EAAE9J,UAAU;QAClB+J,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA1G,YAAY,CAAC,EAAE,CAAC;MAChBE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,WAAW,CAAC,EAAE,CAAC;MACfE,oBAAoB,CAAC,EAAE,CAAC;MACxBE,6BAA6B,CAAC,EAAE,CAAC;MACjCE,gBAAgB,CAAC,EAAE,CAAC;MACpBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,aAAa,CAAC,EAAE,CAAC;MACjBE,YAAY,CAAC,EAAE,CAAC;MAChBE,UAAU,CAAC,EAAE,CAAC;MACdE,uBAAuB,CAAC,KAAK,CAAC;IAEhC,CAAC,CAAC,OAAO2G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDrI,aAAa,CAAC;QAAEuI,MAAM,EAAE;MAAgC,CAAC,CAAC;IAC5D,CAAC,SAAS;MACRrI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMsI,oBAAoB,GAAIC,IAAI,IAAK;IACrC/J,WAAW,CAAC+J,IAAI,CAAC;;IAEjB;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;MAC9B,MAAMC,WAAW,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;MAC7C,MAAMC,YAAY,GAAGJ,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;;MAE/C;MACA,IAAI,CAACpK,UAAU,EAAE;QACf,MAAMqK,cAAc,GAAGF,YAAY,GAAG,EAAE,OAAAhE,MAAA,CAAOgE,YAAY,OAAAhE,MAAA,CAAQgE,YAAY,CAAE;QACjFlK,aAAa,IAAAkG,MAAA,CAAI8D,WAAW,GAAG,CAAC,OAAA9D,MAAA,CAAIkE,cAAc,CAAE,CAAC;MACvD;;MAEA;MACA,IAAI,CAACnK,QAAQ,EAAE;QACb,MAAMmK,cAAc,GAAGF,YAAY,GAAG,EAAE,OAAAhE,MAAA,CAAOgE,YAAY,OAAAhE,MAAA,CAAQgE,YAAY,CAAE;QACjFhK,WAAW,IAAAgG,MAAA,CAAI8D,WAAW,OAAA9D,MAAA,CAAIkE,cAAc,CAAE,CAAC;MACjD;;MAEA;MACA/M,QAAQ,CAACtC,sBAAsB,CAAC;QAC9BgF,UAAU,EAAEA,UAAU,OAAAmG,MAAA,CAAO8D,WAAW,GAAG,CAAC,OAAA9D,MAAA,CAAIgE,YAAY,GAAG,EAAE,OAAAhE,MAAA,CAAOgE,YAAY,IAAKA,YAAY,CAAE;QACvGjK,QAAQ,EAAEA,QAAQ,OAAAiG,MAAA,CAAO8D,WAAW,OAAA9D,MAAA,CAAIgE,YAAY,GAAG,EAAE,OAAAhE,MAAA,CAAOgE,YAAY,IAAKA,YAAY,CAAE;QAC/F/D,SAAS,EAAEhH;MACb,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAEDlF,SAAS,CAAC,MAAM;IACdoD,QAAQ,CACN1C,oBAAoB,CAAC;MACnBsN,MAAM,EAAE9J,UAAU;MAClBL,IAAI,EAAEA,IAAI;MAAE;MACZG,QAAQ;MACRiK,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAEpJ,YAAY;MACpBqJ,KAAK,EAAEvJ,WAAW;MAClBwJ,OAAO,EAAEpJ,aAAa;MACtBkH,SAAS,EAAEhH;IACb,CAAC,CACH,CAAC;EACH,CAAC,EAAE,CAAC9B,QAAQ,EAAES,IAAI,EAAEG,QAAQ,CAAC,CAAC;EAE9B,MAAMoM,UAAU,GAAG,MAAO7C,EAAE,IAAK;IAC/BlG,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMgJ,QAAQ,GAAG,MAAMjN,QAAQ,CAACpC,4BAA4B,CAACuM,EAAE,CAAC,CAAC,CAAC+C,MAAM,CAAC,CAAC;MAC1E,MAAMC,OAAO,GAAGF,QAAQ,CAAC5E,IAAI;;MAE7B;MACA1C,SAAS,CAACwE,EAAE,CAAC;MACbtE,qBAAqB,CAACsH,OAAO,CAAC3B,cAAc,IAAI,EAAE,CAAC;MACnDzF,8BAA8B,CAACoH,OAAO,CAAC3B,cAAc,GAAG1N,oBAAoB,CAACqP,OAAO,CAAC3B,cAAc,CAAC,GAAG,EAAE,CAAC;MAC1GvF,iBAAiB,CAACkH,OAAO,CAACxB,UAAU,IAAI,EAAE,CAAC;MAC3CxF,0BAA0B,CAACgH,OAAO,CAACxB,UAAU,GAAG7N,oBAAoB,CAACqP,OAAO,CAACxB,UAAU,CAAC,GAAG,EAAE,CAAC;MAC9FtF,kBAAkB,CAAC8G,OAAO,CAACjB,WAAW,GAAG,IAAIQ,IAAI,CAACS,OAAO,CAACjB,WAAW,CAAC,CAACkB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;MACxG9G,aAAa,CAAC4G,OAAO,CAACrC,MAAM,IAAI,EAAE,CAAC;MACnCrE,WAAW,CAAC0G,OAAO,CAAClB,IAAI,IAAI,EAAE,CAAC;MAC/BtF,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACApF,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAO+K,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRnI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAID,MAAMqJ,UAAU,GAAG,MAAAA,CAAOnD,EAAE,EAAEmB,MAAM,EAAEP,KAAK,KAAK;IAC9C9D,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAMjH,QAAQ,CAACpC,4BAA4B,CAACuM,EAAE,CAAC,CAAC;;MAEhD;;MAEA;MACA;MACA;MACA;;MAEA;MACA5I,iBAAiB,CAAC,MAAM,CAAC;MACzBF,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAO+K,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRnF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACnG,mBAAmB,CAACC,OAAO,EAAE;IAElCF,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI;MACF;MACA,MAAMqG,aAAa,GAAGpG,mBAAmB,CAACC,OAAO,CAACoG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;;MAE7E;MACArG,mBAAmB,CAACC,OAAO,CAACqG,YAAY,CACtC,OAAO,KAAA7E,MAAA,CACJ2E,aAAa,2HAIlB,CAAC;MAED,MAAMG,OAAO,GAAGvG,mBAAmB,CAACC,OAAO;MAC3C,MAAMuG,MAAM,GAAG,MAAM1Q,WAAW,CAACyQ,OAAO,EAAE;QACxCE,KAAK,EAAE,CAAC;QAAE;QACVC,OAAO,EAAE,IAAI;QAAE;QACfC,OAAO,EAAE,KAAK;QACdxF,eAAe,EAAE,SAAS;QAC1ByF,MAAM,EAAE;UACNC,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE;QACR;MACF,CAAC,CAAC;;MAEF;MACAhH,mBAAmB,CAACC,OAAO,CAACqG,YAAY,CAAC,OAAO,EAAEF,aAAa,CAAC;;MAEhE;MACA,MAAMa,QAAQ,GAAGT,MAAM,CAACU,SAAS,CAAC,WAAW,CAAC;;MAE9C;MACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxC,MAAMC,QAAQ,GAAGrO,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEsO,IAAI,cAAA9F,MAAA,CACtBxI,cAAc,CAACsO,IAAI,CAAClE,QAAQ,OAAA5B,MAAA,CAAIxI,cAAc,CAACsO,IAAI,CAACnE,SAAS,OAAA3B,MAAA,CAAIxI,cAAc,CAAC6H,cAAc,uBAAAW,MAAA,CAC9F,IAAI6D,IAAI,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC,SAAM;MAE7CmB,IAAI,CAACK,QAAQ,GAAGF,QAAQ;MACxBH,IAAI,CAACM,IAAI,GAAGR,QAAQ;MACpBE,IAAI,CAACO,KAAK,CAAC,CAAC;IACd,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRjF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM4H,SAAS,GAAGA,CAAA,KAAM;IACtB;IACA5K,YAAY,CAAC,EAAE,CAAC;IAChBE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,WAAW,CAAC,EAAE,CAAC;IACfE,oBAAoB,CAAC,EAAE,CAAC;IACxBE,6BAA6B,CAAC,EAAE,CAAC;IACjCE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,yBAAyB,CAAC,EAAE,CAAC;IAC7BE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdE,uBAAuB,CAAC,KAAK,CAAC;IAC9B1B,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAMiL,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC;IACxB,MAAMuC,aAAa,GAAGD,KAAK,CAAC5B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvDlI,aAAa,CAAC8J,aAAa,CAAC;;IAE5B;IACA,MAAMC,IAAI,GAAGF,KAAK,CAACpC,WAAW,CAAC,CAAC;IAChC,MAAM7B,KAAK,GAAGiE,KAAK,CAAClC,QAAQ,CAAC,CAAC,GAAG,CAAC;IAClC,MAAMC,cAAc,MAAAlE,MAAA,CAAMqG,IAAI,OAAArG,MAAA,CAAIkC,KAAK,GAAG,EAAE,OAAAlC,MAAA,CAAOkC,KAAK,IAAKA,KAAK,CAAE;IACpExG,WAAW,CAACwI,cAAc,CAAC;;IAE3B;IACAxL,iBAAiB,CAAC,KAAK,CAAC;IACxBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM8N,cAAc,GAAGA,CAAA,KAAM;IAC3B5N,iBAAiB,CAAC,OAAO,CAAC;IAC1BF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+N,wBAAwB,GAAGA,CAAA,KAAM;IACrC7N,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMgO,mBAAmB,GAAGA,CAAA,KAAM;IAChC9N,iBAAiB,CAAC,cAAc,CAAC;IACjCF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM8K,eAAe,GAAGA,CAAA,KAAM;IAC5B9K,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,EAAE,CAAC;IACrB;IACA4B,aAAa,CAAC,EAAE,CAAC;IACjBE,cAAc,CAAC,EAAE,CAAC;IAClBE,uBAAuB,CAAC,EAAE,CAAC;IAC3BE,eAAe,CAAC,EAAE,CAAC;IACnBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChB8B,SAAS,CAAC,IAAI,CAAC;IACfE,qBAAqB,CAAC,EAAE,CAAC;IACzBE,8BAA8B,CAAC,EAAE,CAAC;IAClCE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,0BAA0B,CAAC,EAAE,CAAC;IAC9BE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,aAAa,CAAC,EAAE,CAAC;IACjBE,WAAW,CAAC,EAAE,CAAC;IACfE,oBAAoB,CAAC,KAAK,CAAC;IAC3B;IACAE,cAAc,CAAC,IAAI,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BhD,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,KAAK,CAAC;IACtBgD,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMqI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMjE,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAIrF,cAAc,KAAKyF,KAAK,CAACzF,cAAc,CAAC,IAAI0F,MAAM,CAAC1F,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3EqF,MAAM,CAACrF,cAAc,GAAG,0BAA0B;IACpD;;IAEA;IACA,IAAIJ,kBAAkB,KAAK6F,KAAK,CAAC7F,kBAAkB,CAAC,IAAI8F,MAAM,CAAC9F,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;MACvFyF,MAAM,CAACzF,kBAAkB,GAAG,0BAA0B;IACxD;;IAEA;IACA,IAAI,CAACU,UAAU,EAAE;MACf+E,MAAM,CAAC/E,UAAU,GAAG,gCAAgC;IACtD;IAEA,OAAO+E,MAAM;EACf,CAAC;;EAED;EACA,MAAMkE,0BAA0B,GAAG,MAAOpE,CAAC,IAAK;IAC9CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMoE,gBAAgB,GAAGtJ,uBAAuB,GAAGnI,kBAAkB,CAACmI,uBAAuB,CAAC,GAAGF,cAAc;IAC/G,MAAMyJ,oBAAoB,GAAG3J,2BAA2B,GAAG/H,kBAAkB,CAAC+H,2BAA2B,CAAC,GAAGF,kBAAkB;;IAE/H;IACA,IAAIM,uBAAuB,EAAE;MAC3BD,iBAAiB,CAACuJ,gBAAgB,CAAC;IACrC;IAEA,IAAI1J,2BAA2B,IAAI,CAACY,iBAAiB,EAAE;MACrDb,qBAAqB,CAAC4J,oBAAoB,CAAC;IAC7C;;IAEA;IACA,MAAMpE,MAAM,GAAGiE,gBAAgB,CAAC,CAAC;IACjC,IAAIzD,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClChI,aAAa,CAACsH,MAAM,CAAC;MACrB;IACF;IAEApH,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM+H,WAAW,GAAG;QAClBL,UAAU,EAAE6D,gBAAgB,GAAG9D,MAAM,CAAC8D,gBAAgB,CAAC,GAAG,CAAC;QAC3D1E,MAAM,EAAExE,UAAU;QAClB2F,IAAI,EAAEzF,QAAQ;QACdE,iBAAiB,EAAEA;MACrB,CAAC;;MAED;MACA,IAAI+I,oBAAoB,IAAI,CAAC/I,iBAAiB,EAAE;QAC9CsF,WAAW,CAACR,cAAc,GAAGE,MAAM,CAAC+D,oBAAoB,CAAC;MAC3D;;MAEA;MACA,IAAIrJ,eAAe,EAAE;QACnB4F,WAAW,CAACE,WAAW,GAAG9F,eAAe;MAC3C;;MAEA;MACA,MAAMpG,QAAQ,CAACrC,oBAAoB,CAAC;QAClCwM,EAAE,EAAEzE,MAAM;QACVsG;MACF,CAAC,CAAC,CAAC;;MAEH;MACAG,eAAe,CAAC,CAAC;MACjBnM,QAAQ,CAAC1C,oBAAoB,CAAC;QAC5BmD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRgK,MAAM,EAAE9J,UAAU;QAClB+J,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAEpJ,YAAY;QACpBqJ,KAAK,EAAEvJ,WAAW;QAClBwJ,OAAO,EAAEpJ,aAAa;QACtBkH,SAAS,EAAEhH;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOsK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDrI,aAAa,CAAC;QAAEuI,MAAM,EAAE;MAAqC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRrI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMyL,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMrE,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACnI,UAAU,EAAE;MACfmI,MAAM,CAACnI,UAAU,GAAG,2BAA2B;IACjD,CAAC,MAAM,IAAI,CAAC,eAAe,CAACqI,IAAI,CAACrI,UAAU,CAAC,EAAE;MAC5CmI,MAAM,CAACnI,UAAU,GAAG,8BAA8B;IACpD;;IAEA;IACA,IAAIE,WAAW,KAAKqI,KAAK,CAACrI,WAAW,CAAC,IAAIsI,MAAM,CAACtI,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;MAClEiI,MAAM,CAACjI,WAAW,GAAG,0BAA0B;IACjD;;IAEA;IACA,IAAI,CAACM,UAAU,EAAE;MACf2H,MAAM,CAAC3H,UAAU,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,YAAY,EAAE;MACjB6H,MAAM,CAAC7H,YAAY,GAAG,oCAAoC;IAC5D;IAEA,OAAO6H,MAAM;EACf,CAAC;;EAED;EACA,MAAMsE,wBAAwB,GAAG,MAAOxE,CAAC,IAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMwE,iBAAiB,GAAGtM,oBAAoB,GAAGvF,kBAAkB,CAACuF,oBAAoB,CAAC,GAAGF,WAAW;;IAEvG;IACA,IAAIE,oBAAoB,EAAE;MACxBD,cAAc,CAACuM,iBAAiB,CAAC;IACnC;;IAEA;IACA,MAAMvE,MAAM,GAAGqE,wBAAwB,CAAC,CAAC;IACzC,IAAI7D,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,GAAG,CAAC,EAAE;MAClChI,aAAa,CAACsH,MAAM,CAAC;MACrB;IACF;IAEApH,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM4L,SAAS,GAAG;QAChB9E,KAAK,EAAE7H,UAAU;QACjB0I,OAAO,EAAEpI,YAAY;QACrBE,UAAU;QACVuI,IAAI,EAAErI;MACR,CAAC;;MAED;MACA,IAAIgM,iBAAiB,EAAE;QACrBC,SAAS,CAACrE,cAAc,GAAGE,MAAM,CAACkE,iBAAiB,CAAC;MACtD;;MAEA;MACA,MAAM5P,QAAQ,CAACvC,0BAA0B,CAACoS,SAAS,CAAC,CAAC;;MAErD;MACA1D,eAAe,CAAC,CAAC;MACjBnM,QAAQ,CAAC1C,oBAAoB,CAAC;QAC5BmD,IAAI,EAAEA,IAAI;QACVG,QAAQ;QACRgK,MAAM,EAAE9J,UAAU;QAClB+J,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DrI,aAAa,CAAC;QAAEuI,MAAM,EAAE;MAA0C,CAAC,CAAC;IACtE,CAAC,SAAS;MACRrI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM6L,YAAY,GAAI3F,EAAE,IAAK;IAC3BhJ,kBAAkB,CAACgJ,EAAE,CAAC;IACtBlJ,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM8O,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC/P,QAAQ,CAACzC,oBAAoB,CAAC2D,eAAe,CAAC,CAAC;IAC/CD,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM+O,YAAY,GAAGA,CAAA,KAAM;IACzB/O,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8O,cAAc,GAAInF,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,oBACEtL,OAAA;UAAM0Q,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAE7E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,QAAQ;QACX,oBACE9Q,OAAA;UAAM0Q,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEzE;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACE9Q,OAAA;UAAM0Q,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EAAC;QAE/E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX,KAAK,SAAS;QACZ,oBACE9Q,OAAA;UAAM0Q,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAE3E;UAAAzB,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX;QACE,oBACE9Q,OAAA;UAAM0Q,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EACvErF;QAAM;UAAA4D,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEb;EACF,CAAC;EAED,IAAIlQ,OAAO,EAAE;IACX,oBAAOZ,OAAA,CAACnB,cAAc;MAAAqQ,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,MAAMC,sBAAsB,GAAGC,IAAA,IAA6B;IAAA,IAA5B;MAAEC,IAAI;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAAH,IAAA;IACrD,oBACEhR,OAAA;MACEmR,OAAO,EAAEA,OAAQ;MACjBT,SAAS,EAAC,2IAA2I;MAAAC,QAAA,GAEpJM,IAAI,eACLjR,OAAA;QAAA2Q,QAAA,EAAOO;MAAI;QAAAhC,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEb,CAAC;EAED,MAAMM,OAAO,gBACXpR,OAAA;IAAK,wBAAgB;IAAC0Q,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC3Q,OAAA,CAACd,IAAI;MAACmS,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CACN;EAED,MAAMQ,SAAS,gBACbtR,OAAA;IAAK,wBAAgB;IAAC0Q,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC3Q,OAAA,CAACZ,QAAQ;MAACiS,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMS,YAAY,gBAChBvR,OAAA;IAAK,wBAAgB;IAAC0Q,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC3Q,OAAA,CAACX,QAAQ;MAACgS,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CACN;EAED,MAAMU,SAAS,gBACbxR,OAAA;IAAK,wBAAgB;IAAC0Q,SAAS,EAAC,UAAU;IAAAC,QAAA,eACxC3Q,OAAA,CAACV,KAAK;MAAC+R,IAAI,EAAE;IAAG;MAAAnC,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CACN;EAED,oBACE9Q,OAAA,CAACJ,WAAW;IAAA+Q,QAAA,gBACV3Q,OAAA;MAAK0Q,SAAS,EAAC,oEAAoE;MAAAC,QAAA,EAAC;IAEpF;MAAAzB,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAEN9Q,OAAA;MAAK0Q,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpF3Q,OAAA;QAAK0Q,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrB3Q,OAAA;UAAK0Q,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD3Q,OAAA;YAAK0Q,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC3Q,OAAA;cACEyR,KAAK,EAAC,4BAA4B;cAClCC,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXnB,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eAEnE3Q,OAAA;gBACE8R,CAAC,EAAC,mPAAmP;gBACrPC,MAAM,EAAC,SAAS;gBAChBC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC;cAAO;gBAAA/C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9Q,OAAA;cACEyJ,IAAI,EAAC,MAAM;cACXyI,WAAW,EAAC,kEAAwC;cACpDjI,KAAK,EAAE3I,UAAW;cAClB6Q,QAAQ,EAAGxG,CAAC,IAAKpK,aAAa,CAACoK,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;cAC/CyG,SAAS,EAAC;YAAsI;cAAAxB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9Q,OAAA;YAAK0Q,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB3Q,OAAA;cACE0Q,SAAS,EAAC,oDAAoD;cAC9DzG,KAAK,EAAEjI,WAAY;cACnBmQ,QAAQ,EAAGxG,CAAC,IAAK1J,cAAc,CAAC0J,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;cAAA0G,QAAA,gBAEhD3Q,OAAA;gBAAQiK,KAAK,EAAC,EAAE;gBAAA0G,QAAA,EAAC;cAAK;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC9BuB,KAAK,CAACC,IAAI,CAAC;gBAAE/F,MAAM,EAAE;cAAG,CAAC,EAAE,CAACgG,CAAC,EAAEC,CAAC,KAAK;gBACpC,MAAMjH,KAAK,GAAGiH,CAAC,GAAG,CAAC;gBACnB,MAAM9C,IAAI,GAAG,IAAIxC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;gBACrC,MAAMqF,QAAQ,GAAGlH,KAAK,GAAG,EAAE,OAAAlC,MAAA,CAAOkC,KAAK,OAAAlC,MAAA,CAAQkC,KAAK,CAAE;gBACtD,oBACEvL,OAAA;kBAAoBiK,KAAK,KAAAZ,MAAA,CAAKqG,IAAI,OAAArG,MAAA,CAAIoJ,QAAQ,CAAG;kBAAA9B,QAAA,cAAAtH,MAAA,CACrCkC,KAAK,OAAAlC,MAAA,CAAIqG,IAAI;gBAAA,GADZnE,KAAK;kBAAA2D,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CAAC;cAEb,CAAC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9Q,OAAA;YAAK0Q,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB3Q,OAAA;cACE0Q,SAAS,EAAC,oDAAoD;cAC9DzG,KAAK,EAAE/H,YAAa;cACpBiQ,QAAQ,EAAGxG,CAAC,IAAKxJ,eAAe,CAACwJ,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;cAAA0G,QAAA,gBAEjD3Q,OAAA;gBAAQiK,KAAK,EAAC,EAAE;gBAAA0G,QAAA,EAAC;cAAU;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC9Q,OAAA;gBAAQiK,KAAK,EAAC,MAAM;gBAAA0G,QAAA,EAAC;cAAa;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C9Q,OAAA;gBAAQiK,KAAK,EAAC,QAAQ;gBAAA0G,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C9Q,OAAA;gBAAQiK,KAAK,EAAC,SAAS;gBAAA0G,QAAA,EAAC;cAAmB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9Q,OAAA;YAAK0Q,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB3Q,OAAA;cACE0Q,SAAS,EAAC,oDAAoD;cAC9DzG,KAAK,EAAE7H,aAAc;cACrB+P,QAAQ,EAAGxG,CAAC,IAAKtJ,gBAAgB,CAACsJ,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;cAAA0G,QAAA,gBAElD3Q,OAAA;gBAAQiK,KAAK,EAAC,EAAE;gBAAA0G,QAAA,EAAC;cAAc;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC9Q,OAAA;gBAAQiK,KAAK,EAAC,MAAM;gBAAA0G,QAAA,EAAC;cAAU;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC9Q,OAAA;gBAAQiK,KAAK,EAAC,OAAO;gBAAA0G,QAAA,EAAC;cAAY;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9Q,OAAA;YAAK0Q,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB3Q,OAAA;cACE0Q,SAAS,EAAC,oDAAoD;cAC9DzG,KAAK,EAAE3H,WAAY;cACnB6P,QAAQ,EAAGxG,CAAC,IAAKpJ,cAAc,CAACoJ,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;cAAA0G,QAAA,gBAEhD3Q,OAAA;gBAAQiK,KAAK,EAAC,EAAE;gBAAA0G,QAAA,EAAC;cAAI;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B9Q,OAAA;gBAAQiK,KAAK,EAAC,IAAI;gBAAA0G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC9Q,OAAA;gBAAQiK,KAAK,EAAC,IAAI;gBAAA0G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC9Q,OAAA;gBAAQiK,KAAK,EAAC,IAAI;gBAAA0G,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN9Q,OAAA;YAAK0Q,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB3Q,OAAA,CAACpC,gBAAgB;cACfqM,KAAK,EAAEvH,eAAgB;cACvBgQ,eAAe,EAAElQ,aAAc;cAC/B2P,QAAQ,EAAExP,kBAAmB;cAC7BgQ,QAAQ,EAAElI,iBAAkB;cAC5BmI,OAAO,EAAE/H,yBAA0B;cACnCqH,WAAW,EAAC;YAAqB;cAAAhD,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9Q,OAAA;YACEmR,OAAO,EAAEhG,YAAa;YACtBuF,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3F3Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAACmB,IAAI,EAAC,MAAM;cAACE,MAAM,EAAC,cAAc;cAACH,OAAO,EAAC,WAAW;cAACH,KAAK,EAAC,4BAA4B;cAAAd,QAAA,eACpH3Q,OAAA;gBAAMgS,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACY,WAAW,EAAC,GAAG;gBAACf,CAAC,EAAC;cAA6C;gBAAA5C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH,CAAC,oBAER;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9Q,OAAA;YAAK0Q,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACrE3Q,OAAA;cACE0Q,SAAS,iCAAArH,MAAA,CAAiCrG,QAAQ,KAAK,OAAO,GAAG,wBAAwB,GAAG,aAAa,CAAG;cAC5GmO,OAAO,EAAEA,CAAA,KAAMpE,oBAAoB,CAAC,OAAO,CAAE;cAAA4D,QAAA,gBAE7C3Q,OAAA,CAACN,IAAI;gBAAC2R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9Q,OAAA;cACE0Q,SAAS,iCAAArH,MAAA,CAAiCrG,QAAQ,KAAK,YAAY,GAAG,wBAAwB,GAAG,aAAa,CAAG;cACjHmO,OAAO,EAAEA,CAAA,KAAMpE,oBAAoB,CAAC,YAAY,CAAE;cAAA4D,QAAA,gBAElD3Q,OAAA,CAACP,SAAS;gBAAC4R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAE1C;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGH,CAAC,eAEN9Q,OAAA;UAAK0Q,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD3Q,OAAA,CAAC+Q,sBAAsB;YAACE,IAAI,EAAEG,OAAQ;YAACF,IAAI,EAAE,iBAAkB;YAACC,OAAO,EAAE5B;UAAU;YAAAL,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtF9Q,OAAA,CAAC+Q,sBAAsB;YAACE,IAAI,EAAEM,YAAa;YAACL,IAAI,EAAE,uBAAwB;YAACC,OAAO,EAAEvB;UAAyB;YAAAV,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChH9Q,OAAA,CAAC+Q,sBAAsB;YAACE,IAAI,EAAEO,SAAU;YAACN,IAAI,EAAE,sBAAuB;YAACC,OAAO,EAAEtB;UAAoB;YAAAX,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,oCAGF,eAAA9Q,OAAA;MAAK0Q,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D3Q,OAAA;QAAG0Q,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAzB,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,SAEN,eAAA9Q,OAAA;MAAK0Q,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B3Q,OAAA;QAAO0Q,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACzE3Q,OAAA;UAAO0Q,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC5B3Q,OAAA;YAAA2Q,QAAA,gBACE3Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAG;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAM;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAY;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAK;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAc;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAU;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD9Q,OAAA;cAAI0Q,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAQ;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR9Q,OAAA;UAAA2Q,QAAA,EACGjQ,eAAe,CAAC8H,GAAG,CAAC,CAACmF,OAAO,EAAEmF,KAAK;YAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;YAAA,oBAClClT,OAAA;cAEE0Q,SAAS,EAAEoC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,UAAW;cAAAnC,QAAA,gBAEvD3Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,CAAC1P,IAAI,GAAG,CAAC,IAAIG,QAAQ,GAAG0R,KAAK,GAAG;cAAC;gBAAA5D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,EAAAoC,aAAA,GAAApF,OAAO,CAACwB,IAAI,cAAA4D,aAAA,uBAAZA,aAAA,CAAc9H,QAAQ,IAAG,GAAG,KAAA+H,cAAA,GAAGrF,OAAO,CAACwB,IAAI,cAAA6D,cAAA,uBAAZA,cAAA,CAAchI,SAAS,KAAI;cAAK;gBAAAkE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAsC,cAAA,GAAAtF,OAAO,CAACwB,IAAI,cAAA8D,cAAA,uBAAZA,cAAA,CAAcE,KAAK,KAAI;cAAK;gBAAAjE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7D9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE,EAAAuC,cAAA,GAAAvF,OAAO,CAACwB,IAAI,cAAA+D,cAAA,uBAAZA,cAAA,CAAcE,UAAU,KAAI;cAAK;gBAAAlE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClE9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBtS,cAAc,CAACsP,OAAO,CAAC3B,cAAc;cAAC;gBAAAkD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBtS,cAAc,CAACsP,OAAO,CAACxB,UAAU;cAAC;gBAAA+C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEhD,OAAO,CAACjF;cAAc;gBAAAwG,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvD9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBhD,OAAO,CAACjB,WAAW,GAChB,IAAIQ,IAAI,CAACS,OAAO,CAACjB,WAAW,CAAC,CAAC2G,kBAAkB,CAAC,OAAO,CAAC,GACzD;cAAiB;gBAAAnE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtB,IAAIzD,IAAI,CAACS,OAAO,CAACvB,OAAO,CAAC,CAACiH,kBAAkB,CAAC,OAAO;cAAC;gBAAAnE,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACtBF,cAAc,CAAC9C,OAAO,CAACrC,MAAM,KAAK,MAAM,GAAG,MAAM,GAAGqC,OAAO,CAAC2F,SAAS,GAAG,SAAS,GAAG3F,OAAO,CAACrC,MAAM,KAAK,SAAS,GAAG,SAAS,GAAGqC,OAAO,CAACrC,MAAM;cAAC;gBAAA4D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I,CAAC,eACL9Q,OAAA;gBAAI0Q,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB3Q,OAAA;kBAAK0Q,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B3Q,OAAA;oBACEmR,OAAO,EAAEA,CAAA,KAAMrD,UAAU,CAACH,OAAO,CAAChD,EAAE,EAAEgD,OAAO,CAAC7B,MAAM,EAAE6B,OAAO,CAACpC,KAAK,CAAE;oBACrEmF,SAAS,EAAC,mCAAmC;oBAC7C6C,KAAK,EAAC,mBAAc;oBAAA5C,QAAA,eAEpB3Q,OAAA,CAACb,GAAG;sBAACkS,IAAI,EAAE;oBAAG;sBAAAnC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACT9Q,OAAA;oBACEmR,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAACG,OAAO,CAAChD,EAAE,CAAE;oBACtC+F,SAAS,EAAC,uCAAuC;oBACjD6C,KAAK,EAAC,qBAAW;oBAAA5C,QAAA,eAEjB3Q,OAAA,CAAChB,IAAI;sBAACqS,IAAI,EAAE;oBAAG;sBAAAnC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACT9Q,OAAA;oBACEmR,OAAO,EAAEA,CAAA,KAAMb,YAAY,CAAC3C,OAAO,CAAChD,EAAE,CAAE;oBACxC+F,SAAS,EAAC,iCAAiC;oBAC3C6C,KAAK,EAAC,QAAK;oBAAA5C,QAAA,eAEX3Q,OAAA,CAACf,KAAK;sBAACoS,IAAI,EAAE;oBAAG;sBAAAnC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArDAnD,OAAO,CAAChD,EAAE;cAAAuE,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsDb,CAAC;UAAA,CACN;QAAC;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,KAEV,EAAC9N,QAAQ,KAAK,OAAO,iBACnBhD,OAAA;MAAK0Q,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB3Q,OAAA,CAAClB,UAAU;QACT0U,WAAW,EAAEvS,IAAK;QAClBwS,YAAY,EAAGxS,IAAI,IAAKT,QAAQ,CAAC9B,cAAc,CAACuC,IAAI,CAAC,CAAE;QACvDyS,UAAU,EAAEvS,KAAM;QAClBwS,KAAK,EAAEvS;MAAS;QAAA8N,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEC,CACN,EAIAlP,cAAc,iBACb5B,OAAA;MAAK0Q,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBACpF3Q,OAAA;QAAK0Q,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7E3Q,OAAA;UAAI0Q,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAClC7O,cAAc,KAAK,KAAK,IAAI,yBAAyB,EACrDA,cAAc,KAAK,cAAc,IAAI,uBAAuB,EAC5DA,cAAc,KAAK,cAAc,IAAI,sBAAsB,EAC3DA,cAAc,KAAK,MAAM,IAAI,8BAA8B,EAC3DA,cAAc,KAAK,MAAM,IAAI,6BAA6B;QAAA;UAAAoN,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACL9Q,OAAA;UACEmR,OAAO,EAAExE,eAAgB;UACzB+D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C3Q,OAAA,CAACT,CAAC;YAAC8R,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9Q,OAAA;QAAA2Q,QAAA,GACG7O,cAAc,KAAK,KAAK,iBACvB9B,OAAA;UAAK0Q,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB3Q,OAAA;YAAG0Q,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+C;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvE9Q,OAAA;YAAM0Q,SAAS,EAAC,WAAW;YAACkD,QAAQ,EAAElI,sBAAuB;YAAAiF,QAAA,gBAC3D3Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,gBAAS,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxH9Q,OAAA,CAACnC,eAAe;gBACdoM,KAAK,EAAErF,iBAAkB;gBACzBhC,cAAc,EAAE8B,SAAU;gBAC1ByN,QAAQ,EAAEtN,oBAAqB;gBAC/B8N,QAAQ,EAAGxD,IAAI,IAAK;kBAClBxK,YAAY,CAACwK,IAAI,CAACxE,EAAE,CAAC;kBACrB9F,oBAAoB,IAAAwE,MAAA,CAAI8F,IAAI,CAAClE,QAAQ,OAAA5B,MAAA,CAAI8F,IAAI,CAACnE,SAAS,CAAE,CAAC;gBAC5D,CAAE;gBACF4H,OAAO,EAAEA,CAAA,KAAM;kBACbjO,YAAY,CAAC,EAAE,CAAC;kBAChBE,oBAAoB,CAAC,EAAE,CAAC;gBAC1B,CAAE;gBACFqN,WAAW,EAAC,mCAAsB;gBAClC2B,IAAI,EAAC;cAAS;gBAAA3E,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDxM,UAAU,CAACwH,MAAM,iBAChB9L,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACwH,MAAM;cAAA;gBAAAoD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH9Q,OAAA;gBACEyJ,IAAI,EAAC,OAAO;gBACZiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACiH,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC3GtB,KAAK,EAAEnF,QAAS;gBAChBqN,QAAQ,EAAGxG,CAAC,IAAK5G,WAAW,CAAC4G,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACDxM,UAAU,CAACiH,KAAK,iBACfvL,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACiH,KAAK;cAAA;gBAAA2D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAA3Q,OAAA;kBAAK0Q,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC3Q,OAAA;oBACEyJ,IAAI,EAAC,UAAU;oBACfkB,EAAE,EAAC,sBAAsB;oBACzBmJ,OAAO,EAAE9N,oBAAqB;oBAC9BmM,QAAQ,EAAGxG,CAAC,IAAK1F,uBAAuB,CAAC0F,CAAC,CAACyG,MAAM,CAAC0B,OAAO,CAAE;oBAC3DpD,SAAS,EAAC;kBAAM;oBAAAxB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACF9Q,OAAA;oBAAO+T,OAAO,EAAC,sBAAsB;oBAACrD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAExE;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACR9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAAC0H,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,kBAAA3C,MAAA,CAAerD,oBAAoB,GAAG,aAAa,GAAG,EAAE,CAAG;gBACjKkM,WAAW,EAAC,mDAAuB;gBACnCjI,KAAK,EAAE/E,0BAA2B;gBAClCiN,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM1B,KAAK,GAAG0B,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAC+J,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC/H,KAAK,CAAChC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjChF,oBAAoB,CAACgF,KAAK,CAAC;oBAC3B9E,6BAA6B,CAAC8E,KAAK,GAAG3L,oBAAoB,CAAC2V,QAAQ,CAAChK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACvF;gBACF,CAAE;gBACFiK,QAAQ,EAAElO;cAAqB;gBAAAkJ,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACDxM,UAAU,CAAC0H,cAAc,iBACxBhM,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAAC0H,cAAc;cAAA;gBAAAkD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAAC6H,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH+F,WAAW,EAAC,qDAAsB;gBAClCjI,KAAK,EAAE3E,sBAAuB;gBAC9B6M,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM1B,KAAK,GAAG0B,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAC+J,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC/H,KAAK,CAAChC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC5E,gBAAgB,CAAC4E,KAAK,CAAC;oBACvB1E,yBAAyB,CAAC0E,KAAK,GAAG3L,oBAAoB,CAAC2V,QAAQ,CAAChK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACnF;gBACF;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDxM,UAAU,CAAC6H,UAAU,iBACpBnM,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAAC6H,UAAU;cAAA;gBAAA+C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,EAAC,oDAAoD;gBAC9DzG,KAAK,EAAEzE,cAAe;gBACtB2M,QAAQ,EAAGxG,CAAC,IAAKlG,iBAAiB,CAACkG,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAAC8H,OAAO,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC7GnC,KAAK,EAAEvE,UAAW;gBAClByM,QAAQ,EAAGxG,CAAC,IAAKhG,aAAa,CAACgG,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,EACDxM,UAAU,CAAC8H,OAAO,iBACjBpM,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAAC8H,OAAO;cAAA;gBAAA8C,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1H9Q,OAAA;gBACE0Q,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACgH,MAAM,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAC5GrB,KAAK,EAAErE,SAAU;gBACjBuM,QAAQ,EAAGxG,CAAC,IAAK9F,YAAY,CAAC8F,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;gBAAA0G,QAAA,gBAE9C3Q,OAAA;kBAAQiK,KAAK,EAAC,EAAE;kBAAA0G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC9Q,OAAA;kBAAQiK,KAAK,EAAC,MAAM;kBAAA0G,QAAA,EAAC;gBAAa;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C9Q,OAAA;kBAAQiK,KAAK,EAAC,QAAQ;kBAAA0G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C9Q,OAAA;kBAAQiK,KAAK,EAAC,SAAS;kBAAA0G,QAAA,EAAC;gBAAmB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRxM,UAAU,CAACgH,MAAM,iBAChBtL,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACgH,MAAM;cAAA;gBAAA4D,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9Q,OAAA;gBACE0Q,SAAS,EAAC,oDAAoD;gBAC9DyD,IAAI,EAAC,GAAG;gBACRjC,WAAW,EAAC,uCAAuB;gBACnCjI,KAAK,EAAEnE,OAAQ;gBACfqM,QAAQ,EAAGxG,CAAC,IAAK5F,UAAU,CAAC4F,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLxM,UAAU,CAACwI,MAAM,iBAChB9M,OAAA;cAAK0Q,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjG3Q,OAAA,CAACR,WAAW;gBAAC6R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAA2B;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/D9Q,OAAA;gBAAA2Q,QAAA,EAAIrM,UAAU,CAACwI;cAAM;gBAAAoC,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACN,eACD9Q,OAAA;cACEyJ,IAAI,EAAC,QAAQ;cACbiH,SAAS,EAAC,uHAAuH;cACjIwD,QAAQ,EAAE1P,YAAa;cAAAmM,QAAA,EAEtBnM,YAAY,GAAG,eAAe,GAAG;YAAgB;cAAA0K,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EACAhP,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAK0Q,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB3Q,OAAA;YAAG0Q,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0D;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAElF9Q,OAAA;YAAM0Q,SAAS,EAAC,WAAW;YAACkD,QAAQ,EAAGjI,CAAC,IAAKwE,wBAAwB,CAACxE,CAAC,CAAE;YAAAgF,QAAA,gBACvE3Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAM,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrH9Q,OAAA;gBACEyJ,IAAI,EAAC,OAAO;gBACZiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACZ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHuG,KAAK,EAAEvG,UAAW;gBAClByO,QAAQ,EAAGxG,CAAC,IAAKhI,aAAa,CAACgI,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;gBAC/CmK,QAAQ;cAAA;gBAAAlF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDxM,UAAU,CAACZ,UAAU,iBACpB1D,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACZ,UAAU;cAAA;gBAAAwL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,0CAC7C,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACR9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACV,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACjHsO,WAAW,EAAC,2GAA4C;gBACxDjI,KAAK,EAAEnG,oBAAqB;gBAC5BqO,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM1B,KAAK,GAAG0B,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAC+J,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC/H,KAAK,CAAChC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjClG,uBAAuB,CAACkG,KAAK,GAAG3L,oBAAoB,CAAC2V,QAAQ,CAAChK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC/EpG,cAAc,CAACoG,KAAK,GAAGgK,QAAQ,CAAChK,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;kBAClD;gBACF;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDxM,UAAU,CAACV,WAAW,iBACrB5D,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACV,WAAW;cAAA;gBAAAsL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CACJ,eACD9Q,OAAA;gBAAG0Q,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,WAAI,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnH9Q,OAAA;gBACE0Q,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACJ,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChH+F,KAAK,EAAE/F,UAAW;gBAClBiO,QAAQ,EAAGxG,CAAC,IAAKxH,aAAa,CAACwH,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;gBAC/CmK,QAAQ;gBAAAzD,QAAA,gBAER3Q,OAAA;kBAAQiK,KAAK,EAAC,EAAE;kBAAA0G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9Q,OAAA;kBAAQiK,KAAK,EAAC,IAAI;kBAAA0G,QAAA,EAAC;gBAAM;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9Q,OAAA;kBAAQiK,KAAK,EAAC,IAAI;kBAAA0G,QAAA,EAAC;gBAAM;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC9Q,OAAA;kBAAQiK,KAAK,EAAC,IAAI;kBAAA0G,QAAA,EAAC;gBAAM;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,EACRxM,UAAU,CAACJ,UAAU,iBACpBlE,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACJ,UAAU;cAAA;gBAAAgL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yBAAe,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9H9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACN,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAClHiG,KAAK,EAAEjG,YAAa;gBACpBmO,QAAQ,EAAGxG,CAAC,IAAK1H,eAAe,CAAC0H,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;gBACjDmK,QAAQ;cAAA;gBAAAlF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDxM,UAAU,CAACN,YAAY,iBACtBhE,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACN,YAAY;cAAA;gBAAAkL,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9Q,OAAA;gBACE0Q,SAAS,EAAC,oDAAoD;gBAC9DyD,IAAI,EAAC,GAAG;gBACRjC,WAAW,EAAC,uCAAuB;gBACnCjI,KAAK,EAAE7F,SAAU;gBACjB+N,QAAQ,EAAGxG,CAAC,IAAKtH,YAAY,CAACsH,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9Q,OAAA;cACEyJ,IAAI,EAAC,QAAQ;cACbiH,SAAS,EAAC,uHAAuH;cACjIwD,QAAQ,EAAE1P,YAAa;cAAAmM,QAAA,EAEtBnM,YAAY,GAAG,eAAe,GAAG;YAAuB;cAAA0K,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAhP,cAAc,KAAK,cAAc,iBAChC9B,OAAA;UAAK0Q,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB3Q,OAAA;YAAG0Q,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkD;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE1E9Q,OAAA;YAAM0Q,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACzB3Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9Q,OAAA;gBAAQ0Q,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBACpE3Q,OAAA;kBAAQiK,KAAK,EAAC,EAAE;kBAAA0G,QAAA,EAAC;gBAAY;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC9Q,OAAA;kBAAQiK,KAAK,EAAC,GAAG;kBAAA0G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC9Q,OAAA;kBAAQiK,KAAK,EAAC,GAAG;kBAAA0G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC9Q,OAAA;kBAAQiK,KAAK,EAAC,GAAG;kBAAA0G,QAAA,EAAC;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAK;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7E9Q,OAAA;gBACEyJ,IAAI,EAAC,OAAO;gBACZiH,SAAS,EAAC;cAAoD;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxF9Q,OAAA;gBACEyJ,IAAI,EAAC,QAAQ;gBACbiH,SAAS,EAAC,oDAAoD;gBAC9DwB,WAAW,EAAC;cAAc;gBAAAhD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAc;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtF9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,EAAC;cAAoD;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9Q,OAAA;gBACE0Q,SAAS,EAAC,oDAAoD;gBAC9DyD,IAAI,EAAC,GAAG;gBACRjC,WAAW,EAAC;cAAuB;gBAAAhD,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN9Q,OAAA;cACEyJ,IAAI,EAAC,QAAQ;cACbiH,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACjF;YAED;cAAAzB,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAhP,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAK0Q,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB3Q,OAAA;YAAG0Q,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuC;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE/D9Q,OAAA;YAAM0Q,SAAS,EAAC,WAAW;YAACkD,QAAQ,EAAGjI,CAAC,IAAKoE,0BAA0B,CAACpE,CAAC,CAAE;YAAAgF,QAAA,gBACzE3Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,yCAE9D,eAAA3Q,OAAA;kBAAK0Q,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC3Q,OAAA;oBACEyJ,IAAI,EAAC,UAAU;oBACfkB,EAAE,EAAC,mBAAmB;oBACtBmJ,OAAO,EAAE5M,iBAAkB;oBAC3BiL,QAAQ,EAAGxG,CAAC,IAAKxE,oBAAoB,CAACwE,CAAC,CAACyG,MAAM,CAAC0B,OAAO,CAAE;oBACxDpD,SAAS,EAAC;kBAAM;oBAAAxB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACF9Q,OAAA;oBAAO+T,OAAO,EAAC,mBAAmB;oBAACrD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErE;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACR9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAAC8B,kBAAkB,GAAG,gBAAgB,GAAG,iBAAiB,kBAAAiD,MAAA,CAAenC,iBAAiB,GAAG,aAAa,GAAG,EAAE,CAAG;gBAClKgL,WAAW,EAAC,mDAAuB;gBACnCjI,KAAK,EAAE3D,2BAA4B;gBACnC6L,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM1B,KAAK,GAAG0B,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAC+J,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC/H,KAAK,CAAChC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjC1D,8BAA8B,CAAC0D,KAAK,GAAG3L,oBAAoB,CAAC2V,QAAQ,CAAChK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACxF;gBACF,CAAE;gBACFiK,QAAQ,EAAEhN;cAAkB;gBAAAgI,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EACDxM,UAAU,CAAC8B,kBAAkB,iBAC5BpG,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAAC8B,kBAAkB;cAAA;gBAAA8I,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACkC,cAAc,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBACpH0L,WAAW,EAAC,qDAAsB;gBAClCjI,KAAK,EAAEvD,uBAAwB;gBAC/ByL,QAAQ,EAAGxG,CAAC,IAAK;kBACf,MAAM1B,KAAK,GAAG0B,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAC+J,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;kBAC/C,IAAI,CAAC/H,KAAK,CAAChC,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;oBACjCtD,0BAA0B,CAACsD,KAAK,GAAG3L,oBAAoB,CAAC2V,QAAQ,CAAChK,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;kBACpF;gBACF;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDxM,UAAU,CAACkC,cAAc,iBACxBxG,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACkC,cAAc;cAAA;gBAAA0I,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvF9Q,OAAA;gBACEyJ,IAAI,EAAC,MAAM;gBACXiH,SAAS,EAAC,oDAAoD;gBAC9DzG,KAAK,EAAErD,eAAgB;gBACvBuL,QAAQ,EAAGxG,CAAC,IAAK9E,kBAAkB,CAAC8E,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAAC,qBAAW,eAAA3Q,OAAA;kBAAM0Q,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1H9Q,OAAA;gBACE0Q,SAAS,6BAAArH,MAAA,CAA6B/E,UAAU,CAACwC,UAAU,GAAG,gBAAgB,GAAG,iBAAiB,gBAAc;gBAChHmD,KAAK,EAAEnD,UAAW;gBAClBqL,QAAQ,EAAGxG,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACyG,MAAM,CAACnI,KAAK,CAAE;gBAC/CmK,QAAQ;gBAAAzD,QAAA,gBAER3Q,OAAA;kBAAQiK,KAAK,EAAC,EAAE;kBAAA0G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzC9Q,OAAA;kBAAQiK,KAAK,EAAC,MAAM;kBAAA0G,QAAA,EAAC;gBAAa;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C9Q,OAAA;kBAAQiK,KAAK,EAAC,QAAQ;kBAAA0G,QAAA,EAAC;gBAAe;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C9Q,OAAA;kBAAQiK,KAAK,EAAC,SAAS;kBAAA0G,QAAA,EAAC;gBAAmB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EACRxM,UAAU,CAACwC,UAAU,iBACpB9G,OAAA;gBAAG0Q,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBACxD3Q,OAAA,CAACR,WAAW;kBAAC6R,IAAI,EAAE,EAAG;kBAACX,SAAS,EAAC;gBAAM;kBAAAxB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACwC,UAAU;cAAA;gBAAAoI,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACJ;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN9Q,OAAA;cAAA2Q,QAAA,gBACE3Q,OAAA;gBAAO0Q,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/E9Q,OAAA;gBACE0Q,SAAS,EAAC,oDAAoD;gBAC9DyD,IAAI,EAAC,GAAG;gBACRjC,WAAW,EAAC,uCAAuB;gBACnCjI,KAAK,EAAEjD,QAAS;gBAChBmL,QAAQ,EAAGxG,CAAC,IAAK1E,WAAW,CAAC0E,CAAC,CAACyG,MAAM,CAACnI,KAAK;cAAE;gBAAAiF,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EACLxM,UAAU,CAACwI,MAAM,iBAChB9M,OAAA;cAAG0Q,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD3Q,OAAA,CAACR,WAAW;gBAAC6R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAM;gBAAAxB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACxM,UAAU,CAACwI,MAAM;YAAA;cAAAoC,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACJ,eACD9Q,OAAA;cACEyJ,IAAI,EAAC,QAAQ;cACbiH,SAAS,EAAC,uHAAuH;cACjIwD,QAAQ,EAAE1P,YAAa;cAAAmM,QAAA,EAEtBnM,YAAY,GAAG,eAAe,GAAG;YAAc;cAAA0K,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEAhP,cAAc,KAAK,MAAM,iBACxB9B,OAAA;UAAA2Q,QAAA,EACGnJ,WAAW,gBACVxH,OAAA;YAAK0Q,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpD3Q,OAAA,CAACnB,cAAc;cAAAqQ,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,GACJjQ,cAAc,gBAChBb,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAKqU,GAAG,EAAEzM,mBAAoB;cAAC8I,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAE1E3Q,OAAA;gBAAK0Q,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,eACnE3Q,OAAA;kBAAK0Q,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3Q,OAAA;oBAAK0Q,SAAS,EAAC,+FAA+F;oBAAAC,QAAA,EAAC;kBAE/G;oBAAAzB,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9Q,OAAA;oBAAA2Q,QAAA,gBACE3Q,OAAA;sBAAI0Q,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClE9Q,OAAA;sBAAG0Q,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA2B;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpE9Q,OAAA;sBAAG0Q,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,qBAAW,EAAC,IAAIzD,IAAI,CAAC,CAAC,CAACmG,kBAAkB,CAAC,OAAO,CAAC;oBAAA;sBAAAnE,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAGN9Q,OAAA;gBAAK0Q,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC3Q,OAAA;kBAAI0Q,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClE9Q,OAAA;kBAAK0Q,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB3Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,GAAA3Q,oBAAA,GAACU,cAAc,CAACsO,IAAI,cAAAhP,oBAAA,uBAAnBA,oBAAA,CAAqB8K,QAAQ,EAAC,GAAC,GAAA7K,qBAAA,GAACS,cAAc,CAACsO,IAAI,cAAA/O,qBAAA,uBAAnBA,qBAAA,CAAqB4K,SAAS;kBAAA;oBAAAkE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpH9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAc;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAzQ,qBAAA,GAAAQ,cAAc,CAACsO,IAAI,cAAA9O,qBAAA,uBAAnBA,qBAAA,CAAqBiU,KAAK,KAAI,UAAU;kBAAA;oBAAApF,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrG9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAI;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAxQ,qBAAA,GAAAO,cAAc,CAACsO,IAAI,cAAA7O,qBAAA,uBAAnBA,qBAAA,CAAqB6S,KAAK,KAAI,UAAU;kBAAA;oBAAAjE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3F9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAO;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,EAAAvQ,qBAAA,GAAAM,cAAc,CAACsO,IAAI,cAAA5O,qBAAA,uBAAnBA,qBAAA,CAAqB6S,UAAU,KAAI,UAAU;kBAAA;oBAAAlE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9Q,OAAA;gBAAA2Q,QAAA,gBACE3Q,OAAA;kBAAI0Q,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpE9Q,OAAA;kBAAK0Q,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvE3Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAM;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjQ,cAAc,CAAC6H,cAAc;kBAAA;oBAAAwG,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClF9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACzS,cAAc,CAACwC,cAAc,CAACmL,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAkD,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClH9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACzS,cAAc,CAACwC,cAAc,CAACsL,UAAU,IAAI,CAAC,CAAC;kBAAA;oBAAA+C,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7G9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACzS,cAAc,CAAC,CAACwC,cAAc,CAACmL,cAAc,IAAI,CAAC,KAAKnL,cAAc,CAACsL,UAAU,IAAI,CAAC,CAAC,CAAC;kBAAA;oBAAA+C,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9I9Q,OAAA;oBAAA2Q,QAAA,gBACE3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAW;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAAC,GAAG,eACrD9Q,OAAA;sBAAM0Q,SAAS,oCAAArH,MAAA,CAAoCxI,cAAc,CAACyK,MAAM,KAAK,MAAM,GAAG,6BAA6B,GACjHzK,cAAc,CAACyK,MAAM,KAAK,QAAQ,GAAG,yBAAyB,GAC5DzK,cAAc,CAACyK,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAC/D,+BAA+B,CAChC;sBAAAqF,QAAA,EACF9P,cAAc,CAACyK,MAAM,KAAK,MAAM,GAAG,eAAe,GACjDzK,cAAc,CAACyK,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GACpDzK,cAAc,CAACyK,MAAM,KAAK,SAAS,GAAG,qBAAqB,GACzD;oBAAS;sBAAA4D,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACJ9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjQ,cAAc,CAAC6L,WAAW,GAAG,IAAIQ,IAAI,CAACrM,cAAc,CAAC6L,WAAW,CAAC,CAAC2G,kBAAkB,CAAC,CAAC,GAAG,iBAAiB;kBAAA;oBAAAnE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzK9Q,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAe;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjQ,cAAc,CAACuL,OAAO,GAAG,IAAIc,IAAI,CAACrM,cAAc,CAACuL,OAAO,CAAC,CAACiH,kBAAkB,CAAC,CAAC,GAAG,UAAU;kBAAA;oBAAAnE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxJjQ,cAAc,CAAC4L,IAAI,iBAAIzM,OAAA;oBAAA2Q,QAAA,gBAAG3Q,OAAA;sBAAM0Q,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,KAAC,EAACjQ,cAAc,CAAC4L,IAAI;kBAAA;oBAAAyC,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLhQ,yBAAyB,IAAIA,yBAAyB,CAACyT,aAAa,IAAIzT,yBAAyB,CAACyT,aAAa,CAAChI,MAAM,GAAG,CAAC,iBACzHvM,OAAA;gBAAA2Q,QAAA,gBACE3Q,OAAA;kBAAI0Q,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzE9Q,OAAA;kBAAK0Q,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvB7P,yBAAyB,CAACyT,aAAa,CAAC/L,GAAG,CAAExH,OAAO,iBACnDhB,OAAA;oBAAsB0Q,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC9E3Q,OAAA;sBAAK0Q,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD3Q,OAAA;wBAAI0Q,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAE3P,OAAO,CAAC0P;sBAAS;wBAAAxB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpD9Q,OAAA;wBAAM0Q,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,GAAC,WACrE,EAAC3P,OAAO,CAACwT,UAAU;sBAAA;wBAAAtF,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAA5B,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN9Q,OAAA;sBAAA2Q,QAAA,gBAAG3Q,OAAA;wBAAM0Q,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAzB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACzS,cAAc,CAAC2C,OAAO,CAACyT,MAAM,CAAC;oBAAA;sBAAAvF,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/F9Q,OAAA;sBAAA2Q,QAAA,gBAAG3Q,OAAA;wBAAM0Q,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAc;wBAAAzB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC,IAAI5D,IAAI,CAAClM,OAAO,CAAC0T,QAAQ,CAAC,CAACrB,kBAAkB,CAAC,CAAC;oBAAA;sBAAAnE,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACrH9P,OAAO,CAACyL,IAAI,iBAAIzM,OAAA;sBAAA2Q,QAAA,gBAAG3Q,OAAA;wBAAM0Q,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAzB,QAAA,EAAA0B,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAAC9P,OAAO,CAACyL,IAAI;oBAAA;sBAAAyC,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,GATtF9P,OAAO,CAAC2J,EAAE;oBAAAuE,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUf,CACN;gBAAC;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN9Q,OAAA;kBAAK0Q,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C3Q,OAAA;oBAAG0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,6CAAsB,EAACtS,cAAc,CAACyC,yBAAyB,CAAC6T,WAAW,IAAI,CAAC,CAAC;kBAAA;oBAAAzF,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjH9Q,OAAA;oBAAG0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,2CAAkB,EAACtS,cAAc,CAACwC,cAAc,CAACmL,cAAc,IAAI,CAAC,CAAC;kBAAA;oBAAAkD,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGD9Q,OAAA;gBAAK0Q,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,eACjC3Q,OAAA;kBAAK0Q,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD3Q,OAAA;oBAAA2Q,QAAA,gBACE3Q,OAAA;sBAAG0Q,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAClE9Q,OAAA;sBAAG0Q,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACN9Q,OAAA;oBAAK0Q,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B3Q,OAAA;sBAAG0Q,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD9Q,OAAA;sBAAK0Q,SAAS,EAAC;oBAAM;sBAAAxB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5B9Q,OAAA;sBAAG0Q,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxD9Q,OAAA;sBAAG0Q,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAzB,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,KAAC,eAGP9Q,OAAA;cAAK0Q,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvC3Q,OAAA;gBACEmR,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC3M,cAAc,CAAC8J,EAAE,CAAE;gBAC7C+F,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjF;cAED;gBAAAzB,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9Q,OAAA;gBACEmR,OAAO,EAAEpD,iBAAkB;gBAC3B2C,SAAS,EAAC,yGAAyG;gBAAAC,QAAA,EAElHjJ,aAAa,gBACZ1H,OAAA;kBAAM0Q,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBACjC3Q,OAAA;oBAAK0Q,SAAS,EAAC,4CAA4C;oBAACe,KAAK,EAAC,4BAA4B;oBAACI,IAAI,EAAC,MAAM;oBAACD,OAAO,EAAC,WAAW;oBAAAjB,QAAA,gBAC5H3Q,OAAA;sBAAQ0Q,SAAS,EAAC,YAAY;sBAACkE,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAAC/C,MAAM,EAAC,cAAc;sBAACc,WAAW,EAAC;oBAAG;sBAAA3D,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACrG9Q,OAAA;sBAAM0Q,SAAS,EAAC,YAAY;sBAACmB,IAAI,EAAC,cAAc;sBAACC,CAAC,EAAC;oBAAiH;sBAAA5C,QAAA,EAAA0B,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAA5B,QAAA,EAAA0B,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzK,CAAC,0BAER;gBAAA;kBAAA5B,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEP9Q,OAAA;kBAAA2Q,QAAA,EAAM;gBAAQ;kBAAAzB,QAAA,EAAA0B,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACrB;gBAAA5B,QAAA,EAAA0B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAA5B,QAAA,EAAA0B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAA5B,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEN9Q,OAAA;YAAK0Q,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAEhD;YAAAzB,QAAA,EAAA0B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAA5B,QAAA,EAAA0B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAA5B,QAAA,EAAA0B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAA5B,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9Q,OAAA,CAACjB,YAAY;MACXgW,MAAM,EAAEvT,gBAAiB;MACzBwT,SAAS,EAAEzE,aAAc;MACzBW,IAAI,EAAC,qGAAmD;MACxD+D,OAAO,EAAEzE;IAAa;MAAAtB,QAAA,EAAA0B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAAA;IAAA5B,QAAA,EAAA0B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAElB,CAAC;AAAC5Q,EAAA,CAjqDID,kBAAkB;EAAA,QACL1C,WAAW,EACXE,WAAW,EACuED,WAAW,EAEhEA,WAAW;AAAA;AAAA0X,EAAA,GALrDjV,kBAAkB;AAmqDxB,eAAeA,kBAAkB;AAAC,IAAAiV,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}