import { use, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchUsers } from "../../features/user/userSlice";
import { setSortOrder } from "../../features/user/userSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { TotalComponent } from "./TotalComponent";
const UserList = () => {
    const dispatch = useDispatch();
    const { users, loading, pagination, graduationYearFilter, classFilter, search } = useSelector((state) => state.users);
    const navigate = useNavigate();

    useEffect(() => {
        dispatch(fetchUsers({
            search,
            currentPage: pagination.page,
            limit: pagination.pageSize,
            sortOrder: pagination.sortOrder,
            graduationYear: graduationYearFilter,
            classFilter
        }));
    }, [dispatch, search, pagination.page, pagination.pageSize, pagination.sortOrder, graduationYearFilter, classFilter]);

    // useEffect(() => {
    //     console.log(users);
    // }, [users]);


    if (loading) return (
        <div className="flex items-center justify-center h-screen">
            <LoadingSpinner
                size="4rem"
                showText={true}
                text="Đang tải danh sách người dùng..."
            />
        </div>
    )

    return (
        <div className="flex flex-col gap-4 min-h-0 text-sm">
            <TotalComponent 
                total={pagination.total}
                page={pagination.page}
                pageSize={pagination.pageSize}
                setSortOrder={() => dispatch(setSortOrder())}
            />
            <div className="flex-grow h-[70vh] overflow-y-auto hide-scrollbar">
                <table className="w-full border-collapse border border-[#E7E7ED]">
                    <thead className="bg-[#F6FAFD] sticky top-0 z-10">
                        <tr className="border border-[#E7E7ED]">
                            <th className="p-3 text-center">ID</th>
                            <th className="p-3 text-center">Họ và Tên</th>
                            <th className="p-3 text-center">Kiểu</th>
                            <th className="p-3 text-center">Số Điện Thoại</th>
                            <th className="p-3 text-center">Trường Học</th>
                            <th className="p-3 text-center">Lớp</th>
                            <th className="p-3 text-center">Năm tốt nghiệp</th>
                            <th className="p-3 text-center">Tài khoản</th>
                            <th className="p-3 text-center">Mật khẩu</th>
                            <th className="p-3 text-center">Lớp</th>
                            <th className="p-3 text-center">Ngày tham gia</th>
                        </tr>
                    </thead>
                    <tbody>
                        {users.map((user, index) => (
                            <tr
                                onClick={() => navigate(`/admin/student-management/${user.id}`)}
                                key={user.id} className="border border-[#E7E7ED] hover:bg-gray-50 cursor-pointer">
                                <td className="p-3 text-center">{user.id}</td>
                                <td className="p-3 text-center">{user.lastName} {user.firstName}</td>
                                <td className="p-3 text-center">{user.userType}</td>
                                <td className="p-3 text-center">{user.phone || "Chưa có"}</td>
                                <td className="p-3 text-center">{user.highSchool || "Chưa cập nhật"}</td>
                                <td className="p-3 text-center">{user.class || "Chưa cập nhật"}</td>
                                <td className="p-3 text-center">{user.graduationYear || "Chưa cập nhật"}</td>
                                <td className="p-3 text-center">{user.username}</td>
                                <td className="p-3 text-center">{user.password?.length > 10 ? user.password?.substring(0, 10) + "..." : user.password}</td>
                                <td className="p-3 text-center">{user.classStatuses?.length > 0 ? (
                                    user.classStatuses?.map((cls, idx) => (
                                        <span key={idx} className="block">{cls.class.name}</span>
                                    ))
                                ) : "Chưa cập nhật"}</td>
                                <td className="p-3 text-center">
                                    {new Date(user.createdAt).toLocaleDateString()}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );

};

export default UserList;
