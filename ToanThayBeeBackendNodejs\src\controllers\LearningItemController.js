import db from "../models/index.js"
import { uploadPdfToFirebase, deletePdfFromFirebase } from "../utils/pdfUpload.js"
import { sendClassNotification } from "../utils/notificationUtils.js"
import * as learningItemService from "../services/learningItem.service.js"
import PaginationResponse from "../dtos/responses/pagination/PaginationResponse.js"
import * as notificationService from "../services/notification.service.js"

export const getLearningItemById = async (req, res) => {
    const { id } = req.params
    const learningItem = await db.LearningItem.findOne({ where: { id } })
    if (!learningItem) {
        return res.status(404).json({
            message: `Không tìm thấy mục học tập với ID: ${id}!`
        })
    }
    return res.status(200).json({
        message: '<PERSON><PERSON><PERSON> thông tin mục học tập thành công!',
        data: learningItem
    })
}

export const getLearningItemByLesson = async (req, res) => {
    const { lessonId } = req.params
    const find = await db.Lesson.findByPk(lessonId)
    if (!find) {
        return res.status(404).json({
            message: `Không tìm thấy buổi học với ID: ${lessonId}!`
        })
    }
    const learningItems = await db.LearningItem.findAll({
        where: { lessonId },
        include: [
            {
                model: db.Lesson,
                as: 'lesson',
                attributes: ['id', 'name', 'description']
            }
        ],
        order: [['createdAt', 'ASC']]
    })

    return res.status(200).json({
        message: 'Lấy danh sách mục học tập thành công!',
        data: learningItems
    })

}
export const getUncompletedLearningItem = async (req, res) => {
    const { id } = req.user;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    // console.log(page, limit);
    const result = await learningItemService.getUncompletedLearningItemService(id, page, limit);

    return res.status(200).json({
        message: 'Lấy danh sách mục học tập chưa hoàn thành thành công!',
        data: result
    });
};

export const postLearningItem = async (req, res) => {
    const t = await db.sequelize.transaction();
    try {
        const newLearningItem = await db.LearningItem.create(req.body, { transaction: t });

        const lessonUpdated = await db.Lesson.increment(
            { learningItemCount: 1 },
            {
                where: { id: newLearningItem.lessonId },
                transaction: t,
            }
        );

        if (!lessonUpdated[0]) {
            throw new Error("Không tìm thấy buổi học để cập nhật learningItemCount.");
        }

        // 🔍 Bước 1: Tìm classId từ lessonId
        const lesson = await db.Lesson.findByPk(newLearningItem.lessonId, {
            attributes: ['classId'],
            transaction: t,
        });

        // 🔍 Bước 2: Tìm tất cả học sinh đã JOINED lớp
        const students = await db.StudentClassStatus.findAll({
            where: {
                classId: lesson.classId,
                status: 'JS',
            },
            attributes: ['studentId'],
            transaction: t,
        });

        const studentStudyStatuses = students.map((s) => ({
            studentId: s.studentId,
            learningItemId: newLearningItem.id,
            isDone: false,
            studyTime: null,
        }));

        // Bước 3: Tạo tất cả bản ghi trạng thái học tập
        if (studentStudyStatuses.length > 0) {
            await db.StudentStudyStatus.bulkCreate(studentStudyStatuses, {
                transaction: t,
                ignoreDuplicates: true, // đề phòng trường hợp thêm lại
            });
        }

        await t.commit();

        // Gửi thông báo cho tất cả học sinh trong lớp
        try {
            // Lấy thông tin buổi học và lớp học
            const lessonInfo = await db.Lesson.findByPk(newLearningItem.lessonId, {
                include: [
                    {
                        model: db.Class,
                        as: 'class',
                        attributes: ['id', 'name', 'class_code']
                    }
                ]
            });

            if (lessonInfo && lessonInfo.class) {
                // Lấy Socket.IO instance từ app
                // const io = req.app.get('io');

                // Tạo URL để học sinh có thể truy cập vào mục học tập
                const actionUrl = `/class/${lessonInfo.class.class_code}/learning`;

                // Tạo tiêu đề và nội dung thông báo dựa trên loại mục học tập
                let title = "Mục học tập mới đã được thêm";
                let content = `Mục học tập "${newLearningItem.name}" đã được thêm vào buổi học "${lessonInfo.name}" của lớp "${lessonInfo.class.name}"`;

                // Gửi thông báo
                await notificationService.createNotificationsForUsers(
                    students.map(s => s.studentId),
                    {
                        title: title,
                        content: content,
                        type: 'LEARNING_ITEM',
                        relatedId: newLearningItem.id,
                        relatedType: 'LEARNING_ITEM',
                        actionUrl: actionUrl,
                        isRead: false
                    }
                );
                // await sendClassNotification(
                //     io,
                //     lessonInfo.class.id,
                //     title,
                //     content,
                //     'LEARNING_ITEM',
                //     actionUrl
                // );
            }
        } catch (notificationError) {
            console.error('Lỗi khi gửi thông báo:', notificationError);
            // Không ảnh hưởng đến kết quả trả về nếu gửi thông báo thất bại
        }

        return res.status(201).json({
            message: "Thêm mục học tập mới thành công và cập nhật trạng thái học tập!",
            data: newLearningItem,
        });
    } catch (error) {
        await t.rollback();
        return res.status(500).json({
            message: "Lỗi khi thêm mục học tập hoặc cập nhật trạng thái học.",
            error: error.message,
        });
    }
};

export const uploadLearningItemPdf = async (req, res) => {
    const { id } = req.params;

    const learningItem = await db.LearningItem.findOne({ where: { id } });
    if (!learningItem) {
        return res.status(404).json({
            message: `Không tìm thấy mục học tập với ID: ${id}!`
        });
    }

    // Khởi tạo transaction
    const transaction = await db.sequelize.transaction();
    let uploadedFile;
    try {
        // Nếu đã có URL PDF cũ thì xóa trước
        if (learningItem.url) {
            await deletePdfFromFirebase(learningItem.url);
        }

        if (req.file) {
            // Upload PDF mới
            uploadedFile = await uploadPdfToFirebase(req, 'learningItemsPdf');

            // Cập nhật URL trong database
            await learningItem.update({ url: uploadedFile.file }, { transaction });
        } else {
            // Nếu không có file mới, xóa URL cũ
            await learningItem.update({ url: null }, { transaction });
        }

        // Commit transaction
        await transaction.commit();

        return res.status(200).json({
            message: uploadedFile ? "Upload file PDF thành công!" : "Xóa file PDF thành công!",
            data: learningItem
        });
    } catch (error) {
        // Rollback transaction nếu có lỗi
        await transaction.rollback();

        // Nếu đã upload file, xóa lại file đã upload
        if (uploadedFile) {
            await deletePdfFromFirebase(uploadedFile.file);
        }

        return res.status(500).json({
            message: "Lỗi khi upload file PDF.",
            error: error.message
        });
    }
};



export const putLearningItem = async (req, res) => {
    try {
        const { id } = req.params;

        const learningItem = await db.LearningItem.findByPk(id);
        if (!learningItem) {
            return res.status(404).json({
                message: `Không tìm thấy mục học tập với ID: ${id}!`
            });
        }

        // Nếu là DOC và có url cũ → kiểm tra trùng trước khi xóa
        if (learningItem.typeOfLearningItem === 'DOC' && learningItem.url) {
            try {
                const isDuplicate = await learningItemService.checkUrlDuplicate(learningItem.id, learningItem.url);
                if (!isDuplicate) {
                    await deletePdfFromFirebase(learningItem.url);
                }
            } catch (err) {
                console.error("Không thể xóa file cũ trên Firebase:", err.message);
            }
        }

        // Cập nhật thông tin mới
        await learningItem.update(req.body);

        return res.status(200).json({
            message: "Cập nhật thông tin mục học tập thành công!",
            data: learningItem
        });

    } catch (error) {
        console.error("Lỗi khi cập nhật mục học tập:", error);
        return res.status(500).json({
            message: "Đã xảy ra lỗi trong quá trình cập nhật mục học tập."
        });
    }
};


export const deleteLearningItem = async (req, res) => {
    const { id } = req.params
    const t = await db.sequelize.transaction()

    try {
        // Tìm learning item để lấy lessonId trước khi xóa
        const learningItem = await db.LearningItem.findByPk(id, { transaction: t });
        if (!learningItem) {
            await t.rollback();
            return res.status(404).json({
                message: `Không tìm thấy mục học tập với ID: ${id}!`
            });
        }

        const lessonId = learningItem.lessonId;

        // Sử dụng service để xóa learning item
        const deleteResult = await learningItemService.deleteLearningItem(id, t);

        // Giảm learningItemCount của lesson
        const lessonUpdated = await db.Lesson.decrement(
            { learningItemCount: 1 },
            {
                where: { id: lessonId },
                transaction: t,
            }
        );

        if (!lessonUpdated[0]) {
            throw new Error("Không tìm thấy buổi học để cập nhật learningItemCount.");
        }

        // Commit transaction trước
        await t.commit();

        // Xóa file trên Firebase sau khi transaction đã commit thành công
        if (deleteResult.fileToDelete) {
            try {
                const { deletePdfFromFirebase } = await import("../utils/pdfUpload.js");
                await deletePdfFromFirebase(deleteResult.fileToDelete);
            } catch (firebaseError) {
                console.error("Lỗi khi xóa file trên Firebase:", firebaseError);
                // Không ảnh hưởng đến kết quả trả về vì database đã thành công
            }
        }

        return res.status(200).json({
            message: "Xóa mục học tập thành công!",
            data: { id: parseInt(id) }
        });
    } catch (error) {
        await t.rollback()
        return res.status(500).json({
            message: "Lỗi khi xóa mục học tập hoặc cập nhật learningItemCount.",
            error: error.message
        })
    }
}

export const markLearningItem = async (req, res) => {
    const { learningItemId } = req.params;
    const studentId = req.user?.id;

    if (!studentId) {
        return res.status(401).json({
            message: 'Người dùng chưa xác thực!',
        });
    }

    const status = await db.StudentStudyStatus.findOne({
        where: {
            learningItemId,
            studentId,
        },
    });

    if (!status) {
        return res.status(404).json({
            message: 'Không tìm thấy trạng thái học cho mục học tập này!',
        });
    }

    await status.update({
        isDone: !status.isDone,
        studyTime: new Date(),
    });

    return res.status(200).json({
        message: 'Đánh dấu mục học tập thành công!',
        data: status,
    });
};
