import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import UserLayout from '../../../layouts/UserLayout';
import AttendanceCard from '../../../components/attendance/AttendanceCard';
import {
    Calendar,
    CheckCircle,
    XCircle,
    AlertCircle,
    Filter,
    BarChart3,
    ChevronDown,
    ChevronUp
} from 'lucide-react';
import {
    fetchUserAttendances,
    setSelectedYear,
    setSelectedMonth,
    clearUserAttendancesFilters,
    clearUserAttendancesError
} from '../../../features/attendance/attendanceSlice';

const UserAttendancePage = () => {
    const dispatch = useDispatch();
    const { user } = useSelector(state => state.auth);
    const {
        userAttendances,
        userAttendancesLoading,
        userAttendancesError,
        selectedYear,
        selectedMonth
    } = useSelector(state => state.attendances);

    const [expandedMonths, setExpandedMonths] = useState(new Set());
    const [showFilters, setShowFilters] = useState(false);

    // Generate year options (current year and 2 years back)
    const currentYear = new Date().getFullYear();
    const yearOptions = Array.from({ length: 3 }, (_, i) => currentYear - i);

    // Month options
    const monthOptions = [
        { value: null, label: 'Tất cả tháng' },
        { value: 1, label: 'Tháng 1' },
        { value: 2, label: 'Tháng 2' },
        { value: 3, label: 'Tháng 3' },
        { value: 4, label: 'Tháng 4' },
        { value: 5, label: 'Tháng 5' },
        { value: 6, label: 'Tháng 6' },
        { value: 7, label: 'Tháng 7' },
        { value: 8, label: 'Tháng 8' },
        { value: 9, label: 'Tháng 9' },
        { value: 10, label: 'Tháng 10' },
        { value: 11, label: 'Tháng 11' },
        { value: 12, label: 'Tháng 12' }
    ];

    // Load data when component mounts or filters change
    useEffect(() => {
        if (user?.id) {
            const params = { year: selectedYear };
            if (selectedMonth) {
                params.month = selectedMonth;
            }
            dispatch(fetchUserAttendances({ userId: user.id, params }));
        }
    }, [dispatch, user?.id, selectedYear, selectedMonth]);

    // Handle filter changes
    const handleYearChange = (year) => {
        dispatch(setSelectedYear(year));
    };

    const handleMonthChange = (month) => {
        dispatch(setSelectedMonth(month));
    };

    const handleClearFilters = () => {
        dispatch(clearUserAttendancesFilters());
    };

    // Toggle month expansion
    const toggleMonthExpansion = (monthKey) => {
        const newExpanded = new Set(expandedMonths);
        if (newExpanded.has(monthKey)) {
            newExpanded.delete(monthKey);
        } else {
            newExpanded.add(monthKey);
        }
        setExpandedMonths(newExpanded);
    };

    // Format month display
    const formatMonthDisplay = (monthKey) => {
        const [year, month] = monthKey.split('-');
        return `Tháng ${parseInt(month)}/${year}`;
    };

    if (userAttendancesLoading) {
        return (
            <UserLayout>
                <div className="container mx-auto px-4 py-8">
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sky-500"></div>
                    </div>
                </div>
            </UserLayout>
        );
    }

    return (
        <UserLayout>
            <div className="container mx-auto px-4 py-8 max-w-6xl">
                {/* Header */}
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                            <Calendar className="text-sky-600" />
                            Lịch sử điểm danh
                        </h1>
                        <p className="text-gray-600 mt-1">
                            Xem lại các buổi học bạn đã tham gia
                        </p>
                    </div>

                    {/* Filter Toggle */}
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center gap-2 px-4 py-2 bg-sky-50 hover:bg-sky-100 text-sky-600 rounded-lg transition-colors"
                    >
                        <Filter size={16} />
                        <span>Bộ lọc</span>
                        {showFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </button>
                </div>

                {/* Filters */}
                {showFilters && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                        <div className="flex flex-wrap gap-4 items-end">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Năm học
                                </label>
                                <select
                                    value={selectedYear}
                                    onChange={(e) => handleYearChange(parseInt(e.target.value))}
                                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                >
                                    {yearOptions?.map(year => (
                                        <option key={year} value={year}>{year}</option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Tháng
                                </label>
                                <select
                                    value={selectedMonth || ''}
                                    onChange={(e) => handleMonthChange(e.target.value ? parseInt(e.target.value) : null)}
                                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                                >
                                    {monthOptions?.map(option => (
                                        <option key={option.value || 'all'} value={option.value || ''}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <button
                                onClick={handleClearFilters}
                                className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 hover:border-gray-400 rounded-lg transition-colors"
                            >
                                Xóa bộ lọc
                            </button>
                        </div>
                    </div>
                )}

                {/* Error Message */}
                {userAttendancesError && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        <p>{userAttendancesError}</p>
                        <button
                            onClick={() => dispatch(clearUserAttendancesError())}
                            className="text-red-600 hover:text-red-800 underline mt-1"
                        >
                            Đóng
                        </button>
                    </div>
                )}

                {/* Summary Stats */}
                {userAttendances.monthlyData?.length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-blue-100 rounded-full">
                                    <BarChart3 className="w-5 h-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Tổng buổi học</p>
                                    <p className="text-xl font-bold text-gray-800">
                                        {userAttendances.totalAttendances}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-100 rounded-full">
                                    <CheckCircle className="w-5 h-5 text-green-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Có mặt</p>
                                    <p className="text-xl font-bold text-green-600">
                                        {userAttendances.monthlyData.reduce((sum, month) => sum + month.summary.present, 0)}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-red-100 rounded-full">
                                    <XCircle className="w-5 h-5 text-red-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Vắng mặt</p>
                                    <p className="text-xl font-bold text-red-600">
                                        {userAttendances.monthlyData.reduce((sum, month) => sum + month.summary.absent, 0)}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-yellow-100 rounded-full">
                                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Muộn</p>
                                    <p className="text-xl font-bold text-yellow-600">
                                        {userAttendances.monthlyData.reduce((sum, month) => sum + month.summary.late, 0)}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Monthly Data */}
                {userAttendances.monthlyData?.length === 0 ? (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                        <Calendar size={48} className="mx-auto text-gray-300 mb-4" />
                        <h3 className="text-lg font-medium text-gray-800 mb-2">
                            Không có dữ liệu điểm danh
                        </h3>
                        <p className="text-gray-600">
                            Chưa có buổi học nào được ghi nhận trong khoảng thời gian này.
                        </p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {userAttendances.monthlyData?.map((monthData) => (
                            <div key={monthData.month} className="bg-white rounded-lg shadow-sm border border-gray-200">
                                {/* Month Header */}
                                <div
                                    className="p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors"
                                    onClick={() => toggleMonthExpansion(monthData.month)}
                                >
                                    <div className="flex justify-between items-center">
                                        <div className="flex items-center gap-3">
                                            <h3 className="text-lg font-semibold text-gray-800">
                                                {formatMonthDisplay(monthData.month)}
                                            </h3>
                                            <span className="text-sm text-gray-500">
                                                ({monthData.summary.total} buổi học)
                                            </span>
                                        </div>

                                        <div className="flex items-center gap-4">
                                            {/* Month Summary */}
                                            <div className="flex items-center gap-3 text-sm">
                                                <span className="flex items-center gap-1 text-green-600">
                                                    <CheckCircle size={14} />
                                                    {monthData.summary.present}
                                                </span>
                                                <span className="flex items-center gap-1 text-red-600">
                                                    <XCircle size={14} />
                                                    {monthData.summary.absent}
                                                </span>
                                                <span className="flex items-center gap-1 text-yellow-600">
                                                    <AlertCircle size={14} />
                                                    {monthData.summary.late}
                                                </span>
                                            </div>

                                            {expandedMonths.has(monthData.month) ?
                                                <ChevronUp size={20} className="text-gray-400" /> :
                                                <ChevronDown size={20} className="text-gray-400" />
                                            }
                                        </div>
                                    </div>
                                </div>

                                {/* Month Details */}
                                {expandedMonths.has(monthData.month) && (
                                    <div className="p-4">
                                        <div className="space-y-4">
                                            {monthData.attendances?.map((attendance) => (
                                                <AttendanceCard
                                                    key={attendance.id}
                                                    attendance={attendance}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </UserLayout>
    );
};

export default UserAttendancePage;
