import * as notificationService from '../services/notification.service.js';

/**
 * Get notifications for the authenticated user
 */
export const getUserNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit, offset, isRead, type, sortBy, sortOrder } = req.query;

    // Parse query parameters
    const options = {
      limit: limit ? parseInt(limit, 10) : 10,
      offset: offset ? parseInt(offset, 10) : 0,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'DESC'
    };

    // Parse isRead if provided
    if (isRead !== undefined) {
      options.isRead = isRead === 'true';
    }

    // Add type if provided
    if (type) {
      options.type = type;
    }

    const result = await notificationService.getUserNotifications(userId, options);

    return res.status(200).json({
      message: 'Lấy danh sách thông báo thành công',
      data: result.notifications,
      totalItems: result.pagination.total,
      limit: options.limit,
      offset: options.offset,
      hasMore: result.notifications.length === options.limit && options.offset + result.notifications.length < result.pagination.total
    });
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return res.status(500).json({
      message: 'Lỗi khi lấy danh sách thông báo',
      error: error.message
    });
  }
};

/**
 * Get unread notification count for the authenticated user
 */
export const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await notificationService.getUnreadNotificationCount(userId);

    return res.status(200).json({
      message: 'Lấy số lượng thông báo chưa đọc thành công',
      data: { count }
    });
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    return res.status(500).json({
      message: 'Lỗi khi lấy số lượng thông báo chưa đọc',
      error: error.message
    });
  }
};

/**
 * Mark notifications as read
 */
export const markAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    const { notificationIds } = req.body;

    if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
      return res.status(400).json({
        message: 'Vui lòng cung cấp danh sách ID thông báo cần đánh dấu đã đọc'
      });
    }

    const updatedCount = await notificationService.markNotificationsAsRead(userId, notificationIds);

    // Lấy số lượng thông báo chưa đọc sau khi cập nhật
    const unreadCount = await notificationService.getUnreadNotificationCount(userId);

    return res.status(200).json({
      message: 'Đánh dấu thông báo đã đọc thành công',
      data: {
        updatedCount,
        notificationIds,
        unreadCount
      }
    });
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return res.status(500).json({
      message: 'Lỗi khi đánh dấu thông báo đã đọc',
      error: error.message
    });
  }
};

/**
 * Mark all notifications as read
 */
export const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    const updatedCount = await notificationService.markAllNotificationsAsRead(userId);

    // Sau khi đánh dấu tất cả là đã đọc, số lượng chưa đọc sẽ là 0
    const unreadCount = 0;

    return res.status(200).json({
      message: 'Đánh dấu tất cả thông báo đã đọc thành công',
      data: {
        updatedCount,
        unreadCount
      }
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(500).json({
      message: 'Lỗi khi đánh dấu tất cả thông báo đã đọc',
      error: error.message
    });
  }
};

/**
 * Delete notifications
 */
export const deleteNotifications = async (req, res) => {
  try {
    const userId = req.user.id;
    const { notificationIds } = req.body;

    if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
      return res.status(400).json({
        message: 'Vui lòng cung cấp danh sách ID thông báo cần xóa'
      });
    }

    const deletedCount = await notificationService.deleteNotifications(userId, notificationIds);

    // Lấy số lượng thông báo chưa đọc sau khi xóa
    const unreadCount = await notificationService.getUnreadNotificationCount(userId);

    return res.status(200).json({
      message: 'Xóa thông báo thành công',
      data: {
        deletedCount,
        notificationIds,
        unreadCount
      }
    });
  } catch (error) {
    console.error('Error deleting notifications:', error);
    return res.status(500).json({
      message: 'Lỗi khi xóa thông báo',
      error: error.message
    });
  }
};

/**
 * Delete notification
 */
export const deleteNotification = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const deletedCount = await notificationService.deleteNotification(userId, id);

    // Lấy số lượng thông báo chưa đọc sau khi xóa
    const unreadCount = await notificationService.getUnreadNotificationCount(userId);

    return res.status(200).json({
      message: 'Xóa thông báo thành công',
      data: {
        id,
        deletedCount,
        unreadCount
      }
    });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return res.status(500).json({
      message: 'Lỗi khi xóa thông báo',
      error: error.message
    });
  }
};
