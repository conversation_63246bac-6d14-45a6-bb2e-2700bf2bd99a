import Joi from "joi"

class PostUserRequest {
    constructor(data) {
        this.lastName = data.lastName;
        this.firstName = data.firstName;
        this.username = data.username;
        this.password = data.password;
        this.gender = data.gender;
        this.birthDate = data.birthDate;
        this.phone = data.phone;
        this.highSchool = data.highSchool;
        this.class = data.class;
        this.avatarUrl = data.avatarUrl;
        this.userType = data.userType;
        this.classIds = data.classIds;
        this.graduationYear = data.graduationYear;
    }

    static validate(data) {
        const schema = Joi.object({
            lastName: Joi.string().max(50).trim().required(),
            firstName: Joi.string().max(50).trim().required(),
            username: Joi.string().min(3).max(30).trim().optional(),
            password: Joi.string().min(6).max(50).optional(),
            gender: Joi.boolean().optional(),
            birthDate: Joi.date().less("now").optional().allow(null),
            phone: Joi.alternatives().try(
                Joi.string().pattern(/^[0-9]{10,15}$/),
                Joi.allow(null)
            ).required(),
            highSchool: Joi.string().max(100).trim().required(),
            class: Joi.string().max(50).trim().required(),
            avatarUrl: Joi.string().uri().optional(),
            userType: Joi.string().valid("HS1", "GV", "AS").required(),
            classIds: Joi.array().items(Joi.number().integer()).optional(),
            graduationYear: Joi.number().integer().required(),
        });

        return schema.validate(data, { abortEarly: false });
    }
}

export default PostUserRequest;
