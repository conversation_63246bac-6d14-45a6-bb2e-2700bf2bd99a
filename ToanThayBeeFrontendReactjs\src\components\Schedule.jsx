import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import { useSelector } from 'react-redux';

const Schedule = ({ classes }) => {
    const [isMobile, setIsMobile] = useState(false);

    // Check if the device is mobile
    useEffect(() => {
        const checkIfMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkIfMobile();
        window.addEventListener('resize', checkIfMobile);

        return () => {
            window.removeEventListener('resize', checkIfMobile);
        };
    }, []);
    const getTimeSlotsFromClasses = (classes) => {
        if (!classes || classes.length === 0) return [];

        let minHour = 24;
        let maxHour = 0;

        classes.forEach(cls => {
            // Handle new format (startTime1/endTime1, startTime2/endTime2)
            if (cls.dayOfWeek1 && cls.startTime1 && cls.endTime1) {
                const [startHour1] = cls.startTime1.split(':').map(Number);
                const [endHour1] = cls.endTime1.split(':').map(Number);
                minHour = Math.min(minHour, startHour1);
                maxHour = Math.max(maxHour, endHour1);
            }

            if (cls.dayOfWeek2 && cls.startTime2 && cls.endTime2) {
                const [startHour2] = cls.startTime2.split(':').map(Number);
                const [endHour2] = cls.endTime2.split(':').map(Number);
                minHour = Math.min(minHour, startHour2);
                maxHour = Math.max(maxHour, endHour2);
            }

            // Handle old format (backward compatibility)
            if (cls.dayOfWeek && cls.startTime && cls.endTime) {
                const [startHour] = cls.startTime.split(':').map(Number);
                const [endHour] = cls.endTime.split(':').map(Number);
                minHour = Math.min(minHour, startHour);
                maxHour = Math.max(maxHour, endHour);
            }
        });

        // Tạo mảng các giờ từ min đến max
        const timeSlots = [];
        for (let hour = minHour; hour <= maxHour; hour++) {
            timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);
        }

        return timeSlots;
    };
    const timeSlots = getTimeSlotsFromClasses(classes);


    const weekDays = [
        { short: 'T2', long: 'Thứ 2' },
        { short: 'T3', long: 'Thứ 3' },
        { short: 'T4', long: 'Thứ 4' },
        { short: 'T5', long: 'Thứ 5' },
        { short: 'T6', long: 'Thứ 6' },
        { short: 'T7', long: 'Thứ 7' },
        { short: 'CN', long: 'CN' },
    ];

    const getClassHeight = (startTime, endTime) => {
        // Format times to HH:MM if they are in HH:MM:SS format
        const formattedStartTime = startTime.length > 5 ? startTime.substring(0, 5) : startTime;
        const formattedEndTime = endTime.length > 5 ? endTime.substring(0, 5) : endTime;

        const [sh, sm] = formattedStartTime.split(':').map(Number);
        const [eh, em] = formattedEndTime.split(':').map(Number);
        const durationHours = (eh - sh) + (em - sm) / 60;
        return `${durationHours * 4}rem`;
    };

    return (
        <div className="flex items-center w-full justify-center border border-gray ">
            <div className="bg-white rounded-xl w-full max-h-[50vh] flex flex-col">
                {isMobile && (
                    <div className="bg-sky-50 p-2 text-xs text-sky-700 flex items-center justify-center">
                        <div className="flex items-center">
                            <ChevronLeft size={14} />
                            <span className="mx-1">Kéo ngang để xem thêm</span>
                            <ChevronRight size={14} />
                        </div>
                    </div>
                )}

                {/* Calendar container with horizontal scroll for mobile */}
                <div className="overflow-auto w-full flex-grow">
                    <div className="min-w-[800px]"> {/* Minimum width to ensure horizontal scrolling on mobile */}
                        {/* Calendar header - Days of the week */}
                        <div className="grid sm:grid-cols-[6rem_repeat(7,1fr)] grid-cols-[4rem_repeat(7,1fr)] bg-emerald-50 border-b border-gray-200 sticky top-0 z-10">
                            <div className="p-2 border-r border-gray-200 sticky left-0 bg-emerald-50 z-20"></div>
                            {weekDays.map((day, index) => (
                                <div key={index} className="p-2 text-center border-r border-gray-200 last:border-r-0">
                                    <div className="font-semibold text-emerald-800">{day.short}</div>
                                    <div className="text-xs text-gray-500 hidden sm:block">{day.long}</div>
                                </div>
                            ))}
                        </div>

                        {/* Calendar body */}
                        <div className="grid sm:grid-cols-[6rem_repeat(7,1fr)] grid-cols-[4rem_repeat(7,1fr)]">
                            {/* Time slots (Y-axis) - Sticky on mobile */}
                            <div className="col-span-1 border-r border-gray-200 sticky left-0 bg-white z-10">
                                {timeSlots.map((time, index) => (
                                    <div
                                        key={index}
                                        className="h-16 border-b border-gray-200 flex items-center justify-center"
                                    >
                                        <span className="text-xs sm:text-sm text-gray-500">{time}</span>
                                    </div>
                                ))}
                            </div>

                            {/* Calendar grid */}
                            {weekDays.map((day, dayIndex) => (
                                <div key={dayIndex} className="col-span-1 border-r border-gray-200 last:border-r-0 relative">
                                    {timeSlots.map((_, timeIndex) => (
                                        <div
                                            key={timeIndex}
                                            className="h-16 border-b border-gray-200"
                                        ></div>
                                    ))}

                                    {/* Render class blocks */}
                                    {(() => {
                                        // Get classes for this day - check for either old format or new format
                                        const dayClasses = classes.filter(cls =>
                                            cls.status === 'LHD' &&
                                            cls.public == true &&
                                            (
                                                // New format: check if this day matches dayOfWeek1 or dayOfWeek2
                                                (cls.dayOfWeek1 === day.short && cls.startTime1 && cls.endTime1) ||
                                                (cls.dayOfWeek2 === day.short && cls.startTime2 && cls.endTime2) ||
                                                // Old format: backward compatibility
                                                (cls.dayOfWeek === day.short && cls.startTime && cls.endTime)
                                            )
                                        );

                                        // Find overlapping classes
                                        const overlappingGroups = [];

                                        // Process each class - create new objects with formatted times for both sessions
                                        const processedClasses = [];

                                        dayClasses.forEach((cls) => {
                                            // Handle new format - only show session that matches current day
                                            if (cls.dayOfWeek1 === day.short && cls.startTime1 && cls.endTime1) {
                                                // Session 1 for this day
                                                const startTime1 = cls.startTime1 || "08:00:00";
                                                const endTime1 = cls.endTime1 || "09:00:00";
                                                const formattedStartTime1 = startTime1.substring(0, 5);
                                                const formattedEndTime1 = endTime1.substring(0, 5);

                                                processedClasses.push({
                                                    ...cls,
                                                    formattedStartTime: formattedStartTime1,
                                                    formattedEndTime: formattedEndTime1,
                                                    sessionNumber: 1,
                                                    sessionId: `${cls.id}-session1`
                                                });
                                            }

                                            if (cls.dayOfWeek2 === day.short && cls.startTime2 && cls.endTime2) {
                                                // Session 2 for this day
                                                const startTime2 = cls.startTime2 || "10:00:00";
                                                const endTime2 = cls.endTime2 || "11:00:00";
                                                const formattedStartTime2 = startTime2.substring(0, 5);
                                                const formattedEndTime2 = endTime2.substring(0, 5);

                                                processedClasses.push({
                                                    ...cls,
                                                    formattedStartTime: formattedStartTime2,
                                                    formattedEndTime: formattedEndTime2,
                                                    sessionNumber: 2,
                                                    sessionId: `${cls.id}-session2`
                                                });
                                            }

                                            // Handle old format (backward compatibility)
                                            if (cls.dayOfWeek === day.short && cls.startTime && cls.endTime) {
                                                const startTime = cls.startTime || "08:00:00";
                                                const endTime = cls.endTime || "09:00:00";
                                                const formattedStartTime = startTime.substring(0, 5);
                                                const formattedEndTime = endTime.substring(0, 5);

                                                processedClasses.push({
                                                    ...cls,
                                                    formattedStartTime,
                                                    formattedEndTime,
                                                    sessionNumber: null,
                                                    sessionId: `${cls.id}-legacy`
                                                });
                                            }
                                        });

                                        // Process each class
                                        processedClasses.forEach((cls) => {
                                            const { formattedStartTime, formattedEndTime } = cls;

                                            const [startHour, startMin] = formattedStartTime.split(':').map(Number);
                                            const [endHour, endMin] = formattedEndTime.split(':').map(Number);

                                            // Convert to minutes for easier comparison
                                            const startMinutes = startHour * 60 + startMin;
                                            const endMinutes = endHour * 60 + endMin;

                                            // Check if this class overlaps with any existing group
                                            let foundGroup = false;

                                            for (const group of overlappingGroups) {
                                                // Check if this class overlaps with any class in the group
                                                const overlapsWithGroup = group.some(existingClass => {
                                                    const { formattedStartTime: eFormattedStartTime, formattedEndTime: eFormattedEndTime } = existingClass;

                                                    const [eStartHour, eStartMin] = eFormattedStartTime.split(':').map(Number);
                                                    const [eEndHour, eEndMin] = eFormattedEndTime.split(':').map(Number);

                                                    const eStartMinutes = eStartHour * 60 + eStartMin;
                                                    const eEndMinutes = eEndHour * 60 + eEndMin;

                                                    // Check for overlap
                                                    return (
                                                        (startMinutes < eEndMinutes && endMinutes > eStartMinutes) ||
                                                        (eStartMinutes < endMinutes && eEndMinutes > startMinutes)
                                                    );
                                                });

                                                if (overlapsWithGroup) {
                                                    group.push(cls);
                                                    foundGroup = true;
                                                    break;
                                                }
                                            }

                                            // If no overlapping group found, create a new one
                                            if (!foundGroup) {
                                                overlappingGroups.push([cls]);
                                            }
                                        });

                                        // Render all classes with appropriate positioning
                                        return overlappingGroups.flatMap((group, groupIndex) => {
                                            return group.map((cls, classIndex) => {
                                                const { formattedStartTime } = cls;
                                                const [sh, sm] = formattedStartTime.split(':').map(Number);
                                                const baseHour = parseInt(timeSlots[0].split(':')[0]);
                                                const top = (sh - baseHour + sm / 60) * 4;

                                                // Calculate width and left position based on number of overlapping classes
                                                const width = group.length > 1 ? `calc((100% - 0.5rem) / ${group.length})` : 'calc(100% - 0.5rem)';
                                                const left = group.length > 1 ? `calc(${classIndex} * (100% / ${group.length}))` : '0';

                                                // Determine color based on class name
                                                let colorClass;

                                                if (cls.name.toLowerCase().includes('đề')) {
                                                    // Lớp có chữ "đề"
                                                    colorClass = 'bg-sky-100 border-sky-500 text-sky-800';
                                                } else if (cls.name.toLowerCase().includes('đại')) {
                                                    // Lớp có chữ "đại"
                                                    colorClass = 'bg-purple-100 border-purple-500 text-purple-800';
                                                } else if (cls.name.toLowerCase().includes('hình')) {
                                                    // Lớp có chữ "hình"
                                                    colorClass = 'bg-amber-100 border-amber-500 text-amber-800';
                                                } else {
                                                    // Các lớp khác
                                                    const defaultColors = [
                                                        'bg-emerald-100 border-emerald-500 text-emerald-800',
                                                        'bg-rose-100 border-rose-500 text-rose-800',
                                                        'bg-cyan-100 border-cyan-500 text-cyan-800',
                                                        'bg-indigo-100 border-indigo-500 text-indigo-800',
                                                        'bg-lime-100 border-lime-500 text-lime-800'
                                                    ];

                                                    // Nếu có nhiều lớp trùng thời gian, sử dụng màu khác nhau
                                                    const colorIndex = group.length > 1 ? classIndex % defaultColors.length : groupIndex % defaultColors.length;
                                                    colorClass = defaultColors[colorIndex];
                                                }

                                                const [bgColor, borderColor, textColor] = colorClass.split(' ');

                                                return (
                                                    <div
                                                        key={cls.id || `${groupIndex}-${classIndex}`}
                                                        className={`absolute mx-1 ${bgColor} border-l-4 ${borderColor} rounded-r-md p-1 sm:p-2 overflow-hidden hover:brightness-95 transition-colors duration-150 cursor-pointer`}
                                                        style={{
                                                            top: `${top}rem`,
                                                            height: getClassHeight(cls.formattedStartTime, cls.formattedEndTime),
                                                            width: width,
                                                            left: left
                                                        }}
                                                        onClick={() => alert(`Lớp: ${cls.name}${cls.sessionNumber ? ` (Buổi ${cls.sessionNumber})` : ''}\nThời gian: ${cls.formattedStartTime} - ${cls.formattedEndTime}\nTrạng thái: ${cls.status}`)}
                                                    >
                                                        <div className={`font-semibold text-xs sm:text-sm ${textColor} truncate`}>
                                                            {cls.name}{cls.sessionNumber ? ` (${cls.sessionNumber})` : ''}
                                                        </div>
                                                        <div className="text-xs text-gray-700">{cls.formattedStartTime} - {cls.formattedEndTime}</div>
                                                        {!isMobile && cls.description && group.length === 1 && (
                                                            <div className="text-xs text-gray-600 mt-1 truncate">{cls.description}</div>
                                                        )}
                                                        {!isMobile && group.length === 1 && (
                                                            <div className="text-xs text-gray-500 mt-1">{cls.academicYear}</div>
                                                        )}
                                                    </div>
                                                );
                                            });
                                        });
                                    })()}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Schedule;
