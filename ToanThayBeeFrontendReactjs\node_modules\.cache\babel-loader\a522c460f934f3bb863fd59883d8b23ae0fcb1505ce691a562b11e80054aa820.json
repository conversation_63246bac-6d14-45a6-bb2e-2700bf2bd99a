{"ast": null, "code": "import api from \"./api\";\n\n// Tuition Payment APIs\n/**\r\n * Get all tuition payment records (admin)\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\nexport const getAllTuitionPaymentsAPI = async params => {\n  const response = await api.get(\"/v1/admin/tuition-payment\", {\n    params\n  });\n  return response;\n};\n\n/**\r\n * Get tuition payment records for a specific class (admin)\r\n * @param {string} classId - ID of the class\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\nexport const getClassTuitionPaymentsAPI = async (classId, params) => {\n  const response = await api.get(\"/v1/admin/class/\".concat(classId, \"/tuition-payment\"), {\n    params\n  });\n  return response;\n};\n\n/**\r\n * Get tuition payment records for a specific user (admin)\r\n * @param {string} userId - ID of the user\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\nexport const getUserTuitionPaymentsAdminAPI = async (userId, params) => {\n  // params = { search, currentPage, limit, sortOrder }\n  const response = await api.get(\"/v1/admin/user/\".concat(userId, \"/tuition-payment\"), {\n    params\n  });\n  return response;\n};\n\n/**\r\n * Get tuition payment records for the current user\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\nexport const getUserTuitionPaymentsAPI = async params => {\n  const response = await api.get(\"/v1/user/tuition-payment\", {\n    params\n  });\n  return response;\n};\n\n/**\r\n * Get a specific tuition payment record by ID (admin)\r\n * @param {string} id - ID of the payment record\r\n * @returns {Promise} - API response\r\n */\nexport const getTuitionPaymentByIdAdminAPI = async id => {\n  const response = await api.get(\"/v1/admin/tuition-payment/\".concat(id));\n  return response;\n};\n\n/**\r\n * Get a specific tuition payment record by ID (user)\r\n * @param {string} id - ID of the payment record\r\n * @returns {Promise} - API response\r\n */\nexport const getTuitionPaymentByIdUserAPI = async id => {\n  const response = await api.get(\"/v1/user/tuition-payment/\".concat(id));\n  return response;\n};\n\n/**\r\n * Create a new tuition payment record\r\n * @param {Object} paymentData - Payment data to create\r\n * @returns {Promise} - API response\r\n */\nexport const createTuitionPaymentAPI = async paymentData => {\n  const response = await api.post(\"/v1/admin/tuition-payment\", paymentData);\n  return response;\n};\n\n/**\r\n * Create multiple tuition payment records in batch\r\n * @param {Object} batchData - Batch payment data\r\n * @returns {Promise} - API response\r\n */\nexport const createBatchTuitionPaymentsAPI = async batchData => {\n  const response = await api.post(\"/v1/admin/tuition-payment/batch\", batchData);\n  return response;\n};\n\n/**\r\n * Update a tuition payment record\r\n * @param {string} id - ID of the payment record to update\r\n * @param {Object} paymentData - Updated payment data\r\n * @returns {Promise} - API response\r\n */\nexport const updateTuitionPaymentAPI = async _ref => {\n  let {\n    id,\n    paymentData\n  } = _ref;\n  const response = await api.put(\"/v1/admin/tuition-payment/\".concat(id), paymentData);\n  return response;\n};\n\n/**\r\n * Delete a tuition payment record\r\n * @param {string} id - ID of the payment record to delete\r\n * @returns {Promise} - API response\r\n */\nexport const deleteTuitionPaymentAPI = async id => {\n  const response = await api.delete(\"/v1/admin/tuition-payment/\".concat(id));\n  return response;\n};\n\n/**\r\n * Get tuition payment statistics\r\n * @param {Object} params - Query parameters (startMonth, endMonth, userClass)\r\n * @returns {Promise} - API response with statistics data\r\n */\nexport const getTuitionStatisticsAPI = async params => {\n  const response = await api.get(\"/v1/admin/tuition-payment/statistics\", {\n    params\n  });\n  return response;\n};\n\n/**\r\n * Get tuition summary for a user (admin view)\r\n * @param {string} userId - User ID\r\n * @returns {Promise} - API response with user tuition summary\r\n */\nexport const getUserTuitionSummaryAdminAPI = async userId => {\n  const response = await api.get(\"/v1/admin/user/\".concat(userId, \"/tuition-summary\"));\n  return response;\n};\n\n/**\r\n * Get tuition summary for current user\r\n * @returns {Promise} - API response with user tuition summary\r\n */\nexport const getUserTuitionSummaryAPI = async () => {\n  const response = await api.get(\"/v1/user/tuition-summary\");\n  return response;\n};\n\n/**\r\n * Get class tuitions for a specific month for classes that the current user has joined\r\n * @param {string} month - Month in YYYY-MM format\r\n * @returns {Promise} - API response with class tuitions data\r\n */\nexport const getStudentClassTuitionsByMonthAPI = async month => {\n  const response = await api.get(\"/v1/user/class-tuition/\".concat(month));\n  return response;\n};\n\n/**\r\n * Admin API to get class tuitions for a specific month for classes that a student has joined\r\n * @param {string} userId - ID of the student\r\n * @param {string} month - Month in YYYY-MM format\r\n * @returns {Promise} - API response with class tuitions data\r\n */\nexport const getStudentClassTuitionsByMonthAdminAPI = async _ref2 => {\n  let {\n    userId,\n    month\n  } = _ref2;\n  const response = await api.get(\"/v1/admin/user/\".concat(userId, \"/class-tuition/\").concat(month));\n  return response;\n};\nexport const checkTuitionPaymentNotPaidApi = async () => {\n  const response = await api.get(\"/v1/user/tuition-payment/not-paid\");\n  return response;\n};", "map": {"version": 3, "names": ["api", "getAllTuitionPaymentsAPI", "params", "response", "get", "getClassTuitionPaymentsAPI", "classId", "concat", "getUserTuitionPaymentsAdminAPI", "userId", "getUserTuitionPaymentsAPI", "getTuitionPaymentByIdAdminAPI", "id", "getTuitionPaymentByIdUserAPI", "createTuitionPaymentAPI", "paymentData", "post", "createBatchTuitionPaymentsAPI", "batchData", "updateTuitionPaymentAPI", "_ref", "put", "deleteTuitionPaymentAPI", "delete", "getTuitionStatisticsAPI", "getUserTuitionSummaryAdminAPI", "getUserTuitionSummaryAPI", "getStudentClassTuitionsByMonthAPI", "month", "getStudentClassTuitionsByMonthAdminAPI", "_ref2", "checkTuitionPaymentNotPaidApi"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/tuitionApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\n// Tuition Payment APIs\r\n/**\r\n * Get all tuition payment records (admin)\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\r\nexport const getAllTuitionPaymentsAPI = async (params) => {\r\n  const response = await api.get(\"/v1/admin/tuition-payment\", {\r\n    params\r\n  });\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get tuition payment records for a specific class (admin)\r\n * @param {string} classId - ID of the class\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\r\nexport const getClassTuitionPaymentsAPI = async (classId, params) => {\r\n  const response = await api.get(`/v1/admin/class/${classId}/tuition-payment`, {\r\n    params\r\n  });\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get tuition payment records for a specific user (admin)\r\n * @param {string} userId - ID of the user\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\r\nexport const getUserTuitionPaymentsAdminAPI = async (userId, params) => {  // params = { search, currentPage, limit, sortOrder }\r\n  const response = await api.get(`/v1/admin/user/${userId}/tuition-payment`, {\r\n    params\r\n  });\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get tuition payment records for the current user\r\n * @param {Object} params - Query parameters (search, currentPage, limit, sortOrder)\r\n * @returns {Promise} - API response\r\n */\r\nexport const getUserTuitionPaymentsAPI = async (params) => {\r\n  const response = await api.get(\"/v1/user/tuition-payment\", {\r\n    params\r\n  });\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get a specific tuition payment record by ID (admin)\r\n * @param {string} id - ID of the payment record\r\n * @returns {Promise} - API response\r\n */\r\nexport const getTuitionPaymentByIdAdminAPI = async (id) => {\r\n  const response = await api.get(`/v1/admin/tuition-payment/${id}`);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get a specific tuition payment record by ID (user)\r\n * @param {string} id - ID of the payment record\r\n * @returns {Promise} - API response\r\n */\r\nexport const getTuitionPaymentByIdUserAPI = async (id) => {\r\n  const response = await api.get(`/v1/user/tuition-payment/${id}`);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Create a new tuition payment record\r\n * @param {Object} paymentData - Payment data to create\r\n * @returns {Promise} - API response\r\n */\r\nexport const createTuitionPaymentAPI = async (paymentData) => {\r\n  const response = await api.post(\"/v1/admin/tuition-payment\", paymentData);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Create multiple tuition payment records in batch\r\n * @param {Object} batchData - Batch payment data\r\n * @returns {Promise} - API response\r\n */\r\nexport const createBatchTuitionPaymentsAPI = async (batchData) => {\r\n  const response = await api.post(\"/v1/admin/tuition-payment/batch\", batchData);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Update a tuition payment record\r\n * @param {string} id - ID of the payment record to update\r\n * @param {Object} paymentData - Updated payment data\r\n * @returns {Promise} - API response\r\n */\r\nexport const updateTuitionPaymentAPI = async ({ id, paymentData }) => {\r\n  const response = await api.put(`/v1/admin/tuition-payment/${id}`, paymentData);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Delete a tuition payment record\r\n * @param {string} id - ID of the payment record to delete\r\n * @returns {Promise} - API response\r\n */\r\nexport const deleteTuitionPaymentAPI = async (id) => {\r\n  const response = await api.delete(`/v1/admin/tuition-payment/${id}`);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get tuition payment statistics\r\n * @param {Object} params - Query parameters (startMonth, endMonth, userClass)\r\n * @returns {Promise} - API response with statistics data\r\n */\r\nexport const getTuitionStatisticsAPI = async (params) => {\r\n  const response = await api.get(\"/v1/admin/tuition-payment/statistics\", {\r\n    params\r\n  });\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get tuition summary for a user (admin view)\r\n * @param {string} userId - User ID\r\n * @returns {Promise} - API response with user tuition summary\r\n */\r\nexport const getUserTuitionSummaryAdminAPI = async (userId) => {\r\n  const response = await api.get(`/v1/admin/user/${userId}/tuition-summary`);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get tuition summary for current user\r\n * @returns {Promise} - API response with user tuition summary\r\n */\r\nexport const getUserTuitionSummaryAPI = async () => {\r\n  const response = await api.get(\"/v1/user/tuition-summary\");\r\n  return response;\r\n};\r\n\r\n/**\r\n * Get class tuitions for a specific month for classes that the current user has joined\r\n * @param {string} month - Month in YYYY-MM format\r\n * @returns {Promise} - API response with class tuitions data\r\n */\r\nexport const getStudentClassTuitionsByMonthAPI = async (month) => {\r\n  const response = await api.get(`/v1/user/class-tuition/${month}`);\r\n  return response;\r\n};\r\n\r\n/**\r\n * Admin API to get class tuitions for a specific month for classes that a student has joined\r\n * @param {string} userId - ID of the student\r\n * @param {string} month - Month in YYYY-MM format\r\n * @returns {Promise} - API response with class tuitions data\r\n */\r\nexport const getStudentClassTuitionsByMonthAdminAPI = async ({ userId, month }) => {\r\n  const response = await api.get(`/v1/admin/user/${userId}/class-tuition/${month}`);\r\n  return response;\r\n};\r\n\r\nexport const checkTuitionPaymentNotPaidApi = async () => {\r\n  const response = await api.get(\"/v1/user/tuition-payment/not-paid\");\r\n  return response;\r\n};"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAG,MAAOC,MAAM,IAAK;EACxD,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,2BAA2B,EAAE;IAC1DF;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,0BAA0B,GAAG,MAAAA,CAAOC,OAAO,EAAEJ,MAAM,KAAK;EACnE,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,oBAAAG,MAAA,CAAoBD,OAAO,uBAAoB;IAC3EJ;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,8BAA8B,GAAG,MAAAA,CAAOC,MAAM,EAAEP,MAAM,KAAK;EAAG;EACzE,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,mBAAAG,MAAA,CAAmBE,MAAM,uBAAoB;IACzEP;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,yBAAyB,GAAG,MAAOR,MAAM,IAAK;EACzD,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,0BAA0B,EAAE;IACzDF;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,6BAA6B,GAAG,MAAOC,EAAE,IAAK;EACzD,MAAMT,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,8BAAAG,MAAA,CAA8BK,EAAE,CAAE,CAAC;EACjE,OAAOT,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,4BAA4B,GAAG,MAAOD,EAAE,IAAK;EACxD,MAAMT,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,6BAAAG,MAAA,CAA6BK,EAAE,CAAE,CAAC;EAChE,OAAOT,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,uBAAuB,GAAG,MAAOC,WAAW,IAAK;EAC5D,MAAMZ,QAAQ,GAAG,MAAMH,GAAG,CAACgB,IAAI,CAAC,2BAA2B,EAAED,WAAW,CAAC;EACzE,OAAOZ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,6BAA6B,GAAG,MAAOC,SAAS,IAAK;EAChE,MAAMf,QAAQ,GAAG,MAAMH,GAAG,CAACgB,IAAI,CAAC,iCAAiC,EAAEE,SAAS,CAAC;EAC7E,OAAOf,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,uBAAuB,GAAG,MAAAC,IAAA,IAA+B;EAAA,IAAxB;IAAER,EAAE;IAAEG;EAAY,CAAC,GAAAK,IAAA;EAC/D,MAAMjB,QAAQ,GAAG,MAAMH,GAAG,CAACqB,GAAG,8BAAAd,MAAA,CAA8BK,EAAE,GAAIG,WAAW,CAAC;EAC9E,OAAOZ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,uBAAuB,GAAG,MAAOV,EAAE,IAAK;EACnD,MAAMT,QAAQ,GAAG,MAAMH,GAAG,CAACuB,MAAM,8BAAAhB,MAAA,CAA8BK,EAAE,CAAE,CAAC;EACpE,OAAOT,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,uBAAuB,GAAG,MAAOtB,MAAM,IAAK;EACvD,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,sCAAsC,EAAE;IACrEF;EACF,CAAC,CAAC;EACF,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,6BAA6B,GAAG,MAAOhB,MAAM,IAAK;EAC7D,MAAMN,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,mBAAAG,MAAA,CAAmBE,MAAM,qBAAkB,CAAC;EAC1E,OAAON,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMuB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;EAClD,MAAMvB,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,0BAA0B,CAAC;EAC1D,OAAOD,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwB,iCAAiC,GAAG,MAAOC,KAAK,IAAK;EAChE,MAAMzB,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,2BAAAG,MAAA,CAA2BqB,KAAK,CAAE,CAAC;EACjE,OAAOzB,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,sCAAsC,GAAG,MAAAC,KAAA,IAA6B;EAAA,IAAtB;IAAErB,MAAM;IAAEmB;EAAM,CAAC,GAAAE,KAAA;EAC5E,MAAM3B,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,mBAAAG,MAAA,CAAmBE,MAAM,qBAAAF,MAAA,CAAkBqB,KAAK,CAAE,CAAC;EACjF,OAAOzB,QAAQ;AACjB,CAAC;AAED,OAAO,MAAM4B,6BAA6B,GAAG,MAAAA,CAAA,KAAY;EACvD,MAAM5B,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,mCAAmC,CAAC;EACnE,OAAOD,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}