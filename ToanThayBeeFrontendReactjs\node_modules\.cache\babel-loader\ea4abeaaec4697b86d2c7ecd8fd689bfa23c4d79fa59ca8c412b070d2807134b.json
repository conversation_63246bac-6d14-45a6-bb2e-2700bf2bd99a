{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\input\\\\InputSearch.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InputSearch = _ref => {\n  _s();\n  let {\n    onDebouncedChange,\n    placeholder = \"Nhập id câu hỏi\",\n    className\n  } = _ref;\n  const [value, setValue] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    setLoading(true);\n    const handler = setTimeout(() => {\n      onDebouncedChange === null || onDebouncedChange === void 0 ? void 0 : onDebouncedChange(value);\n      setLoading(false);\n    }, 1000);\n    return () => clearTimeout(handler);\n  }, [value, onDebouncedChange]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative w-full h-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      value: value,\n      onChange: e => setValue(e.target.value),\n      placeholder: placeholder,\n      className: \"w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this), !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-y-0 left-3 flex items-center pointer-events-none\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"w-5 h-5 text-gray-400\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M19.6 21L13.3 14.7C12.8 15.1 12.225 15.4167 11.575 15.65C10.925 15.8833 10.2333 16 9.5 16C7.68333 16 6.146 15.3707 4.888 14.112C3.63 12.8533 3.00067 11.316 3 9.5C2.99933 7.684 3.62867 6.14667 4.888 4.888C6.14733 3.62933 7.68467 3 9.5 3C11.3153 3 12.853 3.62933 14.113 4.888C15.373 6.14667 16.002 7.684 16 9.5C16 10.2333 15.8833 10.925 15.65 11.575C15.4167 12.225 15.1 12.8 14.7 13.3L21 19.6L19.6 21ZM9.5 14C10.75 14 11.8127 13.5627 12.688 12.688C13.5633 11.8133 14.0007 10.7507 14 9.5C13.9993 8.24933 13.562 7.187 12.688 6.313C11.814 5.439 10.7513 5.00133 9.5 5C8.24867 4.99867 7.18633 5.43633 6.313 6.313C5.43967 7.18967 5.002 8.252 5 9.5C4.998 10.748 5.43567 11.8107 6.313 12.688C7.19033 13.5653 8.25267 14.0027 9.5 14Z\",\n          fill: \"#9CA3AF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-y-0 left-3 flex items-center\",\n      children: loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"1.25rem\",\n        thickness: \"border-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n};\n_s(InputSearch, \"6+7MjrhH+XZTDnKJtkyyHM6Zx5s=\");\n_c = InputSearch;\nexport default InputSearch;\nvar _c;\n$RefreshReg$(_c, \"InputSearch\");", "map": {"version": 3, "names": ["useEffect", "useState", "LoadingSpinner", "jsxDEV", "_jsxDEV", "InputSearch", "_ref", "_s", "onDebouncedChange", "placeholder", "className", "value", "setValue", "loading", "setLoading", "handler", "setTimeout", "clearTimeout", "children", "type", "onChange", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xmlns", "viewBox", "fill", "d", "size", "thickness", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/input/InputSearch.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport LoadingSpinner from \"../loading/LoadingSpinner\";\r\n\r\nconst InputSearch = ({ onDebouncedChange, placeholder = \"Nhập id câu hỏi\", className }) => {\r\n    const [value, setValue] = useState(\"\");\r\n    const [loading, setLoading] = useState(false);\r\n\r\n    useEffect(() => {\r\n        setLoading(true);\r\n        const handler = setTimeout(() => {\r\n            onDebouncedChange?.(value);\r\n            setLoading(false);\r\n        }, 1000);\r\n\r\n        return () => clearTimeout(handler);\r\n    }, [value, onDebouncedChange]);\r\n\r\n    return (\r\n\r\n        <div className=\"relative w-full h-full\">\r\n            <input\r\n                type=\"text\"\r\n                value={value}\r\n                onChange={(e) => setValue(e.target.value)}\r\n                placeholder={placeholder}\r\n                className=\"w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150\"\r\n            />\r\n\r\n            {!loading && (\r\n                <div className=\"absolute inset-y-0 left-3 flex items-center pointer-events-none\">\r\n                    <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        className=\"w-5 h-5 text-gray-400\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                    >\r\n                        <path\r\n                            d=\"M19.6 21L13.3 14.7C12.8 15.1 12.225 15.4167 11.575 15.65C10.925 15.8833 10.2333 16 9.5 16C7.68333 16 6.146 15.3707 4.888 14.112C3.63 12.8533 3.00067 11.316 3 9.5C2.99933 7.684 3.62867 6.14667 4.888 4.888C6.14733 3.62933 7.68467 3 9.5 3C11.3153 3 12.853 3.62933 14.113 4.888C15.373 6.14667 16.002 7.684 16 9.5C16 10.2333 15.8833 10.925 15.65 11.575C15.4167 12.225 15.1 12.8 14.7 13.3L21 19.6L19.6 21ZM9.5 14C10.75 14 11.8127 13.5627 12.688 12.688C13.5633 11.8133 14.0007 10.7507 14 9.5C13.9993 8.24933 13.562 7.187 12.688 6.313C11.814 5.439 10.7513 5.00133 9.5 5C8.24867 4.99867 7.18633 5.43633 6.313 6.313C5.43967 7.18967 5.002 8.252 5 9.5C4.998 10.748 5.43567 11.8107 6.313 12.688C7.19033 13.5653 8.25267 14.0027 9.5 14Z\"\r\n                            fill=\"#9CA3AF\"\r\n                        />\r\n                    </svg>\r\n                </div>\r\n            )}\r\n\r\n            {/* Loading spinner (phải) */}\r\n            <div className=\"absolute inset-y-0 left-3 flex items-center\">\r\n                {loading && (\r\n                    <LoadingSpinner\r\n                        size=\"1.25rem\"\r\n                        thickness=\"border-2\"\r\n                    />\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default InputSearch;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,cAAc,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGC,IAAA,IAAuE;EAAAC,EAAA;EAAA,IAAtE;IAAEC,iBAAiB;IAAEC,WAAW,GAAG,iBAAiB;IAAEC;EAAU,CAAC,GAAAJ,IAAA;EAClF,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE7CD,SAAS,CAAC,MAAM;IACZc,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMC,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC7BR,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGG,KAAK,CAAC;MAC1BG,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,YAAY,CAACF,OAAO,CAAC;EACtC,CAAC,EAAE,CAACJ,KAAK,EAAEH,iBAAiB,CAAC,CAAC;EAE9B,oBAEIJ,OAAA;IAAKM,SAAS,EAAC,wBAAwB;IAAAQ,QAAA,gBACnCd,OAAA;MACIe,IAAI,EAAC,MAAM;MACXR,KAAK,EAAEA,KAAM;MACbS,QAAQ,EAAGC,CAAC,IAAKT,QAAQ,CAACS,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;MAC1CF,WAAW,EAAEA,WAAY;MACzBC,SAAS,EAAC;IAAsN;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnO,CAAC,EAED,CAACb,OAAO,iBACLT,OAAA;MAAKM,SAAS,EAAC,iEAAiE;MAAAQ,QAAA,eAC5Ed,OAAA;QACIuB,KAAK,EAAC,4BAA4B;QAClCjB,SAAS,EAAC,uBAAuB;QACjCkB,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,MAAM;QAAAX,QAAA,eAEXd,OAAA;UACI0B,CAAC,EAAC,mtBAAmtB;UACrtBD,IAAI,EAAC;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAGDtB,OAAA;MAAKM,SAAS,EAAC,6CAA6C;MAAAQ,QAAA,EACvDL,OAAO,iBACJT,OAAA,CAACF,cAAc;QACX6B,IAAI,EAAC,SAAS;QACdC,SAAS,EAAC;MAAU;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IACJ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnB,EAAA,CApDIF,WAAW;AAAA4B,EAAA,GAAX5B,WAAW;AAsDjB,eAAeA,WAAW;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}