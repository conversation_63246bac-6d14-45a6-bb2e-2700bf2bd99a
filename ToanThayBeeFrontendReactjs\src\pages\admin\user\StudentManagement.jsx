import AdminLayout from "../../../layouts/AdminLayout";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import UserList from "../../../components/table/userTable";
import AddStudentModal from "../../../components/modal/AddStudentModal";
import { useDispatch, useSelector } from "react-redux";
import { findClasses } from "src/features/class/classSlice";
import { useEffect } from "react";
import { fetchUsersWithoutPagination } from "src/features/user/userSlice";
import { exportStudentsToExcel } from "src/utils/excelExport";
import { setCurrentPage, setLimit, setSearch } from "src/features/user/userSlice";

const StudentManagement = () => {
    const { isAddView } = useSelector((state) => state.filter);
    const dispatch = useDispatch();
    const { pagination, graduationYearFilter, classFilter } = useSelector((state) => state.users);

    const handleExportToExcel = () => {
        try {
            // Fetch all students with current filters, no search, limit 1000
            dispatch(fetchUsersWithoutPagination({
                graduationYear: graduationYearFilter,
                classFilter
            }))
                .unwrap()
                .then((result) => {
                    if (result && result.data) {
                        // Export to Excel with filter info

                        const filters = {
                            graduationYear: graduationYearFilter,
                            classFilter: classFilter
                        };
                        exportStudentsToExcel(result.data.data, filters);
                    }
                })
                .catch((error) => {
                    console.error('Error fetching students for export:', error);
                });
        } catch (error) {
            console.error('Error exporting to Excel:', error);
        }
    };


    useEffect(() => {
        dispatch(findClasses(""));
    }, [dispatch]);

    return (
        <AdminLayout>
            <>
                <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9">
                    Danh sách học sinh
                </div>
                <FunctionBarAdmin
                    showExportExcel={true}
                    showFilter={true}
                    classFilter={classFilter}
                    currentPage={pagination.page}
                    totalItems={pagination.total}
                    totalPages={pagination.totalPages}
                    limit={pagination.pageSize}
                    setLimit={(newLimit) => dispatch(setLimit(newLimit))}
                    setCurrentPage={(newPage) => dispatch(setCurrentPage(newPage))}
                    setSearch={(value) => dispatch(setSearch(value))}
                    handleExportToExcel={handleExportToExcel}
                />
                <UserList />

                {/* Modal thêm học sinh */}
                {isAddView && <AddStudentModal />}
            </>
        </AdminLayout>
    );
}

export default StudentManagement;