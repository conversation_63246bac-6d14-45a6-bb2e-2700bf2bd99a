import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import UserType from '../constants/UserType.js'
import * as NotificationExampleController from '../controllers/NotificationExampleController.js'

const router = express.Router()

// Example routes for sending notifications (admin only)
router.post('/v1/admin/notifications/send-to-user',
    requireRoles([UserType.ADMIN]),
    async<PERSON>andler(NotificationExampleController.sendNotificationToUserExample)
)

router.post('/v1/admin/notifications/send-to-class',
    requireRoles([UserType.ADMIN]),
    async<PERSON>and<PERSON>(NotificationExampleController.sendNotificationToClassExample)
)

router.post('/v1/admin/notifications/send-to-exam',
    requireRoles([UserType.ADMIN]),
    as<PERSON><PERSON><PERSON><PERSON>(NotificationExampleController.sendNotificationToExamExample)
)

export default router
